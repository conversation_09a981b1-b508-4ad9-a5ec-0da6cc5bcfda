"use client";

import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { useCertificates } from "@/hooks/use-certificates";
import { useAuth } from "@/hooks/use-auth";
import { useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";

export type CitizenStats = {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  delivered: number;
  expired: number;
  draft: number;
  trends: {
    daily: {
      total: number;
      percentage: number;
    };
    weekly: {
      total: number;
      percentage: number;
    };
    monthly: {
      total: number;
      percentage: number;
    };
  };
};

export function useCitizenStats() {
  const { user } = useAuth();
  const router = useRouter();
  const { certificates, isLoading, error } = useCertificates();

  // Redirection si l'utilisateur n'est pas connecté ou n'est pas un citoyen
  useEffect(() => {
    if (!isLoading && (!user || user.prefs?.role !== "citizen")) {
      router.push("/dashboard");
    }
  }, [user, isLoading, router]);

  const stats = useMemo(() => {
    if (!certificates || !user) {
      return null;
    }

    // Filtrer les certificats du citoyen
    const userCertificates = certificates.filter(
      (cert) => cert.citizenId === user.$id
    );

    // Dates pour les tendances
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Calculer les statistiques
    const total = userCertificates.length;
    const pending = userCertificates.filter(
      (cert) =>
        cert.status === CERTIFICATE_STATUS.PENDING ||
        cert.status === CERTIFICATE_STATUS.SUBMITTED
    ).length;
    const approved = userCertificates.filter(
      (cert) =>
        cert.status === CERTIFICATE_STATUS.APPROVED ||
        cert.status === CERTIFICATE_STATUS.VERIFIED
    ).length;
    const rejected = userCertificates.filter(
      (cert) => cert.status === CERTIFICATE_STATUS.REJECTED
    ).length;
    const delivered = userCertificates.filter(
      (cert) => cert.status === CERTIFICATE_STATUS.DELIVERED
    ).length;
    const expired = userCertificates.filter(
      (cert) => cert.status === CERTIFICATE_STATUS.EXPIRED
    ).length;
    const draft = userCertificates.filter(
      (cert) => cert.status === CERTIFICATE_STATUS.DRAFT
    ).length;

    // Calculer les tendances
    const dailyStats = userCertificates.filter(
      (cert) => new Date(cert.$createdAt) >= oneDayAgo
    ).length;
    const weeklyStats = userCertificates.filter(
      (cert) => new Date(cert.$createdAt) >= oneWeekAgo
    ).length;
    const monthlyStats = userCertificates.filter(
      (cert) => new Date(cert.$createdAt) >= oneMonthAgo
    ).length;

    // Calculer les pourcentages de variation
    const calculatePercentage = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    const previousDayStats = userCertificates.filter(
      (cert) =>
        new Date(cert.$createdAt) >=
          new Date(oneDayAgo.getTime() - 24 * 60 * 60 * 1000) &&
        new Date(cert.$createdAt) < oneDayAgo
    ).length;

    const previousWeekStats = userCertificates.filter(
      (cert) =>
        new Date(cert.$createdAt) >=
          new Date(oneWeekAgo.getTime() - 7 * 24 * 60 * 60 * 1000) &&
        new Date(cert.$createdAt) < oneWeekAgo
    ).length;

    const previousMonthStats = userCertificates.filter(
      (cert) =>
        new Date(cert.$createdAt) >=
          new Date(oneMonthAgo.getTime() - 30 * 24 * 60 * 60 * 1000) &&
        new Date(cert.$createdAt) < oneMonthAgo
    ).length;

    return {
      total,
      pending,
      approved,
      rejected,
      delivered,
      expired,
      draft,
      trends: {
        daily: {
          total: dailyStats,
          percentage: calculatePercentage(dailyStats, previousDayStats),
        },
        weekly: {
          total: weeklyStats,
          percentage: calculatePercentage(weeklyStats, previousWeekStats),
        },
        monthly: {
          total: monthlyStats,
          percentage: calculatePercentage(monthlyStats, previousMonthStats),
        },
      },
    };
  }, [certificates, user]);

  return {
    stats,
    isLoading,
    error,
  };
}
