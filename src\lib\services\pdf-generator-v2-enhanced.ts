import { getCertificateWithFullData } from "@/actions/certificates";
import { Certificate } from "@/actions/types";
import { CertificateVerificationService } from "@/lib/database/certificate-verification";
import { createAdminClient } from "@/lib/server/appwrite";
import { SIGNATURES_BUCKET_ID } from "@/lib/server/storage";
import {
  CertificateCrypto,
  CertificateHashData,
} from "@/lib/utils/certificate-crypto";
import { QRCodeGenerator } from "@/lib/utils/qr-generator";
import { jsPDF } from "jspdf";

const COLORS_V2 = {
  RED: "#CE1126",
  YELLOW: "#FCD116",
  GREEN: "#009639",
  DARK_GREEN: "#006B2F",
  LIGHT_GRAY: "#F8F9FA",
  DARK_GRAY: "#343A40",
  BLACK: "#000000",
  WHITE: "#FFFFFF",
};

const DESIGN_V2 = {
  PAGE_MARGIN: 10,
  BORDER_WIDTH: 2,
  HEADER_HEIGHT: 65,
  FOOTER_HEIGHT: 45,
  CONTENT_PADDING: 12,
  SECURITY_OPACITY: 0.1,
  QR_SIZE: 20,
  QR_MARGIN: 3,
};

export class PdfGeneratorV2Enhanced {
  private static baseUrl = process.env.NEXT_PUBLIC_APP_URL!;
  private static brandingBase64: string;
  private static armoirieBase64: string;
  private static simandouBase64: string;

  private static async loadImage(url: string): Promise<string> {
    try {
      // Add timeout to prevent hanging
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          "Cache-Control": "no-cache",
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const base64 = Buffer.from(arrayBuffer).toString("base64");
      return base64;
    } catch (error) {
      console.error(`Erreur lors du chargement de l'image ${url}:`, error);

      // Return a fallback empty base64 image instead of empty string
      // This prevents PDF generation from failing completely
      return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
    }
  }

  static async generateCertificatePdf(certificateId: string): Promise<Buffer> {
    try {
      console.log(`Starting PDF generation for certificate: ${certificateId}`);

      const { certificate } = await getCertificateWithFullData(certificateId);
      const { storage } = await createAdminClient();

      if (!certificate) {
        throw new Error(`Certificate not found: ${certificateId}`);
      }

      // Debug logging to verify data availability
      console.log("=== PDF Generator V2 Enhanced Debug ===");
      console.log("Certificate ID:", certificateId);
      console.log("Certificate Reference:", certificate.reference);
      console.log("Citizen Data:", {
        name: certificate.citizenName,
        numeroIdentificationUnique:
          certificate.citizen?.numeroIdentificationUnique,
        profession: certificate.citizen?.profession,
        dateNaissance: certificate.citizen?.dateNaissance,
        lieuNaissance: certificate.citizen?.lieuNaissance,
        nomPere: certificate.citizen?.nomPere,
        nomMere: certificate.citizen?.nomMere,
        adressePrecise: certificate.citizen?.adressePrecise,
        dateInstallation: certificate.citizen?.dateInstallation,
        carteElecteur: certificate.citizen?.carteElecteur,
      });
      console.log("Quartier Data:", {
        nom: certificate.quartier?.nom,
        commune: certificate.quartier?.commune,
        region: certificate.quartier?.region,
      });
      console.log("Chef Data:", {
        name: certificate.chefQuartierName,
      });
      console.log("Certificate Motif:", certificate.motif);
      console.log("======================================");

      // Chargement des images avec timeout global
      console.log("Loading images for PDF generation...");

      const imageLoadingPromises = [];

      if (!this.armoirieBase64) {
        imageLoadingPromises.push(
          this.loadImage(`${this.baseUrl}/images/armoirie.png`).then(
            (base64) => {
              this.armoirieBase64 = base64;
            }
          )
        );
      }

      if (!this.brandingBase64) {
        imageLoadingPromises.push(
          this.loadImage(`${this.baseUrl}/images/branding.png`).then(
            (base64) => {
              this.brandingBase64 = base64;
            }
          )
        );
      }

      if (!this.simandouBase64) {
        imageLoadingPromises.push(
          this.loadImage(`${this.baseUrl}/images/simandou.png`).then(
            (base64) => {
              this.simandouBase64 = base64;
            }
          )
        );
      }

      // Load all images in parallel with overall timeout
      if (imageLoadingPromises.length > 0) {
        await Promise.race([
          Promise.all(imageLoadingPromises),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error("Image loading timeout")), 30000)
          ),
        ]);
      }

      console.log("Images loaded successfully");

      // Génération des données de sécurité
      const securityData = await this.generateSecurityData(certificate);

      // Création du document PDF
      const doc = new jsPDF({
        orientation: "portrait",
        unit: "mm",
        format: "a4",
      });

      // Construction du document moderne avec sécurité
      this.addModernBorder(doc);
      this.addModernHeader(doc, certificate);
      this.addSecurityElements(doc, certificate, securityData);
      this.addModernContent(doc, certificate, securityData);
      await this.addQRCode(doc, securityData);
      this.addModernFooter(doc, certificate);

      // Ajout de la signature si disponible
      if (certificate.signatureFileId) {
        try {
          const signatureFile = await storage.getFileDownload(
            SIGNATURES_BUCKET_ID,
            certificate.signatureFileId
          );
          await this.addModernSignature(doc, signatureFile, certificate);
        } catch (error) {
          console.error(
            "Erreur lors de la récupération de la signature:",
            error
          );
        }
      }

      console.log("PDF generation completed successfully");
      return Buffer.from(doc.output("arraybuffer"));
    } catch (error) {
      console.error("Error during PDF generation:", error);

      // Provide detailed error information
      if (error instanceof Error) {
        throw new Error(`PDF Generation Failed: ${error.message}`);
      } else {
        throw new Error("PDF Generation Failed: Unknown error occurred");
      }
    }
  }

  private static async generateSecurityData(certificate: Certificate) {
    // Préparation des données pour le hash
    const hashData: CertificateHashData = {
      certificateId: certificate.$id,
      citizenId: certificate.citizenId,
      citizenIdUnique: certificate.citizen.numeroIdentificationUnique || "",
      timestamp: Date.now(),
      reference: certificate.reference,
      issuerInfo: {
        type: "chef",
        id: certificate.chefId,
        name: certificate.chefQuartierName || "",
      },
      locationInfo: {
        region: certificate.quartier?.region || "",
        commune: certificate.quartier?.commune || "",
        quartier: certificate.quartier?.nom || "",
      },
    };

    // Génération des données de sécurité
    const security = CertificateCrypto.generateCertificateSecurity(hashData);

    // Création de l'enregistrement de vérification en base avec date d'expiration précise
    try {
      const issuedAt = certificate.deliveredAt
        ? new Date(certificate.deliveredAt)
        : new Date();

      // Calcul précis de la date d'expiration basé sur certificate.expiresAt
      let expiresAt: Date;

      if (certificate.expiresAt) {
        // Utiliser la date d'expiration du certificat si elle existe
        expiresAt = new Date(certificate.expiresAt);
        console.log(`Using certificate expiresAt: ${expiresAt.toISOString()}`);
      } else {
        // Fallback: calculer 3 mois à partir de la date d'émission
        expiresAt = new Date(issuedAt);
        expiresAt.setMonth(expiresAt.getMonth() + 3);
        console.log(
          `Calculated fallback expiresAt: ${expiresAt.toISOString()}`
        );
      }

      // Validation de la date d'expiration
      if (expiresAt <= issuedAt) {
        console.warn(
          "Date d'expiration invalide, utilisation du fallback de 3 mois"
        );
        expiresAt = new Date(issuedAt);
        expiresAt.setMonth(expiresAt.getMonth() + 3);
      }

      console.log(
        `Final security data - Issued: ${issuedAt.toISOString()}, Expires: ${expiresAt.toISOString()}`
      );

      await CertificateVerificationService.createVerification({
        hash: security.verificationHash,
        certificateId: certificate.$id,
        citizenId: certificate.citizenId,
        issuedAt,
        expiresAt,
        metadata: {
          issuerType: hashData.issuerInfo.type,
          issuerId: hashData.issuerInfo.id,
          issuerName: hashData.issuerInfo.name,
          region: hashData.locationInfo.region,
          commune: hashData.locationInfo.commune,
          quartier: hashData.locationInfo.quartier,
        },
      });
    } catch (error) {
      console.error(
        "Erreur lors de la création de l'enregistrement de vérification:",
        error
      );
    }

    return security;
  }

  private static addModernBorder(doc: jsPDF) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = DESIGN_V2.PAGE_MARGIN;
    const borderWidth = DESIGN_V2.BORDER_WIDTH;

    // Bordure principale verte
    doc.setDrawColor(COLORS_V2.GREEN);
    doc.setLineWidth(borderWidth);
    doc.rect(margin, margin, pageWidth - 2 * margin, pageHeight - 2 * margin);

    // Bande tricolore en haut
    const bandHeight = 8;
    const bandWidth = pageWidth - 2 * margin - 2 * borderWidth;
    const bandX = margin + borderWidth;
    const bandY = margin + borderWidth;

    // Rouge
    doc.setFillColor(COLORS_V2.RED);
    doc.rect(bandX, bandY, bandWidth / 3, bandHeight, "F");

    // Jaune
    doc.setFillColor(COLORS_V2.YELLOW);
    doc.rect(bandX + bandWidth / 3, bandY, bandWidth / 3, bandHeight, "F");

    // Vert
    doc.setFillColor(COLORS_V2.GREEN);
    doc.rect(
      bandX + (2 * bandWidth) / 3,
      bandY,
      bandWidth / 3,
      bandHeight,
      "F"
    );

    // Bande tricolore en bas
    const bottomBandY = pageHeight - margin - borderWidth - bandHeight;

    // Rouge
    doc.setFillColor(COLORS_V2.RED);
    doc.rect(bandX, bottomBandY, bandWidth / 3, bandHeight, "F");

    // Jaune
    doc.setFillColor(COLORS_V2.YELLOW);
    doc.rect(
      bandX + bandWidth / 3,
      bottomBandY,
      bandWidth / 3,
      bandHeight,
      "F"
    );

    // Vert
    doc.setFillColor(COLORS_V2.GREEN);
    doc.rect(
      bandX + (2 * bandWidth) / 3,
      bottomBandY,
      bandWidth / 3,
      bandHeight,
      "F"
    );
  }

  private static addModernHeader(doc: jsPDF, certificate: Certificate) {
    const pageWidth = doc.internal.pageSize.width;
    const headerY = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH + 10;
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
    let currentY = headerY;

    // SECTION GAUCHE: Ministère (aligné avec le contenu et QR code)
    doc.setFontSize(9);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GRAY);

    // Aligné avec la marge gauche du contenu principal et QR code
    const leftX = margin + DESIGN_V2.QR_MARGIN;
    // Positionné verticalement pour s'aligner avec "Réf: NCR-..." (ligne 30)
    const ministereY = 30; // Même niveau que "Réf:"
    doc.text("MINISTÈRE DE L'ADMINISTRATION", leftX, ministereY);
    doc.text("DU TERRITOIRE ET DE LA", leftX, ministereY + 4);
    doc.text("DÉCENTRALISATION", leftX, ministereY + 8);

    // SECTION CENTRE: Armoirie et titres officiels
    // Armoirie au centre
    if (this.armoirieBase64) {
      const logoSize = 20;
      doc.addImage(
        `data:image/png;base64,${this.armoirieBase64}`,
        "PNG",
        pageWidth / 2 - logoSize / 2,
        currentY,
        logoSize,
        logoSize
      );
    }
    currentY += 25;

    // République de Guinée (centré)
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.BLACK);
    doc.text("RÉPUBLIQUE DE GUINÉE", pageWidth / 2, currentY, {
      align: "center",
    });
    currentY += 6;

    // Devise nationale (centré, sous République de Guinée)
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
    doc.setTextColor(COLORS_V2.DARK_GRAY);
    doc.text("Travail - Justice - Solidarité", pageWidth / 2, currentY, {
      align: "center",
    });
    currentY += 15;

    // Titre principal du certificat
    doc.setFontSize(22);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text("CERTIFICAT", pageWidth / 2, currentY, { align: "center" });
    currentY += 8;

    doc.setFontSize(20);
    doc.text("DE RÉSIDENCE", pageWidth / 2, currentY, { align: "center" });
    currentY += 12;

    // Numéro de certificat (centré sous le titre)
    doc.setFontSize(12);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text(`N° ${certificate.reference}`, pageWidth / 2, currentY, {
      align: "center",
    });
    // Ligne horizontale supprimée pour un design plus épuré
  }

  private static addSecurityElements(
    doc: jsPDF,
    certificate: Certificate,
    securityData: any
  ) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;

    // Filigrane de sécurité - Watermark dynamique en arrière-plan
    doc.saveGraphicsState();
    doc.setGState(doc.GState({ opacity: DESIGN_V2.SECURITY_OPACITY }));
    doc.setFontSize(35);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.GREEN);

    // Watermark de sécurité positionné
    const watermarkX = pageWidth / 2 + 12;
    const watermarkY = pageHeight / 2 + 35;

    doc.text(securityData.watermark, watermarkX, watermarkY, {
      align: "center",
      angle: 45,
    });

    // Filigrane de branding avec logo (comme version 1)
    if (this.brandingBase64) {
      const watermarkSize = 100; // Taille adaptée pour V2
      doc.setGState(doc.GState({ opacity: 0.15 })); // Opacité optimisée

      // Centré parfaitement sur la page
      const centerX = pageWidth / 2;
      const centerY = pageHeight / 2;

      // Ajout du logo avec rotation légère
      doc.addImage(
        `data:image/png;base64,${this.brandingBase64}`,
        "PNG",
        centerX - watermarkSize / 2,
        centerY - watermarkSize / 2,
        watermarkSize,
        watermarkSize,
        undefined,
        undefined,
        15 // Rotation légère de 15 degrés
      );

      console.log(
        `Branding logo watermark added at center: ${centerX}, ${centerY}`
      );
    }

    doc.restoreGraphicsState();

    // Éléments de sécurité visibles
    this.addVerificationElements(doc, certificate, securityData);
  }

  private static addVerificationElements(
    doc: jsPDF,
    certificate: Certificate,
    securityData: any
  ) {
    // Référence du certificat alignée avec le cadre de signature et badge
    const pageWidth = doc.internal.pageSize.width;
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
    const signatureBoxWidth = 47.5;
    const alignmentX =
      pageWidth - margin - DESIGN_V2.BORDER_WIDTH - signatureBoxWidth;

    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
    doc.setTextColor(COLORS_V2.DARK_GRAY);
    doc.text(
      `Réf: ${certificate.reference}`,
      alignmentX + signatureBoxWidth,
      30,
      {
        align: "right",
      }
    );

    // Hash de vérification aligné avec espacement optimisé
    doc.setFontSize(8);
    doc.text(
      `Hash: ${securityData.displayHash}`,
      alignmentX + signatureBoxWidth,
      36, // Espacement légèrement augmenté
      {
        align: "right",
      }
    );

    // Horodatage cryptographique aligné avec espacement optimisé
    doc.text(
      `TS: ${securityData.timestamp.timestampHash}`,
      alignmentX + signatureBoxWidth,
      42, // Espacement légèrement augmenté
      { align: "right" }
    );

    // Date d'expiration précise basée sur certificate.expiresAt
    if (certificate.expiresAt) {
      const expirationDate = new Date(certificate.expiresAt);
      const formattedExpiration = expirationDate.toLocaleDateString("fr-FR");

      doc.setFontSize(7);
      doc.setTextColor(COLORS_V2.DARK_GRAY);
      doc.text(
        `Exp: ${formattedExpiration}`,
        alignmentX + signatureBoxWidth,
        48, // Position sous le timestamp
        { align: "right" }
      );

      console.log(`Certificate expiration displayed: ${formattedExpiration}`);
    }
  }

  private static addModernContent(
    doc: jsPDF,
    certificate: Certificate,
    _securityData: any
  ) {
    let currentY = 95; // Position après l'en-tête optimisée

    // ID Citoyen mis en valeur
    this.addCitizenIdSection(doc, certificate, currentY);
    currentY += 25; // Adjusted spacing for additional header content

    // Toutes les informations centralisées dans addMainInformation
    this.addMainInformation(doc, certificate, currentY);
  }

  private static addCitizenIdSection(
    doc: jsPDF,
    certificate: Certificate,
    yPosition: number
  ) {
    const pageWidth = doc.internal.pageSize.width;

    // Encadré pour l'ID citoyen avec design moderne
    const boxWidth = 140;
    const boxHeight = 16;
    const boxX = pageWidth / 2 - boxWidth / 2;

    doc.setFillColor(COLORS_V2.LIGHT_GRAY);
    doc.setDrawColor(COLORS_V2.GREEN);
    doc.setLineWidth(1);
    doc.roundedRect(boxX, yPosition, boxWidth, boxHeight, 2, 2, "FD");

    // Texte ID Citoyen avec police cohérente
    doc.setFontSize(11); // Taille cohérente avec le contenu principal
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text(
      `ID CITOYEN: ${certificate.citizen.numeroIdentificationUnique}`,
      pageWidth / 2,
      yPosition + 10,
      { align: "center" }
    );
  }

  private static addMainInformation(
    doc: jsPDF,
    certificate: Certificate,
    yPosition: number
  ) {
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
    const contentX = margin + DESIGN_V2.QR_MARGIN;
    const pageWidth = doc.internal.pageSize.width;
    const midPageX = pageWidth / 2;
    let currentY = yPosition;

    // Espacement vertical uniforme et espace après les deux points standardisé
    const LINE_SPACING = 8;
    const COLON_SPACING = 4; // Espace standardisé après les deux points

    console.log("=== Rendering All Certificate Information ===");

    // Configuration de base - Police standardisée pour le contenu principal
    doc.setFontSize(10); // Taille uniforme pour tout le contenu
    doc.setTextColor(COLORS_V2.BLACK);

    // SECTION 1: Informations géographiques
    // PAIRE 1: Région de: .... et Préfecture de: ...
    doc.setFont("helvetica", "bold");
    doc.text("Région de: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const regionLabelWidth = doc.getTextWidth("Région de: ");
    const regionValue = certificate.quartier?.region || "NON DÉFINI";
    const regionTextWidth = doc.getTextWidth(regionValue);
    doc.text(
      regionValue,
      contentX + regionLabelWidth + COLON_SPACING,
      currentY
    );

    const regionEndX =
      contentX + regionLabelWidth + COLON_SPACING + regionTextWidth + 10;
    const prefectureStartX = Math.max(midPageX, regionEndX);

    doc.setFont("helvetica", "bold");
    doc.text("Préfecture de: ", prefectureStartX, currentY);
    doc.setFont("helvetica", "normal");
    const prefectureLabelWidth = doc.getTextWidth("Préfecture de: ");
    const prefectureValue = certificate.quartier?.commune || "NON DÉFINI";
    doc.text(
      prefectureValue,
      prefectureStartX + prefectureLabelWidth + COLON_SPACING,
      currentY
    );
    currentY += LINE_SPACING;

    // PAIRE 3: District/Quartier: ... et Commune de: ...
    doc.setFont("helvetica", "bold");
    doc.text("District/Quartier: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const quartierLabelWidth = doc.getTextWidth("District/Quartier: ");
    const quartierValue = certificate.quartier?.nom || "NON DÉFINI";
    const quartierTextWidth = doc.getTextWidth(quartierValue);
    doc.text(
      quartierValue,
      contentX + quartierLabelWidth + COLON_SPACING,
      currentY
    );

    // Commune de: sur la même ligne à droite
    const quartierEndX =
      contentX + quartierLabelWidth + COLON_SPACING + quartierTextWidth + 10;
    const communeStartX = Math.max(midPageX, quartierEndX);

    doc.setFont("helvetica", "bold");
    doc.text("Commune de: ", communeStartX, currentY);
    doc.setFont("helvetica", "normal");
    const communeLabelWidth = doc.getTextWidth("Commune de: ");
    const communeValue = certificate.quartier?.commune || "NON DÉFINI";
    doc.text(
      communeValue,
      communeStartX + communeLabelWidth + COLON_SPACING,
      currentY
    );
    currentY += LINE_SPACING;

    // PAIRE 2: Date d'émission: ... et Pour motif: ...
    doc.setFont("helvetica", "bold");
    doc.text("Date d'émission: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const dateLabelWidth = doc.getTextWidth("Date d'émission: ");
    const dateStr = new Date().toLocaleDateString("fr-FR");
    const dateFullText = `Le ${dateStr}`;
    const dateTextWidth = doc.getTextWidth(dateFullText);
    doc.text(dateFullText, contentX + dateLabelWidth + COLON_SPACING, currentY);

    if (certificate.motif) {
      const dateEndX =
        contentX + dateLabelWidth + COLON_SPACING + dateTextWidth + 10;
      const motifStartX = Math.max(midPageX, dateEndX);

      doc.setFont("helvetica", "bold");
      doc.text("Pour motif: ", motifStartX, currentY);
      doc.setFont("helvetica", "normal");
      const motifLabelWidth = doc.getTextWidth("Pour motif: ");
      doc.text(
        certificate.motif,
        motifStartX + motifLabelWidth + COLON_SPACING,
        currentY
      );
    }
    currentY += LINE_SPACING;

    // SECTION 2: Informations d'autorité
    doc.setFont("helvetica", "bold");
    doc.text("Je soussigné M./Mme.: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const chefWidth = doc.getTextWidth("Je soussigné M./Mme.: ");
    const chefValue = certificate.chefQuartierName || "NON DÉFINI";
    doc.text(chefValue, contentX + chefWidth + COLON_SPACING, currentY);
    currentY += LINE_SPACING;

    doc.setFont("helvetica", "bold");
    doc.text("Président du conseil de quartier de: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const presidentWidth = doc.getTextWidth(
      "Président du conseil de quartier de: "
    );
    const presidentQuartierValue = certificate.quartier?.nom || "NON DÉFINI";
    doc.text(
      presidentQuartierValue,
      contentX + presidentWidth + COLON_SPACING,
      currentY
    );
    currentY += LINE_SPACING;

    // SECTION 3: Certification principale
    // PAIRE 3: Certifie que: ... et Demeure à: ...
    doc.setFont("helvetica", "bold");
    doc.text("Certifie que: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const certifieLabelWidth = doc.getTextWidth("Certifie que: ");
    const citizenName = certificate.citizenName || "";
    const citizenNameWidth = doc.getTextWidth(citizenName);
    doc.text(
      citizenName,
      contentX + certifieLabelWidth + COLON_SPACING,
      currentY
    );

    const citizenNameEndX =
      contentX + certifieLabelWidth + COLON_SPACING + citizenNameWidth + 10;
    const demeureStartX = Math.max(midPageX, citizenNameEndX);

    doc.setFont("helvetica", "bold");
    doc.text("Demeure à: ", demeureStartX, currentY);
    doc.setFont("helvetica", "normal");
    const demeureLabelWidth = doc.getTextWidth("Demeure à: ");
    const adresse = certificate.citizen.adressePrecise || "";
    doc.text(
      adresse,
      demeureStartX + demeureLabelWidth + COLON_SPACING,
      currentY
    );
    currentY += LINE_SPACING;

    // PAIRE 4: Bâtiment N°: ... et Propriétaire: ... (TOUJOURS AFFICHÉES)
    doc.setFont("helvetica", "bold");
    doc.text("Bâtiment N°: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const batimentLabelWidth = doc.getTextWidth("Bâtiment N°: ");
    const batimentValue = certificate.citizen?.numeroBatiment || "NON DÉFINI";
    const batimentTextWidth = doc.getTextWidth(batimentValue);
    doc.text(
      batimentValue,
      contentX + batimentLabelWidth + COLON_SPACING,
      currentY
    );

    // Propriétaire sur la même ligne à droite
    const batimentEndX =
      contentX + batimentLabelWidth + COLON_SPACING + batimentTextWidth + 10;
    const proprietaireStartX = Math.max(midPageX, batimentEndX);

    doc.setFont("helvetica", "bold");
    doc.text("Propriétaire: ", proprietaireStartX, currentY);
    doc.setFont("helvetica", "normal");
    const proprietaireLabelWidth = doc.getTextWidth("Propriétaire: ");
    const proprietaireValue =
      certificate.citizen?.proprietaireBatiment || "NON DÉFINI";
    doc.text(
      proprietaireValue,
      proprietaireStartX + proprietaireLabelWidth + COLON_SPACING,
      currentY
    );
    currentY += LINE_SPACING;

    // SECTION 4: Informations personnelles du citoyen (même taille de police)
    // PAIRE 4: de Nationalité: ... et Profession: ...
    doc.setFont("helvetica", "bold");
    doc.text("de Nationalité: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const nationalityLabelWidth = doc.getTextWidth("de Nationalité: ");
    const nationalityValue = "Guinéenne";
    const nationalityTextWidth = doc.getTextWidth(nationalityValue);
    doc.text(
      nationalityValue,
      contentX + nationalityLabelWidth + COLON_SPACING,
      currentY
    );

    if (certificate.citizen?.profession) {
      const nationalityEndX =
        contentX +
        nationalityLabelWidth +
        COLON_SPACING +
        nationalityTextWidth +
        10;
      const professionStartX = Math.max(midPageX, nationalityEndX);

      doc.setFont("helvetica", "bold");
      doc.text("Profession: ", professionStartX, currentY);
      doc.setFont("helvetica", "normal");
      const professionLabelWidth = doc.getTextWidth("Profession: ");
      doc.text(
        certificate.citizen.profession,
        professionStartX + professionLabelWidth + COLON_SPACING,
        currentY
      );
    }
    currentY += LINE_SPACING;

    // PAIRE 5: Né(e) le: ... et à: ...
    if (certificate.citizen?.dateNaissance) {
      doc.setFont("helvetica", "bold");
      doc.text("Né(e) le: ", contentX, currentY);
      doc.setFont("helvetica", "normal");
      const dateLabelWidth = doc.getTextWidth("Né(e) le: ");
      const dateValue = new Date(
        certificate.citizen.dateNaissance
      ).toLocaleDateString("fr-FR");
      const dateValueWidth = doc.getTextWidth(dateValue);
      doc.text(dateValue, contentX + dateLabelWidth + COLON_SPACING, currentY);

      if (certificate.citizen?.lieuNaissance) {
        const dateEndX =
          contentX + dateLabelWidth + COLON_SPACING + dateValueWidth + 10;
        const lieuStartX = Math.max(midPageX, dateEndX);

        doc.setFont("helvetica", "bold");
        doc.text("à: ", lieuStartX, currentY);
        doc.setFont("helvetica", "normal");
        const atWidth = doc.getTextWidth("à: ");
        doc.text(
          certificate.citizen.lieuNaissance,
          lieuStartX + atWidth + COLON_SPACING,
          currentY
        );
      }
      currentY += LINE_SPACING;
    }

    // PAIRE 6: Fils/Fille de: ... et et de: ...
    if (certificate.citizen?.nomPere) {
      doc.setFont("helvetica", "bold");
      doc.text("Fils/Fille de: ", contentX, currentY);
      doc.setFont("helvetica", "normal");
      const parentLabelWidth = doc.getTextWidth("Fils/Fille de: ");
      const nomPereWidth = doc.getTextWidth(certificate.citizen.nomPere);
      doc.text(
        certificate.citizen.nomPere,
        contentX + parentLabelWidth + COLON_SPACING,
        currentY
      );

      if (certificate.citizen?.nomMere) {
        const pereEndX =
          contentX + parentLabelWidth + COLON_SPACING + nomPereWidth + 10;
        const mereStartX = Math.max(midPageX, pereEndX);

        doc.setFont("helvetica", "bold");
        doc.text("et de: ", mereStartX, currentY);
        doc.setFont("helvetica", "normal");
        const etDeWidth = doc.getTextWidth("et de: ");
        doc.text(
          certificate.citizen.nomMere,
          mereStartX + etDeWidth + COLON_SPACING,
          currentY
        );
      }
      currentY += LINE_SPACING;
    }

    // SECTION 5: Informations complémentaires
    // Date d'installation
    if (certificate.citizen?.dateInstallation) {
      doc.setFont("helvetica", "bold");
      doc.text("Réside ce quartier depuis: ", contentX, currentY);
      doc.setFont("helvetica", "normal");
      const residenceLabelWidth = doc.getTextWidth(
        "Réside ce quartier depuis: "
      );
      const installationDate = new Date(
        certificate.citizen.dateInstallation
      ).toLocaleDateString("fr-FR");
      doc.text(
        installationDate,
        contentX + residenceLabelWidth + COLON_SPACING,
        currentY
      );
      currentY += LINE_SPACING;
    }

    // Carte électorale - TOUJOURS affichée
    doc.setFont("helvetica", "bold");
    doc.text("N° Carte Électorale: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const cardLabelWidth = doc.getTextWidth("N° Carte Électorale: ");
    const carteElecteurValue =
      certificate.citizen?.carteElecteur || "NON DÉFINI";
    doc.text(
      carteElecteurValue,
      contentX + cardLabelWidth + COLON_SPACING,
      currentY
    );
    currentY += LINE_SPACING;

    // SECTION 6: Validité et formule de clôture (TOUJOURS AFFICHÉES)
    currentY += 6; // Espacement supplémentaire avant validité

    // Validité - TOUJOURS affichée avec mise en valeur
    doc.setFontSize(11); // Légèrement plus grande pour la validité
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text("Validité: ", contentX, currentY);
    doc.setFont("helvetica", "bold"); // Garde le gras pour la valeur aussi
    const validityLabelWidth = doc.getTextWidth("Validité: ");
    doc.text(
      "03 Mois",
      contentX + validityLabelWidth + COLON_SPACING,
      currentY
    );
    currentY += LINE_SPACING + 2; // Espacement légèrement plus grand

    // Formule de clôture - TOUJOURS affichée avec style professionnel
    doc.setFontSize(9); // Taille réduite pour la formule de clôture
    doc.setFont("helvetica", "italic");
    doc.setTextColor(COLORS_V2.DARK_GRAY); // Couleur plus subtile
    doc.text(
      "En foi de quoi le présent certificat est délivré pour servir et valoir ce que de droit.",
      contentX,
      currentY
    );

    console.log(`All information rendered. Final Y position: ${currentY}`);
  }

  private static async addQRCode(doc: jsPDF, securityData: any) {
    try {
      // Génération du QR code
      const qrCodeBase64 = await QRCodeGenerator.generateCertificateQR(
        securityData.verificationUrl,
        securityData.verificationHash
      );

      // Position du QR code (coin inférieur gauche) - aligned with bottom elements
      const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
      const qrX = margin + DESIGN_V2.QR_MARGIN;
      const pageHeight = doc.internal.pageSize.height;
      const bottomAlignY = pageHeight - margin - 15; // Bottom alignment reference
      const qrY = bottomAlignY - DESIGN_V2.QR_SIZE; // QR code bottom-aligned

      // Ajout du QR code
      doc.addImage(
        `data:image/png;base64,${qrCodeBase64}`,
        "PNG",
        qrX,
        qrY,
        DESIGN_V2.QR_SIZE,
        DESIGN_V2.QR_SIZE
      );

      // Texte explicatif sous le QR code
      doc.setFontSize(8);
      doc.setFont("helvetica", "normal");
      doc.setTextColor(COLORS_V2.DARK_GRAY);
      doc.text(
        "Scanner pour vérifier",
        qrX + DESIGN_V2.QR_SIZE / 2,
        qrY + DESIGN_V2.QR_SIZE + 5,
        { align: "center" }
      );
    } catch (error) {
      console.error("Erreur lors de l'ajout du QR code:", error);
      // Continuer sans QR code en cas d'erreur
    }
  }

  private static addModernFooter(doc: jsPDF, certificate: Certificate) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;

    // Référence d'alignement pour tous les éléments du bas
    const bottomAlignY = pageHeight - margin - 15;

    console.log("=== Rendering Professional Footer Elements ===");

    // SECTION 1: QR Code (déjà géré dans addQRCode avec alignement cohérent)

    // SECTION 2: Logo Simandou (centre-gauche, aligné avec les autres éléments)
    if (this.simandouBase64) {
      const logoWidth = 40;
      const logoHeight = 20;
      const logoX = margin + DESIGN_V2.QR_MARGIN + DESIGN_V2.QR_SIZE + 15; // 15mm après le QR code
      const logoY = bottomAlignY - logoHeight; // Aligné par le bas

      doc.addImage(
        `data:image/png;base64,${this.simandouBase64}`,
        "PNG",
        logoX,
        logoY,
        logoWidth,
        logoHeight
      );

      console.log(`Simandou logo positioned at X: ${logoX}, Y: ${logoY}`);
    }

    // SECTION 3: Texte "Fait à..." (au-dessus de la zone de signature)
    const dateDelivrance = certificate.deliveredAt
      ? new Date(certificate.deliveredAt).toLocaleDateString("fr-FR")
      : new Date().toLocaleDateString("fr-FR");

    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
    doc.setTextColor(COLORS_V2.BLACK);

    // Position du texte "Fait à..." - au-dessus de la signature avec espacement professionnel
    const signatureBoxWidth = 47.5; // Taille du cadre de signature réduit
    const signatureBoxX =
      pageWidth - margin - DESIGN_V2.BORDER_WIDTH - signatureBoxWidth;
    const faitAY = bottomAlignY - 37; // 37mm au-dessus du bas pour laisser place à la signature

    const faitAText = `Fait à ${
      certificate.quartier?.commune || ""
    }, le ${dateDelivrance}`;
    doc.text(
      faitAText,
      signatureBoxX + signatureBoxWidth, // Aligné à droite avec le cadre de signature
      faitAY,
      { align: "right" }
    );

    console.log(
      `"Fait à" text positioned at Y: ${faitAY}, aligned right with signature frame`
    );

    // SECTION 4: Ligne de séparation décorative (optionnelle)
    const separatorY = faitAY - 8;
    const separatorStartX = margin + DESIGN_V2.QR_MARGIN;
    const separatorEndX = pageWidth - margin - DESIGN_V2.CONTENT_PADDING;

    doc.setDrawColor(COLORS_V2.LIGHT_GRAY);
    doc.setLineWidth(0.5);
    doc.line(separatorStartX, separatorY, separatorEndX, separatorY);

    console.log(`Separator line drawn at Y: ${separatorY}`);

    console.log("=== Footer elements rendered professionally ===");
  }

  private static async addModernSignature(
    doc: jsPDF,
    signatureBuffer: ArrayBuffer,
    certificate: Certificate
  ) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;

    // Encadré pour la signature - optimisé pour une meilleure signature
    const signatureBoxWidth = 47.5; // Réduit de moitié (95/2)
    const signatureBoxHeight = 25; // Légèrement agrandi pour la signature
    // Align right edge with tricolor border right edge
    const signatureBoxX =
      pageWidth - margin - DESIGN_V2.BORDER_WIDTH - signatureBoxWidth;

    // Bottom-align signature frame with other elements
    const bottomAlignY = pageHeight - margin - 15;
    const alignedSignatureY = bottomAlignY - signatureBoxHeight;

    doc.setDrawColor(COLORS_V2.GREEN);
    doc.setLineWidth(1);
    doc.rect(
      signatureBoxX,
      alignedSignatureY,
      signatureBoxWidth,
      signatureBoxHeight
    );

    // Titre de la signature - aligné à droite avec le cadre
    doc.setFontSize(10); // Même taille que "Fait à..."
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);

    // Positionnement au-dessus du cadre, après "Fait à..."
    const signatureTextY = alignedSignatureY - 8; // 8mm au-dessus du cadre

    doc.text(
      "Signature et Cachet du",
      signatureBoxX + signatureBoxWidth,
      signatureTextY,
      { align: "right" }
    );
    doc.text(
      "Président du Conseil de Quartier",
      signatureBoxX + signatureBoxWidth,
      signatureTextY + 4, // Interligne de 4mm
      { align: "right" }
    );

    // Image de la signature agrandie
    const base64 = Buffer.from(signatureBuffer).toString("base64");
    doc.addImage(
      `data:image/png;base64,${base64}`,
      "PNG",
      signatureBoxX + 2, // Marge réduite pour plus d'espace
      alignedSignatureY + 2, // Position plus haute
      signatureBoxWidth - 4, // Largeur agrandie
      15 // Hauteur agrandie de 10 à 15
    );

    // Nom du signataire - taille cohérente avec "Fait à..."
    doc.setFontSize(10); // Même taille que "Fait à..."
    doc.setFont("helvetica", "normal");
    doc.text(
      certificate.chefQuartierName || "",
      signatureBoxX + signatureBoxWidth / 2,
      alignedSignatureY + signatureBoxHeight - 1,
      { align: "center" }
    );
  }
}
