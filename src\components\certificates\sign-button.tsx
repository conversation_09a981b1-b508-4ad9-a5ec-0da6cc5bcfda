"use client";

import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils/cn";
import { FileSignature, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { ButtonHTMLAttributes } from "react";

interface SignButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  certificateId: string;
  status: string;
  isProcessing?: boolean;
  variant?: "default" | "action";
  size?: "default" | "sm" | "lg" | "icon";
}

export function SignButton({
  certificateId,
  status,
  isProcessing,
  variant = "default",
  size = "default",
  className,
  onClick,
  ...props
}: SignButtonProps) {
  const router = useRouter();
  const isReady = status === CERTIFICATE_STATUS.READY;

  if (!isReady) return null;

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (onClick) {
      onClick(e);
    } else if (variant !== "action") {
      router.push(`/dashboard/certificates/${certificateId}/sign`);
    }
  };

  return (
    <Button
      {...props}
      onClick={handleClick}
      disabled={isProcessing || !isReady}
      size={size}
      className={cn(
        variant === "action" && "bg-emerald-600 hover:bg-emerald-700",
        className
      )}
    >
      {isProcessing ? (
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      ) : (
        <FileSignature className="h-4 w-4 mr-2" />
      )}
      {variant === "action" ? "Apposer votre signature" : "Signer"}
    </Button>
  );
}
