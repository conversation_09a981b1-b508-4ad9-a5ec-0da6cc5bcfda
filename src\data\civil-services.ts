export const civilServices = [
  {
    id: "residence",
    title: "Certificats de Résidence",
    description: "Gestion numérique des attestations de résidence",
    icon: "📋",
    features: [
      "Demande en ligne",
      "Validation électronique",
      "Suivi en temps réel",
      "Vérification QR code",
    ],
  },
  {
    id: "birth",
    title: "Actes de Naissance",
    description: "Digitalisation des déclarations de naissance",
    icon: "👶",
    features: [
      "Déclaration en ligne",
      "Copies numériques",
      "Mise à jour simplifiée",
      "Archives sécurisées",
    ],
  },
  {
    id: "marriage",
    title: "Actes de Mariage",
    description: "Gestion complète des unions civiles",
    icon: "💑",
    features: [
      "Publication des bans",
      "Programmation des cérémonies",
      "Certificats numériques",
      "Copies d'actes certifiées",
    ],
  },
  {
    id: "nationality",
    title: "Certificats de Nationalité",
    description: "Délivrance sécurisée des attestations de nationalité",
    icon: "🏳️",
    features: [
      "Vérification d'identité",
      "Traitement prioritaire",
      "Authentification biométrique",
      "Copies certifiées conformes",
    ],
  },
  {
    id: "death",
    title: "Actes de Décès",
    description: "Gestion administrative des déclarations de décès",
    icon: "📜",
    features: [
      "Déclaration simplifiée",
      "Traitement accéléré",
      "Copies numériques",
      "Archivage sécurisé",
    ],
  },
  {
    id: "family",
    title: "Livret de Famille",
    description: "Carnet numérique de l'histoire familiale",
    icon: "👨‍👩‍👧‍👦",
    features: [
      "Création automatisée",
      "Mises à jour instantanées",
      "Version numérique",
      "Historique complet",
    ],
  },
  {
    id: "identity",
    title: "Cartes d'Identité",
    description: "Gestion des documents d'identité officiels",
    icon: "🪪",
    features: [
      "Demande en ligne",
      "Photo numérique",
      "Suivi du traitement",
      "Renouvellement simplifié",
    ],
  },
  {
    id: "judicial",
    title: "Casier Judiciaire",
    description: "Délivrance sécurisée des extraits de casier",
    icon: "⚖️",
    features: [
      "Demande confidentielle",
      "Vérification d'identité",
      "Délivrance rapide",
      "Format numérique",
    ],
  },
  {
    id: "life",
    title: "Certificats de Vie",
    description: "Attestations pour les résidents à l'étranger",
    icon: "✅",
    features: [
      "Validation biométrique",
      "Renouvellement automatique",
      "Envoi international",
      "Traduction certifiée",
    ],
  },
];
