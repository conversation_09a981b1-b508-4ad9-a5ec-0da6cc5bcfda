import { Button, ButtonProps } from "@/components/ui/button";
import { Loader } from "@/components/ui/loader";
import { cn } from "@/lib/utils/cn";
import { LucideIcon } from "lucide-react";

interface ActionButtonProps extends ButtonProps {
  icon: LucideIcon;
  isLoading?: boolean;
  loadingText?: string;
}

export function ActionButton({
  icon: Icon,
  children,
  isLoading,
  loadingText,
  className,
  ...props
}: ActionButtonProps) {
  return (
    <Button
      className={cn("flex items-center gap-2", className)}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading ? (
        <>
          <Loader size="xs" variant="ghost" className="text-current" />
          {loadingText || children}
        </>
      ) : (
        <>
          <Icon className="h-4 w-4" />
          {children}
        </>
      )}
    </Button>
  );
}