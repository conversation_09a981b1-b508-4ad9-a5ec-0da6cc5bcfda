"use server";

import { ROLES, STATUS } from "@/actions/auth/constants";
import { createAdminClient } from "@/lib/server/appwrite";
import { AdminUserFormValues } from "@/components/dashboard/admin/users/add-user-form";
import { getCurrentUser } from "../auth/session";
import { ID, Query } from "node-appwrite";
import { checkUserExists } from "../auth/user-account-handler";
import { QUARTIERS_COLLECTION_ID, DATABASE_ID } from "@/lib/server/database";
import { getCollectionByRole } from "@/lib/utils/get-collection-by-role";

export async function createAdminUser(data: AdminUserFormValues) {
  try {
    // 1. Vérifier les permissions de l'utilisateur courant
    const { user } = await getCurrentUser();
    if (!user || user.prefs?.role !== ROLES.ADMIN) {
      throw new Error("Non autorisé");
    }

    // 2. Vérifier l'existence de l'utilisateur
    const telephoneCheck = await checkUserExists("telephone", data.phoneNumber);
    if (telephoneCheck.exists) {
      return { success: false, error: telephoneCheck.error };
    }

    const emailCheck = await checkUserExists("email", data.email);
    if (emailCheck.exists) {
      return { success: false, error: emailCheck.error };
    }

    // 3. Création du client Appwrite avec privilèges admin
    const { account, databases, users } = await createAdminClient();

    // Vérifier le quartier uniquement pour les rôles CHEF et AGENT
    let quartier = null;
    if (data.role !== ROLES.ADMIN) {
      if (!data.quartier) {
        return {
          success: false,
          error: "Le quartier est requis pour les chefs et agents"
        };
      }

      const { documents: quartiers, total } = await databases.listDocuments(
        DATABASE_ID,
        QUARTIERS_COLLECTION_ID,
        [Query.equal("nom", data.quartier)]
      );

      if (total === 0) {
        return {
          success: false,
          error: "Le quartier spécifié n'existe pas."
        };
      }

      quartier = quartiers[0];

      // Vérifications spécifiques pour les chefs
      if (data.role === ROLES.CHEF && quartier.chefId) {
        return {
          success: false,
          error: "Ce quartier a déjà un chef. Veuillez d'abord révoquer le chef actuel."
        };
      }

      // Vérifications spécifiques pour les agents
      if (data.role === ROLES.AGENT && !quartier.chefId) {
        return {
          success: false,
          error: "Impossible d'ajouter un agent : ce quartier n'a pas de chef."
        };
      }
    }

    // 7. Créer l'utilisateur Appwrite
    const newUser = await account.create(
      ID.unique(),
      data.email,
      data.password,
      `${data.firstName} ${data.lastName}`
    );

    // 8. Définir les préférences utilisateur
    await users.updatePrefs(newUser.$id, {
      role: data.role,
      status: STATUS.PENDING,
      avatarUrl: null,
    });

    // 9. Préparer les données utilisateur de base
    const userData: Record<string, any> = {
      userId: newUser.$id,
      nom: data.firstName,
      prenom: data.lastName,
      email: data.email,
      telephone: data.phoneNumber,
      status: STATUS.PENDING,
      role: data.role,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Ajouter les données spécifiques au rôle seulement si nécessaire
    if (data.role !== ROLES.ADMIN && quartier) {
      userData.quartier = quartier.nom;

      if (data.role === ROLES.CHEF) {
        await databases.updateDocument(
          DATABASE_ID,
          QUARTIERS_COLLECTION_ID,
          quartier.$id,
          {
            chefId: newUser.$id,
            updatedAt: new Date().toISOString(),
          }
        );
      } else if (data.role === ROLES.AGENT) {
        userData.chefId = quartier.chefId;
      }
    }

    // 11. Créer le document utilisateur dans la collection appropriée
    const COLLECTION_ID = getCollectionByRole(data.role);
    if (!COLLECTION_ID) {
      throw new Error("Rôle invalide");
    }

    await databases.createDocument(
      DATABASE_ID,
      COLLECTION_ID,
      ID.unique(),
      userData
    );

    return {
      success: true,
      user: {
        $id: newUser.$id,
        name: `${data.firstName} ${data.lastName}`,
        email: data.email,
        role: data.role,
        status: STATUS.PENDING,
        ...(quartier && {
          quartier: {
            id: quartier.$id,
            name: quartier.nom,
          },
        }),
        phoneNumber: data.phoneNumber,
        createdAt: new Date(newUser.$createdAt),
        lastLoginAt: new Date(newUser.$createdAt),
      },
    };

  } catch (error: any) {
    console.error("Erreur lors de la création de l'utilisateur:", error);
    return {
      success: false,
      error: error?.message ?? "Une erreur est survenue lors de la création de l'utilisateur",
    };
  }
}