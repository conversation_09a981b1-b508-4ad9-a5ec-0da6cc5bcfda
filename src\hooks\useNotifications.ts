import { useState, useEffect, useCallback } from "react";
import { NotificationPermission } from "@/types/notifications";
import {
  requestNotificationPermission,
  subscribeToPushNotifications,
  setupNotificationClickHandler,
} from "@/lib/utils/notifications";

export function useNotifications() {
  const [permission, setPermission] = useState<NotificationPermission>("default");
  const [subscription, setSubscription] = useState<PushSubscription | null>(null);
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    // Vérifier le support des notifications
    const supported = "Notification" in window && "serviceWorker" in navigator;
    setIsSupported(supported);

    if (supported) {
      setPermission(Notification.permission);
      setupNotificationClickHandler();
    }
  }, []);

  const requestPermission = useCallback(async () => {
    if (!isSupported) return "denied";

    try {
      const newPermission = await requestNotificationPermission();
      setPermission(newPermission);
      return newPermission;
    } catch (error) {
      console.error("Erreur lors de la demande de permission:", error);
      return "denied";
    }
  }, [isSupported]);

  const subscribe = useCallback(async () => {
    if (!isSupported || permission !== "granted") return null;

    try {
      const registration = await navigator.serviceWorker.ready;
      const newSubscription = await subscribeToPushNotifications(registration);
      setSubscription(newSubscription);
      return newSubscription;
    } catch (error) {
      console.error("Erreur lors de l'inscription aux notifications:", error);
      return null;
    }
  }, [isSupported, permission]);

  const unsubscribe = useCallback(async () => {
    if (!subscription) return false;

    try {
      await subscription.unsubscribe();
      // Informer le serveur
      await fetch("/api/notifications/unsubscribe", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ endpoint: subscription.endpoint }),
      });
      setSubscription(null);
      return true;
    } catch (error) {
      console.error("Erreur lors de la désinscription:", error);
      return false;
    }
  }, [subscription]);

  return {
    isSupported,
    permission,
    subscription,
    requestPermission,
    subscribe,
    unsubscribe,
  };
}
