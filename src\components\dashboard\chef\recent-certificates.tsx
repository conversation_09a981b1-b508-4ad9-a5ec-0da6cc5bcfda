"use client";

import { getRecentCertificates } from "@/actions/certificates";
import { Certificate } from "@/actions/types";
import { CertificateActions } from "@/components/certificates/certificate-actions";
import { StatusBadge } from "@/components/certificates/status-badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { AnimatePresence, motion } from "framer-motion";
import { FileText, Loader2, Search } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

export function RecentCertificates() {
  const router = useRouter();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");

  const { data, isLoading } = useQuery({
    queryKey: ["recent-certificates"],
    queryFn: () => getRecentCertificates(),
  });

  if (!user) return null;

  const certificates = data?.certificates || [];
  const filteredCertificates = certificates.filter(
    (cert: Certificate) =>
      cert.citizenName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      cert.type?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="space-y-6"
    >
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent">
          Certificats récents
        </h2>
        <Button
          variant="outline"
          className="gap-2 text-neutral-600 hover:text-neutral-900"
          onClick={() => router.push("/dashboard/certificates")}
        >
          <FileText className="w-4 h-4" />
          Voir tout
        </Button>
      </div>

      <div className="relative">
        <Input
          placeholder="Rechercher par nom ou type..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-neutral-400" />
      </div>

      <div className="relative rounded-2xl border border-neutral-200/60 overflow-hidden">
        {filteredCertificates.length > 0 && (
          <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-white/30 backdrop-blur-sm" />
        )}

        {isLoading ? (
          <div className="flex items-center justify-center h-64 bg-white">
            <Loader2 className="w-8 h-8 animate-spin text-accent-primary" />
          </div>
        ) : filteredCertificates.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 bg-white text-neutral-500">
            <FileText className="w-12 h-12 mb-4 text-neutral-400" />
            <p className="text-sm">Aucun certificat trouvé</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-neutral-50/50 border-neutral-200/60">
                <TableHead>Type</TableHead>
                <TableHead>Demandeur</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Agent assigné</TableHead>
                <TableHead className="w-[50px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <AnimatePresence mode="popLayout">
                {filteredCertificates.map((cert, index) => (
                  <motion.tr
                    key={cert.$id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="relative hover:bg-neutral-50/50 border-neutral-200/60 group"
                  >
                    <TableCell>
                      <span className="font-medium text-neutral-900">
                        {cert.type}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className="text-neutral-700">
                        {cert.citizenName || "N/A"}
                      </span>
                    </TableCell>
                    <TableCell>
                      <StatusBadge status={cert.status} />
                    </TableCell>
                    <TableCell>
                      <span className="text-neutral-600">
                        {formatDistanceToNow(new Date(cert.$createdAt), {
                          addSuffix: true,
                          locale: fr,
                        })}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className="text-neutral-700">
                        {cert.agentName || "-"}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <CertificateActions
                          certificate={cert}
                          userRole={
                            user.prefs?.role as "admin" | "chef" | "agent"
                          }
                          alwaysVisible={true}
                        />
                      </div>
                    </TableCell>
                  </motion.tr>
                ))}
              </AnimatePresence>
            </TableBody>
          </Table>
        )}
      </div>
    </motion.div>
  );
}
