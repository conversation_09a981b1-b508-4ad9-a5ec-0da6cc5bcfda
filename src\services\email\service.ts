import { ContactFormData } from "@/schemas/contact";
import { createTransport, Transporter } from "nodemailer";
import { getContactEmailTemplate } from "./templates";

export class EmailService {
  private transporter: Transporter;
  private config: {
    from: string;
    adminEmail: string;
    smtpHost: string;
    smtpPort: number;
    smtpSecure: boolean;
    smtpUser: string;
    smtpPass: string;
    emailName: string;
  };

  constructor() {
    this.config = {
      from: process.env.SMTP_FROM || "<EMAIL>",
      adminEmail: process.env.ADMIN_EMAIL || "<EMAIL>",
      smtpHost: process.env.SMTP_HOST || "ssl0.ovh.net",
      smtpPort: parseInt(process.env.SMTP_PORT || "465"),
      smtpSecure: process.env.SMTP_SECURE === "true",
      smtpUser: process.env.SMTP_USER || "",
      smtpPass: process.env.SMTP_PASS || "",
      emailName: process.env.EMAIL_NAME || "NCR",
    };

    this.transporter = createTransport({
      host: this.config.smtpHost,
      port: this.config.smtpPort,
      secure: this.config.smtpSecure,
      auth: {
        user: this.config.smtpUser,
        pass: this.config.smtpPass,
      },
      debug: process.env.NODE_ENV === "development",
    });
  }

  async sendContactConfirmation(data: ContactFormData) {
    const { userTemplate, adminTemplate } = getContactEmailTemplate(data);

    try {
      // Envoi de la confirmation à l'utilisateur
      await this.transporter.sendMail({
        from: `${this.config.emailName} <${this.config.from}>`,
        to: data.email,
        subject: `Confirmation de votre message - ${data.subject}`,
        html: userTemplate,
      });

      // Notification à l'administrateur
      await this.transporter.sendMail({
        from: `${this.config.emailName} <${this.config.from}>`,
        to: this.config.adminEmail,
        replyTo: data.email,
        subject: `Nouvelle demande de contact - ${data.subject}`,
        html: adminTemplate,
      });

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de l'envoi des emails:", error);
      throw new Error(
        error instanceof Error ? error.message : "Échec de l'envoi des emails"
      );
    }
  }

  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      return true;
    } catch (error) {
      console.error("Erreur de connexion SMTP:", {
        error,
        config: {
          host: this.config.smtpHost,
          port: this.config.smtpPort,
          secure: this.config.smtpSecure,
          user: this.config.smtpUser,
        },
      });
      return false;
    }
  }
}
