"use client";

import { ROLES, STATUS } from "@/actions/auth/constants";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Pagination } from "@/components/ui/pagination";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { UserAvatar } from "@/components/ui/user-avatar";
import { useUsers } from "@/hooks/use-users";
import { UsersFilters } from "@/types/user";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { MoreHorizontal, Shield, UserCog, Users } from "lucide-react";
import { useState } from "react";
import { Loader } from "@/components/ui/loader";
import { UserStatusBadge } from "@/components/users/user-status-badge";

const getRoleIcon = (role: ROLES) => {
  switch (role) {
    case ROLES.ADMIN:
      return Shield;
    case ROLES.CHEF:
      return Users;
    case ROLES.AGENT:
      return UserCog;
    default:
      return Users;
  }
};

export function UsersDataTable() {
  const [filters, setFilters] = useState<UsersFilters>({
    page: 0,
    limit: 10,
  });

  const { users, pagination, isLoading, isFetching, updateStatus, isUpdating } =
    useUsers(filters);

  const handleStatusChange = async (userId: string, newStatus: STATUS) => {
    updateStatus.mutate({ userId, status: newStatus });
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8">
        <Loader
          variant="primary"
          size="md"
          text="Chargement des utilisateurs..."
          className="min-h-[200px]"
        />
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Utilisateur</TableHead>
            <TableHead>Rôle</TableHead>
            <TableHead>Quartier</TableHead>
            <TableHead>Statut</TableHead>
            <TableHead>Inscrit le</TableHead>
            <TableHead>Dernière connexion</TableHead>
            <TableHead className="w-[80px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading
            ? // Squelette de chargement
              Array.from({ length: filters.limit || 10 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getRoleIcon(users[index].role)({ className: "w-4 h-4" })}
                      <span className="capitalize">{users[index].role}</span>
                    </div>
                  </TableCell>
                  <TableCell>{users[index].district?.name}</TableCell>
                  <TableCell>
                    <Skeleton className="h-6 w-16 rounded-full" />
                  </TableCell>
                  <TableCell>
                    {formatDistanceToNow(users[index].createdAt, {
                      addSuffix: true,
                      locale: fr,
                    })}
                  </TableCell>
                  <TableCell>
                    {formatDistanceToNow(users[index].lastLoginAt, {
                      addSuffix: true,
                      locale: fr,
                    })}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="h-8 w-8 p-0"
                          disabled={isUpdating}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() =>
                            handleStatusChange(users[index].$id, STATUS.ACTIVE)
                          }
                          disabled={
                            users[index].status === STATUS.ACTIVE || isUpdating
                          }
                        >
                          Activer
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            handleStatusChange(
                              users[index].$id,
                              STATUS.INACTIVE
                            )
                          }
                          disabled={
                            users[index].status === STATUS.INACTIVE ||
                            isUpdating
                          }
                        >
                          Désactiver
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            handleStatusChange(users[index].$id, STATUS.BLOCKED)
                          }
                          disabled={
                            users[index].status === STATUS.BLOCKED || isUpdating
                          }
                          className="text-red-600"
                        >
                          Bloquer
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            : users.map((user) => (
                <TableRow key={user.$id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <UserAvatar
                        name={user.name}
                        src={user.avatarUrl}
                        size="sm"
                      />
                      <div>
                        <p className="font-medium text-gray-900">{user.name}</p>
                        <p className="text-sm text-gray-500">{user.email}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getRoleIcon(user.role)({ className: "w-4 h-4" })}
                      <span className="capitalize">{user.role}</span>
                    </div>
                  </TableCell>
                  <TableCell>{user.district?.name}</TableCell>
                  <TableCell>
                    <UserStatusBadge status={user.status} />
                  </TableCell>
                  <TableCell>
                    {formatDistanceToNow(user.createdAt, {
                      addSuffix: true,
                      locale: fr,
                    })}
                  </TableCell>
                  <TableCell>
                    {formatDistanceToNow(user.lastLoginAt, {
                      addSuffix: true,
                      locale: fr,
                    })}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="h-8 w-8 p-0"
                          disabled={isUpdating}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() =>
                            handleStatusChange(user.$id, STATUS.ACTIVE)
                          }
                          disabled={user.status === STATUS.ACTIVE || isUpdating}
                        >
                          Activer
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            handleStatusChange(user.$id, STATUS.INACTIVE)
                          }
                          disabled={
                            user.status === STATUS.INACTIVE || isUpdating
                          }
                        >
                          Désactiver
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            handleStatusChange(user.$id, STATUS.BLOCKED)
                          }
                          disabled={
                            user.status === STATUS.BLOCKED || isUpdating
                          }
                          className="text-red-600"
                        >
                          Bloquer
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
        </TableBody>
      </Table>

      {pagination && (
        <div className="p-4 border-t border-gray-100">
          <div className="relative">
            {isFetching && !isLoading && (
              <div className="absolute inset-0 bg-white/50">
                <Loader variant="secondary" size="sm" className="h-full" />
              </div>
            )}
            <Pagination
              total={pagination.total}
              page={pagination.page}
              limit={pagination.limit}
              onPageChange={(page) => setFilters({ ...filters, page })}
            />
          </div>
        </div>
      )}
    </div>
  );
}
