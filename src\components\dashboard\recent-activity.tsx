"use client";

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { motion } from "framer-motion";
import { FileText, CheckCircle2, XCir<PERSON>, Clock } from "lucide-react";
import { CERTIFICATE_STATUS } from "@/actions/auth/constants";

type Activity = {
  id: number;
  type: "status_change" | "new_request";
  certificate: string;
  status: keyof typeof statusIcons;
  message: string;
  timestamp: string;
};

const statusIcons = {
  [CERTIFICATE_STATUS.PENDING]: Clock,
  [CERTIFICATE_STATUS.SUBMITTED]: Clock,
  [CERTIFICATE_STATUS.VERIFIED]: CheckCircle2,
  [CERTIFICATE_STATUS.APPROVED]: CheckCircle2,
  [CERTIFICATE_STATUS.REJECTED]: XCircle,
  [CERTIFICATE_STATUS.READY]: Clock,
  [CERTIFICATE_STATUS.SIGNED]: Check<PERSON><PERSON><PERSON><PERSON>,
  [CERTIFICATE_STATUS.DELIVERED]: CheckCircle2,
  [CERTIFICATE_STATUS.EXPIRED]: FileText,
} as const;

const statusColors = {
  [CERTIFICATE_STATUS.PENDING]: "text-yellow-500",
  [CERTIFICATE_STATUS.SUBMITTED]: "text-yellow-500",
  [CERTIFICATE_STATUS.VERIFIED]: "text-blue-500",
  [CERTIFICATE_STATUS.APPROVED]: "text-green-500",
  [CERTIFICATE_STATUS.REJECTED]: "text-red-500",
  [CERTIFICATE_STATUS.READY]: "text-yellow-500",
  [CERTIFICATE_STATUS.SIGNED]: "text-green-500",
  [CERTIFICATE_STATUS.DELIVERED]: "text-green-500",
  [CERTIFICATE_STATUS.EXPIRED]: "text-gray-500",
} as const;

const activities: Activity[] = [
  {
    id: 1,
    type: "status_change",
    certificate: "CERT-001",
    status: CERTIFICATE_STATUS.APPROVED,
    message: "Certificat approuvé",
    timestamp: "2024-01-20T10:00:00",
  },
  {
    id: 2,
    type: "new_request",
    certificate: "CERT-002",
    status: CERTIFICATE_STATUS.PENDING,
    message: "Nouvelle demande créée",
    timestamp: "2024-01-19T15:30:00",
  },
  {
    id: 3,
    type: "status_change",
    certificate: "CERT-003",
    status: CERTIFICATE_STATUS.REJECTED,
    message: "Certificat rejeté",
    timestamp: "2024-01-18T09:15:00",
  },
];

export function RecentActivity() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    >
      <Card>
        <CardHeader>
          <CardTitle>Activité Récente</CardTitle>
          <CardDescription>
            Dernières actions sur vos certificats
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            {activities.map((activity) => {
              const Icon = statusIcons[activity.status];
              return (
                <div key={activity.id} className="flex items-start space-x-4">
                  <div
                    className={`mt-1 rounded-full p-2 ${
                      statusColors[activity.status]
                    } bg-opacity-10`}
                  >
                    <Icon
                      className={`h-4 w-4 ${statusColors[activity.status]}`}
                    />
                  </div>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {activity.message}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Certificat #{activity.certificate}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(activity.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
