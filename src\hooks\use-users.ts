"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getUsers, updateUserStatus } from "@/actions/users";
import type { UsersFilters } from "@/types/user";
import { STATUS } from "@/actions/auth/constants";
import { useToast } from "@/components/ui/use-toast";

export function useUsers(filters: UsersFilters = {}) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data, isLoading, error, isFetching } = useQuery({
    queryKey: ["users", filters],
    queryFn: () => getUsers(filters),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const updateStatus = useMutation({
    mutationFn: ({ userId, status }: { userId: string; status: STATUS }) =>
      updateUserStatus(userId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast({
        title: "Succès",
        description: "Le statut de l'utilisateur a été mis à jour",
        variant: "success",
      });
    },
    onError: (error: any) => {
      console.error("Erreur lors de la mise à jour du statut:", error);
      toast({
        title: "Erreur",
        description: error?.message || "Impossible de mettre à jour le statut",
        variant: "error",
      });
    },
  });

  return {
    users: data?.users || [],
    pagination: data?.pagination,
    isLoading,
    isFetching,
    error,
    updateStatus,
    isUpdating: updateStatus.isPending,
  };
}
