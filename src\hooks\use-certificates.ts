"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/components/ui/use-toast";
import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import {
  getCertificates,
  updateCertificateStatus,
} from "@/actions/admin/certificates";

export type CertificateFilters = {
  status?: CERTIFICATE_STATUS;
  search?: string;
  page?: number;
  limit?: number;
};

export function useCertificates(filters: CertificateFilters = {}) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data, isLoading, error } = useQuery({
    queryKey: ["certificates", filters],
    queryFn: () => getCertificates(filters),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const updateStatus = useMutation({
    mutationFn: ({
      certificateId,
      status,
    }: {
      certificateId: string;
      status: CERTIFICATE_STATUS;
    }) => updateCertificateStatus(certificateId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["certificates"] });
      toast({
        title: "Succès",
        description: "Le statut du certificat a été mis à jour",
        variant: "success",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error?.message || "Impossible de mettre à jour le statut",
        variant: "error",
      });
    },
  });

  return {
    certificates: data?.certificates || [],
    pagination: data?.pagination,
    isLoading,
    error,
    updateStatus,
  };
}
