const VALID_REDIRECT_PATHS = [
  "/admin",
  "/admin/certificates",
  "/admin/users",
  "/certificates",
  "/dashboard",
  "/dashboard/certificates",
  "/dashboard/declaration-naissance",
  "/profile",
];

export function getValidSubpath(path: string): string | null {
  // Nettoyer le chemin
  const cleanPath = path.replace("/(authenticated)", "").replace(/\/+/g, "/");

  // Vérifier si le chemin est valide
  if (
    VALID_REDIRECT_PATHS.some((validPath) => cleanPath.startsWith(validPath))
  ) {
    return cleanPath;
  }

  return null;
}

export function isValidRedirectUrl(url: string | null): boolean {
  if (!url) return false;

  try {
    const parsedUrl = new URL(url, window.location.origin);
    return !!getValidSubpath(parsedUrl.pathname);
  } catch {
    return false;
  }
}
