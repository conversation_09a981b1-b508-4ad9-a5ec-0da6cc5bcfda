"use client";

import { RegisterForm } from "@/components/auth/register-form";
import { motion } from "framer-motion";
import Image from "next/image";

export default function RegisterPage() {
  return (
    <div className="relative min-h-screen grid grid-cols-1 lg:grid-cols-2 overflow-hidden">
      {/* Section de gauche - Illustration */}
      <div className="hidden lg:flex relative overflow-hidden">
        {/* Fond dynamique */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#004D40] via-[#00695C] to-[#00796B]">
          <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-10" />
          <div className="absolute inset-0 bg-gradient-radial from-transparent via-black/5 to-black/20" />
        </div>

        {/* Motif géométrique animé */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-yellow-400/20 via-red-500/20 to-green-400/20" />
          <div className="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-green-400/20 via-red-500/20 to-yellow-400/20" />
          <div className="absolute top-0 left-0 h-full w-2 bg-gradient-to-b from-yellow-400/20 via-red-500/20 to-green-400/20" />
          <div className="absolute top-0 right-0 h-full w-2 bg-gradient-to-b from-green-400/20 via-red-500/20 to-yellow-400/20" />
        </div>

        {/* Cercles décoratifs animés */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.2, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
          }}
          className="absolute -left-32 -top-32 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-3xl opacity-30"
        />
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            repeatType: "reverse",
          }}
          className="absolute -right-32 top-1/4 w-96 h-96 bg-red-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.25, 0.15, 0.25],
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            repeatType: "reverse",
          }}
          className="absolute -bottom-32 left-1/3 w-96 h-96 bg-green-500 rounded-full mix-blend-multiply filter blur-3xl opacity-25"
        />

        {/* Contenu principal */}
        <div className="relative z-10 flex flex-col justify-between w-full p-12">
          {/* En-tête avec logo */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex items-center gap-4 mb-12"
          >
            <div className="relative w-16 h-16 bg-white/10 rounded-2xl p-2 backdrop-blur-sm">
              <Image
                src="/logo.png"
                alt="Logo NCR"
                width={48}
                height={48}
                className="object-contain"
                priority
              />
            </div>
            <div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2, duration: 0.6 }}
              >
                <h1 className="text-2xl font-bold text-white">NCR</h1>
                <p className="text-green-100/80">Certificats de Résidence</p>
              </motion.div>
            </div>
          </motion.div>

          {/* Contenu central */}
          <div className="flex-1 flex items-center">
            <div className="space-y-8 max-w-lg">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="space-y-4"
              >
                <h2 className="text-4xl font-bold text-white leading-tight">
                  Créez votre compte{" "}
                  <span className="bg-gradient-to-r from-yellow-400 via-red-500 to-green-400 bg-clip-text text-transparent">
                    NCR
                  </span>
                </h2>
                <p className="text-lg text-white/90 leading-relaxed">
                  Simplifiez vos démarches administratives avec notre plateforme
                  sécurisée de gestion des certificats de résidence.
                </p>
              </motion.div>

              {/* Carte de processus */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
              >
                <h3 className="text-xl font-semibold text-white mb-4">
                  Processus simplifié
                </h3>
                <div className="space-y-4">
                  {[
                    "Créez votre compte en quelques clics",
                    "Remplissez les informations nécessaires",
                    "Recevez votre certificat sous 48h",
                  ].map((step, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.8 + index * 0.1 }}
                      className="flex items-center gap-3"
                    >
                      <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center text-white font-semibold">
                        {index + 1}
                      </div>
                      <p className="text-white/90">{step}</p>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Statistiques */}
              <div className="grid grid-cols-2 gap-4">
                {[
                  { number: "100%", label: "Sécurisé", icon: "🔒" },
                  { number: "24/7", label: "Disponible", icon: "⚡" },
                  { number: "48h", label: "Délai maximum", icon: "⏱" },
                  { number: "100%", label: "Satisfaction garantie", icon: "⭐" },
                ].map((stat, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 1 + index * 0.1 }}
                    className="group p-4 rounded-xl bg-white/10 backdrop-blur-sm border border-white/10 hover:bg-white/[0.15] transition-all duration-300"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{stat.icon}</span>
                      <div>
                        <div className="text-2xl font-bold text-white group-hover:text-yellow-400 transition-colors">
                          {stat.number}
                        </div>
                        <div className="text-sm text-white/80">
                          {stat.label}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.4 }}
            className="mt-12 text-center text-white/60 text-sm"
          >
            &copy; 2024 NCR - Tous droits réservés
          </motion.div>
        </div>
      </div>

      {/* Section de droite - Formulaire */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
        className="relative flex items-center justify-center p-8"
      >
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <motion.h2
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-3xl font-bold tracking-tight bg-gradient-to-r from-[#004D40] via-yellow-600 to-red-600 bg-clip-text text-transparent"
            >
              Créez votre compte
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="mt-2 text-gray-600"
            >
              Remplissez le formulaire ci-dessous pour commencer
            </motion.p>
          </div>

          <div className="relative">
            <div
              className="absolute inset-0 flex items-center"
              aria-hidden="true"
            >
              <div className="w-full border-t border-gray-200" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 text-gray-500 bg-white">
                Vos informations
              </span>
            </div>
          </div>

          <RegisterForm />
        </div>
      </motion.div>
    </div>
  );
}
