import { AttachedDocumentStatus } from "@/schemas/citizen";
import { Models } from "node-appwrite";
import { CERTIFICATE_STATUS, CERTIFICATE_TYPE } from "../auth/constants";

// Types pour les documents
export interface CertificateDocument extends Models.Document {
  type: string;
  fileId: string;
  fileName: string;
  fileSize: string;
  mimeType: string;
  status: AttachedDocumentStatus;
}

export interface Certificate extends Models.Document {
  reference: string;
  citizenId: string;
  chefId: string;
  status: CERTIFICATE_STATUS;
  motif: string;
  documents?: CertificateDocument[];
  createdAt: string;
  updatedAt: string;
  agentId: string | null;
  validite: string;
  verifiedAt: string | null;
  signedAt: string | null;
  deliveredAt: string | null;
  rejectedAt: string | null;
  rejectedBy: string | null;
  rejectionReason?: string;
  expiresAt: null;
  verifiedBy: null;
  verificationNotes: null;
  type: CERTIFICATE_TYPE;
  downloadedAt: string | null;
  signatureFileId?: string | null;
  documentUrl?: string | null;
  downloads?: string[];
  isPaid: boolean;
  price?: number;
  paymentStatus?: string;
}

export interface AssignCertificateParams {
  certificateId: string;
  agentId: string;
}

export interface RejectCertificateParams {
  certificateId: string;
  reason: string;
}

export interface VerifyCertificateParams {
  certificateId: string;
  notes?: string;
}
