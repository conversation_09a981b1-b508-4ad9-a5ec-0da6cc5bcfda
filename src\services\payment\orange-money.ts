import { PAYMENT_CONFIG, PaymentMetadata } from "@/config/payment";
import { createAdminClient } from "@/lib/server/appwrite";
import {
  DATABASE_ID,
  PAYMENTS_METADATA_COLLECTION_ID,
} from "@/lib/server/database";
import axios from "axios";
import crypto from "crypto";
import { ID, Query } from "node-appwrite";
import { z } from "zod";

// Types de réponse attendus
const tokenResponseSchema = z.object({
  access_token: z.string(),
  token_type: z.string(),
  expires_in: z.number(),
});

const paymentResponseSchema = z.object({
  status: z.number(),
  message: z.string(),
  pay_token: z.string(),
  payment_url: z.string(),
  notif_token: z.string().optional(),
});

const statusResponseSchema = z.object({
  status: z.string(),
  order_id: z.string(),
  txnid: z.string().optional(),
});

export class OrangeMoneyService {
  private static instance: OrangeMoneyService;
  private accessToken: string | null = null;
  private tokenExpiry: number | null = null;

  private constructor() {}

  static getInstance(): OrangeMoneyService {
    if (!this.instance) {
      this.instance = new OrangeMoneyService();
    }
    return this.instance;
  }

  private async getAuthToken(): Promise<string> {
    try {
      // Vérifier si le token existe et est encore valide
      if (
        this.accessToken &&
        this.tokenExpiry &&
        Date.now() < this.tokenExpiry
      ) {
        return this.accessToken;
      }

      // // Préparer les credentials en Base64
      // const credentials = Buffer.from(
      //   `${PAYMENT_CONFIG.orangeMoney.clientId}:${PAYMENT_CONFIG.orangeMoney.clientSecret}`
      // ).toString("base64");

      const credentials = PAYMENT_CONFIG.orangeMoney.authorizationHeader;

      // Faire la requête pour obtenir le token
      const response = await axios.post(
        `${PAYMENT_CONFIG.orangeMoney.baseUrl}/oauth/v3/token`,
        "grant_type=client_credentials",
        {
          headers: {
            Authorization: `Basic ${credentials}`,
            "Content-Type": "application/x-www-form-urlencoded",
            Accept: "application/json",
          },
        }
      );

      // Valider la réponse
      const validatedResponse = tokenResponseSchema.parse(response.data);

      // Sauvegarder le token et son expiration
      this.accessToken = validatedResponse.access_token;
      this.tokenExpiry =
        Date.now() + validatedResponse.expires_in * 1000 - 60000; // -1min pour marge de sécurité

      return this.accessToken;
    } catch (error) {
      console.error("Erreur lors de la récupération du token:", error);
      throw new Error("Impossible d'obtenir le token d'authentification");
    }
  }

  async initiatePayment(params: {
    orderId: string;
    amount: number;
    reference: string;
    metadata?: PaymentMetadata;
  }): Promise<{
    paymentUrl: string;
    payToken: string;
  }> {
    try {
      const token = await this.getAuthToken();
      const { databases } = await createAdminClient();

      // Format du montant : doit être un entier sans décimales
      const formattedAmount = Math.round(params.amount).toString();

      // Construction des URLs avec les paramètres nécessaires
      const baseReturnUrl = PAYMENT_CONFIG.orangeMoney.returnUrl;
      const baseCancelUrl = PAYMENT_CONFIG.orangeMoney.cancelUrl;
      const baseNotifUrl = PAYMENT_CONFIG.orangeMoney.notificationUrl;

      const urlParams = new URLSearchParams({
        transactionId: params.reference,
        orderId: params.orderId,
      });

      // Préparer le payload selon les spécifications d'Orange Money
      const payload = {
        merchant_key: String(PAYMENT_CONFIG.orangeMoney.merchantKey), // Conversion explicite en string
        currency: PAYMENT_CONFIG.orangeMoney.currency,
        order_id: params.orderId,
        amount: formattedAmount,
        return_url: `${baseReturnUrl}?${urlParams.toString()}`,
        cancel_url: `${baseCancelUrl}?${urlParams.toString()}`,
        notif_url: `${baseNotifUrl}?${urlParams.toString()}`,
        lang: PAYMENT_CONFIG.orangeMoney.lang,
        reference: params.reference,
      };

      console.log(
        "Payload envoyé à Orange Money:",
        JSON.stringify(payload, null, 2)
      );

      const response = await axios.post(
        `${PAYMENT_CONFIG.orangeMoney.baseUrl}/orange-money-webpay/${PAYMENT_CONFIG.orangeMoney.environment}/v1/webpayment`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          validateStatus: (status) => status < 500, // Pour capturer les erreurs 4xx
        }
      );

      // Gestion détaillée des erreurs
      if (response.status !== 200 && response.status !== 201) {
        console.error("Réponse d'erreur d'Orange Money:", {
          status: response.status,
          data: response.data,
        });
        throw new Error(
          response.data?.message ||
            "Erreur lors de l'initialisation du paiement"
        );
      }

      const validatedResponse = paymentResponseSchema.parse(response.data);

      // Enregistrer les métadonnées dans une collection dédiée
      if (params.metadata) {
        await databases.createDocument(
          DATABASE_ID,
          PAYMENTS_METADATA_COLLECTION_ID,
          ID.unique(),
          {
            paymentReference: params.reference,
            orderId: params.orderId,
            certificateId: params.metadata.certificateId,
            type: params.metadata.type,
            returnUrl: params.metadata.returnUrl,
            createdAt: new Date().toISOString(),
          }
        );
      }

      return {
        paymentUrl: validatedResponse.payment_url,
        payToken: validatedResponse.pay_token,
      };
    } catch (error: any) {
      console.error("Erreur détaillée lors de l'initialisation du paiement:", {
        error: error.response?.data || error,
        status: error.response?.status,
        message: error.message,
        stack: error.stack,
      });

      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.description ||
        error.message ||
        "Impossible d'initialiser le paiement";

      throw new Error(errorMessage);
    }
  }

  async verifyPayment(params: {
    orderId: string;
    amount: number;
    payToken?: string;
  }): Promise<{
    status: string;
    orderId: string;
    transactionId?: string;
    metadata?: PaymentMetadata;
  }> {
    try {
      const { databases } = await createAdminClient();
      const statusResult = await this.checkTransactionStatus(params);

      // Récupérer les métadonnées associées au paiement
      const metadataQuery = await databases.listDocuments(
        DATABASE_ID,
        PAYMENTS_METADATA_COLLECTION_ID,
        [Query.equal("orderId", params.orderId), Query.limit(1)]
      );

      const metadata = metadataQuery.documents[0];

      return {
        ...statusResult,
        metadata: metadata
          ? {
              certificateId: metadata.certificateId,
              type: metadata.type,
              returnUrl: metadata.returnUrl,
            }
          : undefined,
      };
    } catch (error) {
      console.error("Erreur lors de la vérification du paiement:", error);
      throw new Error("Impossible de vérifier le paiement");
    }
  }

  async checkTransactionStatus(params: {
    orderId: string;
    amount: number;
    payToken?: string;
  }): Promise<{
    status: string;
    orderId: string;
    transactionId?: string;
  }> {
    try {
      const token = await this.getAuthToken();

      const response = await axios.post(
        `${PAYMENT_CONFIG.orangeMoney.baseUrl}/orange-money-webpay/${PAYMENT_CONFIG.orangeMoney.environment}/v1/transactionstatus`,
        {
          order_id: params.orderId,
          amount: params.amount,
          pay_token: params.payToken || null,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        }
      );

      const data = response.data;

      console.log({ data });

      const validatedResponse = statusResponseSchema.parse(data);

      return {
        status: validatedResponse.status,
        orderId: validatedResponse.order_id,
        transactionId: validatedResponse.txnid,
      };
    } catch (error) {
      console.error("Erreur lors de la vérification du statut:", error);
      throw new Error("Impossible de vérifier le statut de la transaction");
    }
  }

  async verifyWebhookSignature(
    signature: string,
    payload: string
  ): Promise<boolean> {
    try {
      // Créer un HMAC avec la clé secrète
      const hmac = crypto.createHmac(
        "sha256",
        PAYMENT_CONFIG.orangeMoney.webhookSecret
      );

      // Mettre à jour le HMAC avec le payload brut
      hmac.update(payload);

      // Générer la signature en base64
      const calculatedSignature = hmac.digest("base64");

      // Comparer la signature calculée avec celle reçue
      // Utilisation de crypto.timingSafeEqual pour éviter les attaques timing
      const signatureBuffer = Buffer.from(signature);
      const calculatedBuffer = Buffer.from(calculatedSignature);

      // Les buffers doivent avoir la même longueur pour la comparaison
      if (signatureBuffer.length !== calculatedBuffer.length) {
        return false;
      }

      return crypto.timingSafeEqual(signatureBuffer, calculatedBuffer);
    } catch (error) {
      console.error("Erreur lors de la vérification de la signature:", error);
      return false;
    }
  }
}
