"use client";

import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { motion, AnimatePresence } from "framer-motion";
import { Upload, X, FileText } from "lucide-react";
import Image from "next/image";
import { useToast } from "@/components/ui/use-toast";

interface FileUploadProps {
  endpoint: string;
  value: any;
  onChange: (value: any) => void;
  options?: {
    maxSize?: number;
    acceptedTypes?: string[];
    aspectRatio?: number;
    minWidth?: number;
    minHeight?: number;
    label?: string;
    description?: string;
  };
}

const DEFAULT_MAX_SIZE = 5 * 1024 * 1024; // 5MB
const DEFAULT_ACCEPTED_TYPES = ["image/jpeg", "image/png", "application/pdf"];

export function FileUpload({
  endpoint,
  value,
  onChange,
  options = {},
}: FileUploadProps) {
  const [preview, setPreview] = useState<string | null>(null);
  const { toast } = useToast();

  const {
    maxSize = DEFAULT_MAX_SIZE,
    acceptedTypes = DEFAULT_ACCEPTED_TYPES,
    aspectRatio,
    minWidth,
    minHeight,
    label,
    description,
  } = options;

  // Validation du fichier
  const validateFile = useCallback(
    async (file: File): Promise<boolean> => {
      // Vérification de la taille
      if (file.size > maxSize) {
        toast({
          title: "Fichier trop volumineux",
          description: `La taille maximale autorisée est de ${Math.round(
            maxSize / 1024 / 1024
          )}MB`,
          variant: "warning",
        });
        return false;
      }

      // Vérification du type
      if (!acceptedTypes.includes(file.type)) {
        toast({
          title: "Type de fichier non supporté",
          description: `Les types de fichiers acceptés sont : ${acceptedTypes.join(
            ", "
          )}`,
          variant: "warning",
        });
        return false;
      }

      // Vérifications supplémentaires pour les images
      if (file.type.startsWith("image/")) {
        return new Promise((resolve) => {
          const img = document.createElement("img") as HTMLImageElement;
          img.src = URL.createObjectURL(file);

          img.onload = () => {
            URL.revokeObjectURL(img.src);

            if (minWidth && img.width < minWidth) {
              toast({
                title: "Image trop petite",
                description: `La largeur minimale requise est de ${minWidth}px`,
                variant: "warning",
              });
              resolve(false);
              return;
            }

            if (minHeight && img.height < minHeight) {
              toast({
                title: "Image trop petite",
                description: `La hauteur minimale requise est de ${minHeight}px`,
                variant: "warning",
              });
              resolve(false);
              return;
            }

            if (
              aspectRatio &&
              Math.abs(img.width / img.height - aspectRatio) > 0.1
            ) {
              toast({
                title: "Format d'image incorrect",
                description: `Le ratio d'aspect requis est de ${aspectRatio}`,
                variant: "warning",
              });
              resolve(false);
              return;
            }

            resolve(true);
          };

          img.onerror = () => {
            URL.revokeObjectURL(img.src);
            toast({
              title: "Image invalide",
              description: "Le fichier semble être corrompu",
              variant: "warning",
            });
            resolve(false);
          };
        });
      }

      return true;
    },
    [maxSize, acceptedTypes, aspectRatio, minWidth, minHeight, toast]
  );

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];

      if (file) {
        console.log(`Uploading file for ${endpoint}`);
        const isValid = await validateFile(file);
        if (!isValid) return;

        // Créer l'aperçu pour les images
        if (file.type.startsWith("image/")) {
          const reader = new FileReader();
          reader.onload = () => {
            setPreview(reader.result as string);
          };
          reader.readAsDataURL(file);
        } else {
          setPreview(null);
        }

        onChange(file);
      }
    },
    [onChange, validateFile, endpoint]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
    maxFiles: 1,
    maxSize,
  });

  const removeFile = () => {
    onChange(null);
    setPreview(null);
  };

  const getFileTypeDescription = () => {
    const types = acceptedTypes.map((type) => {
      if (type.startsWith("image/"))
        return type.replace("image/", "").toUpperCase();
      return type.split("/")[1].toUpperCase();
    });
    return `${types.join(", ")} jusqu'à ${Math.round(maxSize / 1024 / 1024)}MB`;
  };

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`
          relative rounded-lg border-2 border-dashed p-6 transition-colors
          ${
            isDragActive
              ? "border-emerald-500 bg-emerald-500/10"
              : "border-neutral-200 hover:border-emerald-500/50"
          }
        `}
      >
        <input {...getInputProps()} />

        <AnimatePresence>
          {value ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-4"
            >
              {preview ? (
                <div className="relative h-40 w-full overflow-hidden rounded-lg">
                  <Image
                    src={preview}
                    alt="Aperçu"
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="flex items-center gap-2 text-sm text-neutral-500">
                  <FileText className="h-4 w-4" />
                  <span>{value.name}</span>
                </div>
              )}

              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  removeFile();
                }}
                className="absolute right-2 top-2 rounded-full bg-red-500/10 p-1 text-red-500 hover:bg-red-500/20"
              >
                <X className="h-4 w-4" />
              </button>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex flex-col items-center justify-center space-y-2 text-center"
            >
              <div className="rounded-full bg-emerald-500/10 p-2">
                <Upload className="h-6 w-6 text-emerald-500" />
              </div>

              <div className="space-y-1">
                <p className="text-sm font-medium">
                  {label || "Glissez-déposez ou cliquez pour sélectionner"}
                </p>
                <p className="text-xs text-neutral-500">
                  {description || getFileTypeDescription()}
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
