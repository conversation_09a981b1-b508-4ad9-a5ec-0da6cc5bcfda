"use server";

import { createAdminClient } from "@/lib/server/appwrite";
import {
  DATABASE_ID,
  CITIZENS_COLLECTION_ID,
  DOCUMENTS_COLLECTION_ID,
  QUARTIERS_COLLECTION_ID,
} from "@/lib/server/database";
import { DOCUMENTS_BUCKET_ID } from "@/lib/server/storage";
import { ID, Query } from "node-appwrite";
import { AttachedDocumentStatus, RegisterFormData } from "@/schemas/citizen";
import { ROLES, STATUS } from "./constants";
import { checkUserExists } from "./user-account-handler";
import { generateCitizenId } from "@/lib/utils/generate-citizen-id";

export async function register(formData: RegisterFormData) {
  try {
    // 1. Vérifications préliminaires
    const telephoneCheck = await checkUserExists(
      "telephone",
      formData.telephone
    );
    if (telephoneCheck.exists) {
      return { success: false, error: telephoneCheck.error };
    }

    const emailCheck = await checkUserExists("email", formData.email);
    if (emailCheck.exists) {
      return { success: false, error: emailCheck.error };
    }

    // 2. Création du client Appwrite
    const { account, databases, storage, users } = await createAdminClient();

    // 3. Vérification du quartier et de son chef
    const { documents: quartiers, total } = await databases.listDocuments(
      DATABASE_ID,
      QUARTIERS_COLLECTION_ID,
      [
        // Utilisation de l'index fulltext pour une recherche optimisée
        Query.search("nom", formData.quartier),
        // Assurer une correspondance exacte avec le nom complet
        Query.equal("nom", formData.quartier),
        // Limiter à 1 résultat pour optimiser
        Query.limit(1),
      ]
    );

    if (total === 0) {
      return {
        success: false,
        error: "Le quartier spécifié n'existe pas.",
      };
    }

    const quartier = quartiers[0];

    // Vérification de l'existence d'un chef dans le quartier
    if (!quartier.chefId) {
      return {
        success: false,
        error:
          "Impossible de s'inscrire dans ce quartier : aucun chef n'y est assigné. Veuillez contacter l'administration.",
      };
    }

    // 4. Création du compte utilisateur
    const user = await account.create(
      ID.unique(),
      formData.email,
      formData.motDePasse,
      `${formData.prenom} ${formData.nom}`
    );

    // 5. Configuration des préférences utilisateur
    await users.updatePrefs(user.$id, {
      role: ROLES.CITIZEN,
      status: STATUS.PENDING,
      avatarUrl: null,
    });

    const uniqueId = ID.unique();
    // 6. Préparation des données citoyennes
    const citizenData = {
      userId: user.$id,
      nom: formData.nom,
      prenom: formData.prenom,
      dateNaissance: formData.dateNaissance,
      lieuNaissance: formData.lieuNaissance,
      nomPere: formData.nomPere,
      nomMere: formData.nomMere,
      nationalite: formData.nationalite,
      profession: formData.profession,
      telephone: formData.telephone,
      email: formData.email,
      status: STATUS.PENDING,
      role: ROLES.CITIZEN,
      carteElecteur: formData.carteElecteur,
      adressePrecise: formData.adressePrecise,
      dateInstallation: formData.dateInstallation,
      numeroBatiment: formData.numeroBatiment,
      proprietaireBatiment: formData.proprietaireBatiment,
      quartier: quartier.nom,
      numeroIdentificationUnique: generateCitizenId(
        new Date(formData.dateNaissance),
        new Date(),
        uniqueId
      ),
      chefId: quartier.chefId, // Stockage de la référence au chef
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // 7. Création du profil citoyen
    const citizen = await databases.createDocument(
      DATABASE_ID,
      CITIZENS_COLLECTION_ID,
      uniqueId,
      citizenData
    );

    // 8. Traitement des documents
    const documentTypes = {
      pieceIdentite: "Pièce d'identité",
      extraitNaissance: "Extrait d'acte de naissance",
    } as const;

    const documentIds = [];
    for (const [key, type] of Object.entries(documentTypes)) {
      const file = formData[key as keyof typeof documentTypes];
      if (file) {
        // Upload du fichier
        const uploadedFile = await storage.createFile(
          DOCUMENTS_BUCKET_ID,
          ID.unique(),
          file
        );

        // Enregistrement des métadonnées
        const document = await databases.createDocument(
          DATABASE_ID,
          DOCUMENTS_COLLECTION_ID,
          ID.unique(),
          {
            type,
            fileId: uploadedFile.$id,
            fileName: file.name,
            fileSize: file.size.toString(),
            mimeType: file.type,
            status: AttachedDocumentStatus.PENDING,
          }
        );

        documentIds.push(document.$id);
      }
    }

    // 9. Mise à jour du citoyen avec les documents
    if (documentIds.length > 0) {
      await databases.updateDocument(
        DATABASE_ID,
        CITIZENS_COLLECTION_ID,
        citizen.$id,
        { documents: documentIds }
      );
    }

    return { success: true };
  } catch (error: any) {
    console.error("Erreur lors de l'inscription:", error);
    return {
      success: false,
      error: error?.message ?? "Une erreur est survenue lors de l'inscription",
    };
  }
}
