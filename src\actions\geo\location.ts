"use server";

import { createAdminClient } from "@/lib/server/appwrite";
import { DATABASE_ID, QUARTIERS_COLLECTION_ID } from "@/lib/server/database";
import { Query } from "node-appwrite";

// Type pour les données de localisation
export type GeoData = {
  regions: string[];         // Liste des régions disponibles
  prefectures: string[];     // Liste des préfectures de la région sélectionnée
  communes: string[];        // Liste des communes de la préfecture sélectionnée
  sousPrefectures: string[]; // Liste des sous-préfectures de la commune sélectionnée
  quartiers: string[];       // Liste des quartiers de la commune sélectionnée
};

// Type pour les filtres de sélection
export type LocationFilters = {
  region?: string;          // Région sélectionnée
  prefecture?: string;      // Préfecture sélectionnée
  commune?: string;         // Commune sélectionnée
  sousPrefecture?: string;  // Sous-préfecture sélectionnée
};

export async function getLocationData(filters?: LocationFilters) {
  try {
    const { databases } = await createAdminClient();
    const queries: string[] = [];

    // Construction des filtres en cascade
    if (filters?.region) {
      queries.push(Query.equal("region", filters.region));
      if (filters.prefecture) {
        queries.push(Query.equal("prefecture", filters.prefecture));
        if (filters.commune) {
          queries.push(Query.equal("commune", filters.commune));
        }
      }
    }

    const { documents } = await databases.listDocuments(
      DATABASE_ID,
      QUARTIERS_COLLECTION_ID,
      queries
    );

    if (documents.length === 0) {
      return {
        success: false,
        error: "Aucune donnée trouvée pour ces critères"
      };
    }

    // Extraction des données selon le niveau de filtrage
    const data: Partial<GeoData> = {};

    // Sans filtre : liste toutes les régions
    if (!filters?.region) {
      data.regions = [...new Set(documents.map(doc => doc.region))];
    }
    // Avec région : liste les préfectures de cette région
    else if (!filters.prefecture) {
      data.prefectures = [...new Set(documents
        .filter(doc => doc.region === filters.region)
        .map(doc => doc.prefecture))];
    }
    // Avec préfecture : liste les communes de cette préfecture
    else if (!filters.commune) {
      data.communes = [...new Set(documents
        .filter(doc => doc.prefecture === filters.prefecture)
        .map(doc => doc.commune))];
    }
    // Avec commune : liste à la fois les sous-préfectures ET les quartiers
    else {
      // Filtrer les documents pour la commune sélectionnée
      const communeDocs = documents.filter(doc => doc.commune === filters.commune);

      // Extraire les sous-préfectures
      data.sousPrefectures = [...new Set(communeDocs.map(doc => doc.sousPrefecture))];

      // Extraire tous les quartiers de la commune
      data.quartiers = [...new Set(communeDocs.map(doc => doc.nom))];
    }

    // Tri alphabétique du tableau résultant
    Object.keys(data).forEach(key => {
      if (Array.isArray(data[key as keyof GeoData])) {
        (data[key as keyof GeoData] as string[]) = (data[key as keyof GeoData] as string[])
          .sort((a, b) => a.localeCompare(b, 'fr', { sensitivity: 'base' }));
      }
    });

    return { success: true, data };
  } catch (error) {
    console.error("Erreur lors de la récupération des données géographiques:", error);
    return {
      success: false,
      error: "Erreur lors de la récupération des données géographiques"
    };
  }
}