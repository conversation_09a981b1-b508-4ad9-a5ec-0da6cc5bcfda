"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";

interface CompanySpotlightProps {
  delay?: number;
}

export function CompanySpotlight({ delay = 0 }: CompanySpotlightProps) {
  return (
    <div className="relative px-4 sm:px-6 lg:px-8">
      {/* Fond décoratif */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#004D40]/5 to-transparent -z-10 rounded-3xl" />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center py-8 lg:py-12">
        {/* Image et Logo */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay }}
          className="relative mx-auto lg:mx-0 w-full max-w-2xl lg:max-w-none"
        >
          <div className="relative aspect-[4/3] lg:aspect-video overflow-hidden rounded-2xl shadow-lg">
            <Image
              src="/images/ouestech/office.jpg"
              alt="Bureaux de Ouestech"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
          <div className="absolute -bottom-4 md:-bottom-6 -right-4 md:-right-6 bg-white p-3 md:p-4 rounded-xl shadow-lg">
            <Image
              src="/images/ouestech/logo_ouestech.jpg"
              alt="Logo Ouestech"
              width={100}
              height={33}
              className="h-auto w-auto max-w-[80px] md:max-w-[100px]"
            />
          </div>
        </motion.div>

        {/* Contenu */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: delay + 0.2 }}
          className="space-y-4 md:space-y-6"
        >
          <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-[#004D40]">
            Développé par Ouestech
          </h3>
          <p className="text-sm md:text-base text-neutral-600">
            NCR (Numérisation des Certificats de Résidence) est une solution
            innovante conçue et développée par Ouestech, une entreprise leader
            dans la transformation digitale en Afrique de l'Ouest.
          </p>
          <div className="space-y-3 md:space-y-4">
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-[#004D40]/10 text-[#004D40] shrink-0">
                <svg
                  className="w-4 h-4 md:w-5 md:h-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-neutral-900 text-sm md:text-base">
                  Expertise Technologique
                </h4>
                <p className="text-xs md:text-sm text-neutral-600">
                  Plus de 5 ans d'expérience dans la transformation digitale
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-[#004D40]/10 text-[#004D40] shrink-0">
                <svg
                  className="w-4 h-4 md:w-5 md:h-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"
                  />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-neutral-900 text-sm md:text-base">
                  Solutions Sur Mesure
                </h4>
                <p className="text-xs md:text-sm text-neutral-600">
                  Des solutions adaptées aux besoins spécifiques de chaque
                  client
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-[#004D40]/10 text-[#004D40] shrink-0">
                <svg
                  className="w-4 h-4 md:w-5 md:h-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
                  />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-neutral-900 text-sm md:text-base">
                  Présence Internationale
                </h4>
                <p className="text-xs md:text-sm text-neutral-600">
                  Une expertise reconnue en Afrique et à l'international
                </p>
              </div>
            </div>
          </div>
          <div className="pt-2 md:pt-4">
            <Link
              href="https://ouestech.com"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 text-[#004D40] hover:text-[#00695C] transition-colors text-sm md:text-base"
            >
              <span>En savoir plus sur Ouestech</span>
              <svg
                className="w-4 h-4 md:w-5 md:h-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M14 5l7 7m0 0l-7 7m7-7H3"
                />
              </svg>
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
