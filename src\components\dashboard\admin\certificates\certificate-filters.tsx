"use client";

import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import type { CertificateFilters } from "@/hooks/use-certificates";
import { cn } from "@/lib/utils/cn";

// Labels des statuts pour l'affichage
const statusLabels: Record<CERTIFICATE_STATUS, string> = {
  [CERTIFICATE_STATUS.DRAFT]: "Brouillon",
  [CERTIFICATE_STATUS.SUBMITTED]: "<PERSON><PERSON><PERSON>",
  [CERTIFICATE_STATUS.PENDING]: "En attente",
  [CERTIFICATE_STATUS.VERIFIED]: "Vérifié",
  [CERTIFICATE_STATUS.APPROVED]: "Approuvé",
  [CERTIFICATE_STATUS.READY]: "<PERSON>r<PERSON><PERSON>",
  [CERTIFICATE_STATUS.SIGNED]: "<PERSON><PERSON>",
  [CERTIFICATE_STATUS.DELIVERED]: "Déliv<PERSON>",
  [CERTIFICATE_STATUS.REJECTED]: "Rejeté",
  [CERTIFICATE_STATUS.EXPIRED]: "Expiré",
};

// Ordre d'affichage des statuts dans le filtre
const statusOrder: CERTIFICATE_STATUS[] = [
  CERTIFICATE_STATUS.DRAFT,
  CERTIFICATE_STATUS.SUBMITTED,
  CERTIFICATE_STATUS.PENDING,
  CERTIFICATE_STATUS.VERIFIED,
  CERTIFICATE_STATUS.APPROVED,
  CERTIFICATE_STATUS.READY,
  CERTIFICATE_STATUS.SIGNED,
  CERTIFICATE_STATUS.DELIVERED,
  CERTIFICATE_STATUS.REJECTED,
  CERTIFICATE_STATUS.EXPIRED,
];

interface CertificateFiltersProps {
  filters: {
    status: CERTIFICATE_STATUS | undefined;
    search: string;
    page: number;
    limit: number;
  };
  onFiltersChange: (filters: Partial<CertificateFilters>) => void;
}

export function CertificateFilters({ filters, onFiltersChange }: CertificateFiltersProps) {
  const handleStatusChange = (value: string) => {
    onFiltersChange({
      status: value === "all" ? undefined : value as CERTIFICATE_STATUS
    });
  };

  return (
    <div className="flex flex-col sm:flex-row gap-4">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-neutral-500" />
        <Input
          placeholder="Rechercher un certificat..."
          value={filters.search}
          onChange={(e) => onFiltersChange({ search: e.target.value })}
          className="pl-9"
        />
      </div>
      <Select
        value={filters.status || "all"}
        onValueChange={handleStatusChange}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Filtrer par statut">
            {filters.status ? statusLabels[filters.status] : "Tous les statuts"}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tous les statuts</SelectItem>
          {statusOrder.map((status) => (
            <SelectItem
              key={status}
              value={status}
              className="flex items-center gap-2"
            >
              <span className={cn(
                "w-2 h-2 rounded-full",
                {
                  "bg-neutral-400": status === CERTIFICATE_STATUS.DRAFT,
                  "bg-yellow-500": status === CERTIFICATE_STATUS.PENDING,
                  "bg-blue-500": status === CERTIFICATE_STATUS.VERIFIED,
                  "bg-emerald-500": [
                    CERTIFICATE_STATUS.APPROVED,
                    CERTIFICATE_STATUS.SIGNED,
                    CERTIFICATE_STATUS.DELIVERED
                  ].includes(status),
                  "bg-red-500": status === CERTIFICATE_STATUS.REJECTED,
                  "bg-neutral-500": status === CERTIFICATE_STATUS.EXPIRED,
                }
              )} />
              {statusLabels[status]}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}