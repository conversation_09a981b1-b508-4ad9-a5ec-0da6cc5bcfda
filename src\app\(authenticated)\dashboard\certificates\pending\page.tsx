"use client";

import { CERTIFICATE_TYPE } from "@/actions/auth/constants";
import { getPendingCertificates } from "@/actions/citizen/certificates";
import { Certificate } from "@/actions/types";
import { CertificateActions } from "@/components/certificates/certificate-actions";
import { StatusBadge } from "@/components/certificates/status-badge";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { motion } from "framer-motion";
import { CalendarRange, Clock, Filter, Loader2, Search } from "lucide-react";
import { useState } from "react";

const TYPE_LABELS = {
  [CERTIFICATE_TYPE.RESIDENCE]: "Certificat de Résidence",
  [CERTIFICATE_TYPE.BIRTH_CERTIFICATE]: "Certificat de Naissance",
  [CERTIFICATE_TYPE.DEATH_CERTIFICATE]: "Certificat de Décès",
  [CERTIFICATE_TYPE.MARRIAGE_CERTIFICATE]: "Certificat de Mariage",
  [CERTIFICATE_TYPE.DIVORCE_CERTIFICATE]: "Certificat de Divorce",
  [CERTIFICATE_TYPE.CHILD_BIRTH_CERTIFICATE]:
    "Certificat de Naissance d'Enfant",
  [CERTIFICATE_TYPE.CHILD_DEATH_CERTIFICATE]: "Certificat de Décès d'Enfant",
} as const;

const TYPE_COLORS = {
  [CERTIFICATE_TYPE.RESIDENCE]: "from-blue-500 to-blue-600",
  [CERTIFICATE_TYPE.BIRTH_CERTIFICATE]: "from-emerald-500 to-emerald-600",
  [CERTIFICATE_TYPE.DEATH_CERTIFICATE]: "from-purple-500 to-purple-600",
  [CERTIFICATE_TYPE.MARRIAGE_CERTIFICATE]: "from-pink-500 to-pink-600",
  [CERTIFICATE_TYPE.DIVORCE_CERTIFICATE]: "from-orange-500 to-orange-600",
  [CERTIFICATE_TYPE.CHILD_BIRTH_CERTIFICATE]: "from-yellow-500 to-yellow-600",
  [CERTIFICATE_TYPE.CHILD_DEATH_CERTIFICATE]: "from-red-500 to-red-600",
} as const;

const ITEMS_PER_PAGE = 10;

export default function PendingCertificatesPage() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const { data, isLoading } = useQuery<{
    certificates: Certificate[];
    total: number;
  }>({
    queryKey: ["pending-certificates", searchQuery, page, sortBy, sortOrder],
    queryFn: async () => {
      const response = await getPendingCertificates({
        search: searchQuery,
        limit: ITEMS_PER_PAGE,
        offset: (page - 1) * ITEMS_PER_PAGE,
        sortBy,
        sortOrder,
      });

      return {
        certificates: (response.certificates || []) as unknown as Certificate[],
        total: response.pagination.total,
      };
    },
    placeholderData: (previousData) => previousData,
    staleTime: 1000 * 60, // 1 minute
  });

  const columns = [
    {
      header: "Référence",
      accessorKey: "reference",
      cell: (props: any) => (
        <div className="font-medium text-neutral-900">{props.getValue()}</div>
      ),
    },
    {
      header: "Type",
      accessorKey: "type",
      cell: (props: any) => {
        const type = props.getValue() as keyof typeof TYPE_LABELS;
        return (
          <div className="relative group">
            <div
              className={`absolute inset-0 rounded-xl bg-gradient-to-br ${TYPE_COLORS[type]} opacity-[0.08] blur-sm
              group-hover:opacity-[0.12] group-hover:blur-md transition-all duration-300`}
            />
            <div className="relative px-3 py-1 rounded-xl bg-white/60 backdrop-blur-sm border border-neutral-200/60">
              <span
                className={`text-sm font-medium bg-gradient-to-br ${TYPE_COLORS[type]} bg-clip-text text-transparent`}
              >
                {TYPE_LABELS[type] || type}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      header: "Statut",
      accessorKey: "status",
      cell: (props: any) => (
        <StatusBadge
          status={props.getValue()}
          timestamp={props.row.original.updatedAt}
        />
      ),
    },
    {
      header: "Demandeur",
      accessorKey: "citizenName",
      cell: (props: any) => (
        <div className="flex items-center gap-2">
          <div className="font-medium text-neutral-900">
            {props.getValue() || "N/A"}
          </div>
        </div>
      ),
    },
    {
      header: "Date de demande",
      accessorKey: "createdAt",
      cell: (props: any) => (
        <div className="flex items-center gap-2 text-neutral-500">
          <CalendarRange className="h-4 w-4" />
          {formatDistanceToNow(new Date(props.getValue()), {
            addSuffix: true,
            locale: fr,
          })}
        </div>
      ),
    },
    {
      header: "Quartier",
      accessorKey: "quartierInfo",
      cell: (props: any) => (
        <div className="flex items-center gap-2">
          <div className="text-sm text-neutral-700 font-medium">
            {props.getValue() || "N/A"}
          </div>
        </div>
      ),
    },
    {
      header: "Actions",
      id: "actions",
      cell: (props: any) => {
        const certificate = props.row.original;
        return (
          <div className="flex items-center justify-end gap-2">
            <CertificateActions
              certificate={certificate}
              userRole={user?.prefs?.role as "admin" | "chef" | "agent"}
              alwaysVisible={true}
            />
          </div>
        );
      },
    },
  ];

  const rowClassName =
    "group/row hover:bg-neutral-50/80 transition-all duration-200 backdrop-blur-sm";

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5">
      {/* Fond décoratif */}
      <div className="absolute inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(0,77,64,0.08),transparent_50%)]" />
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-[0.02]" />
      </div>

      <div className="container mx-auto px-6 py-8 space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60 p-8"
        >
          <div className="absolute -top-12 -right-12 w-32 h-32 bg-accent-primary/5 rounded-full blur-2xl" />
          <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-accent-secondary/5 rounded-full blur-2xl" />

          <div className="relative flex flex-col gap-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h1 className="text-2xl font-bold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent">
                  Certificats en attente
                </h1>
                <p className="text-sm text-neutral-500">
                  Gérez les certificats qui nécessitent votre attention
                </p>
              </div>
              <div className="flex items-center gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-neutral-400" />
                  <Input
                    placeholder="Rechercher par référence..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 w-[300px] bg-white/50 backdrop-blur-sm border-neutral-200/60"
                  />
                </div>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-[180px] bg-white/50 backdrop-blur-sm border-neutral-200/60">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue placeholder="Trier par" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="createdAt">Date de création</SelectItem>
                    <SelectItem value="reference">Référence</SelectItem>
                    <SelectItem value="type">Type</SelectItem>
                    <SelectItem value="status">Statut</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() =>
                    setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                  }
                  className="bg-white/50 backdrop-blur-sm border-neutral-200/60"
                >
                  {sortOrder === "asc" ? "↑" : "↓"}
                </Button>
              </div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60"
        >
          {isLoading ? (
            <div className="flex flex-col items-center justify-center h-[400px] gap-4">
              <Loader2 className="w-8 h-8 animate-spin text-accent-primary" />
              <p className="text-sm text-neutral-500">
                Chargement des certificats...
              </p>
            </div>
          ) : data?.certificates.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-[400px] gap-4">
              <Clock className="w-12 h-12 text-neutral-400" />
              <div className="text-center">
                <p className="text-lg font-medium text-neutral-900">
                  Aucun certificat en attente
                </p>
                <p className="text-sm text-neutral-500">
                  Tous les certificats ont été traités
                </p>
              </div>
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={data?.certificates || []}
              pageCount={Math.ceil((data?.total || 0) / ITEMS_PER_PAGE)}
              currentPage={page}
              onPageChange={setPage}
              rowClassName={rowClassName}
            />
          )}
        </motion.div>
      </div>
    </div>
  );
}
