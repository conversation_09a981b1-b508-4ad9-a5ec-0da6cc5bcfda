"use client";

import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { getCertificateWithFullData } from "@/actions/certificates";
import { deliverCertificate } from "@/actions/chef/certificates";
import { CertificateDetails } from "@/components/certificates/certificate-details";
import { CertificateTimeline } from "@/components/certificates/certificate-timeline";
import { DashboardHeader } from "@/components/dashboard/header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Loader2,
  Send,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useCallback, useEffect } from "react";

export default function DeliverCertificatePage() {
  const params = useParams();
  const certificateId = params?.id as string;
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Récupération des données du certificat - optimisée
  const { data: certificateData, isLoading: isLoadingCertificate } = useQuery({
    queryKey: ["certificate", certificateId],
    queryFn: () => getCertificateWithFullData(certificateId),
    enabled: !!certificateId && !!user,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });

  const certificate = certificateData?.certificate;
  const isDelivered = certificate?.status === CERTIFICATE_STATUS.DELIVERED;

  // Mutation optimisée pour la délivrance avec retry et timeout
  const deliverMutation = useMutation({
    mutationFn: async (certificateId: string) => {
      // Add overall timeout for the entire operation
      return Promise.race([
        deliverCertificate(certificateId),
        new Promise<never>((_, reject) =>
          setTimeout(
            () => reject(new Error("Opération expirée - Veuillez réessayer")),
            90000
          )
        ),
      ]);
    },
    retry: (failureCount, error) => {
      // Retry up to 2 times for network/timeout errors
      if (failureCount < 2) {
        const errorMessage = error?.message || "";
        return (
          errorMessage.includes("timeout") ||
          errorMessage.includes("network") ||
          errorMessage.includes("fetch")
        );
      }
      return false;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000),
    onSuccess: () => {
      // Invalidation ciblée des queries
      queryClient.invalidateQueries({
        queryKey: ["certificate", certificateId],
      });
      queryClient.invalidateQueries({ queryKey: ["certificates"] });

      toast({
        title: "Certificat délivré",
        description: "Le certificat a été délivré avec succès.",
      });

      // Redirection immédiate
      router.push(`/dashboard/certificates/${certificateId}`);
    },
    onError: (error: any) => {
      console.error("Delivery error:", error);

      let errorMessage = "Une erreur est survenue lors de la délivrance.";
      let errorDescription = "";

      if (error?.message) {
        if (
          error.message.includes("timeout") ||
          error.message.includes("expirée")
        ) {
          errorMessage = "Délai d'attente dépassé";
          errorDescription =
            "L'opération prend plus de temps que prévu. Veuillez réessayer.";
        } else if (error.message.includes("PDF")) {
          errorMessage = "Erreur de génération du document";
          errorDescription =
            "Impossible de générer le certificat PDF. Vérifiez les données et réessayez.";
        } else if (error.message.includes("permissions")) {
          errorMessage = "Permissions insuffisantes";
          errorDescription = error.message;
        } else {
          errorMessage = "Erreur lors de la délivrance";
          errorDescription = error.message;
        }
      }

      toast({
        title: errorMessage,
        description: errorDescription,
        variant: "destructive",
      });
    },
  });

  // Vérification des permissions
  useEffect(() => {
    if (certificate && user) {
      const canDeliver =
        user?.prefs?.role === "admin" ||
        (user?.prefs?.role === "chef" &&
          certificate.status === CERTIFICATE_STATUS.SIGNED &&
          certificate.chefId === user.$id);

      if (!canDeliver && !isDelivered) {
        toast({
          title: "Accès non autorisé",
          description:
            "Vous n'avez pas les permissions nécessaires pour délivrer ce certificat.",
          variant: "destructive",
        });
        router.push("/dashboard/certificates");
        return;
      }
    }
  }, [certificate, user, isDelivered, toast, router]);

  // Fonction de délivrance optimisée avec gestion d'état améliorée
  const handleDeliver = useCallback(async () => {
    if (!certificate || isDelivered || deliverMutation.isPending) return;

    // Show initial loading toast
    toast({
      title: "Délivrance en cours",
      description: "Génération du certificat PDF et finalisation...",
    });

    // Start the delivery process
    deliverMutation.mutate(certificateId);
  }, [certificate, isDelivered, deliverMutation, certificateId, toast]);

  if (isLoadingCertificate || !certificate) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-accent-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-8 p-8">
      <DashboardHeader
        heading={`Délivrance du certificat ${certificate.reference}`}
        text={
          isDelivered
            ? "Certificat délivré"
            : "Vérification finale et délivrance du certificat"
        }
      >
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() =>
              router.push(`/dashboard/certificates/${certificateId}`)
            }
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour aux détails
          </Button>
          {!isDelivered && (
            <Button
              variant="default"
              onClick={handleDeliver}
              disabled={deliverMutation.isPending || isDelivered}
              className="bg-green-600 hover:bg-green-700"
            >
              {deliverMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin text-accent-primary" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              Délivrer le certificat
            </Button>
          )}
        </div>
      </DashboardHeader>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Informations principales */}
        <div className="lg:col-span-2 space-y-6">
          <CertificateDetails
            certificate={certificate}
            userRole={user?.prefs?.role as "admin" | "chef"}
          />

          {/* Delivery Status Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {isDelivered ? (
                  <>
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Certificat délivré
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-5 w-5 text-orange-600" />
                    Prêt pour la délivrance
                  </>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isDelivered ? (
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Ce certificat a été délivré avec succès. Le citoyen peut
                    maintenant le télécharger et l'utiliser selon ses besoins.
                  </p>
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    <span>
                      Délivré le{" "}
                      {new Date(
                        certificate.deliveredAt || ""
                      ).toLocaleDateString("fr-FR")}
                    </span>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Le certificat est prêt à être délivré. Une fois délivré, le
                    citoyen pourra le télécharger et l'utiliser.
                  </p>

                  {/* Progress indicator when delivering */}
                  {deliverMutation.isPending && (
                    <div className="space-y-3">
                      <div className="flex items-center gap-2 text-sm text-blue-600">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>
                          {deliverMutation.failureCount > 0
                            ? `Tentative ${
                                deliverMutation.failureCount + 1
                              }/3...`
                            : "Génération du certificat en cours..."}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-1000 animate-pulse"
                          style={{ width: "60%" }}
                        ></div>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Cette opération peut prendre jusqu'à 2 minutes. Veuillez
                        patienter...
                      </p>
                    </div>
                  )}

                  <div className="flex items-center gap-4">
                    <Button
                      onClick={handleDeliver}
                      disabled={deliverMutation.isPending}
                      className="bg-green-600 hover:bg-green-700 disabled:opacity-50"
                    >
                      {deliverMutation.isPending ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Send className="h-4 w-4 mr-2" />
                      )}
                      {deliverMutation.isPending
                        ? "Délivrance en cours..."
                        : "Délivrer maintenant"}
                    </Button>

                    {deliverMutation.isPending && (
                      <Button
                        variant="outline"
                        onClick={() =>
                          router.push(
                            `/dashboard/certificates/${certificateId}`
                          )
                        }
                        className="text-sm"
                      >
                        Annuler et revenir
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Timeline et vérifications */}
        <div className="space-y-6">
          <CertificateTimeline certificate={certificate} />

          <Card>
            <CardHeader>
              <CardTitle>Liste de vérification</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>Informations du citoyen vérifiées</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>Documents justificatifs validés</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>Signature du chef de quartier confirmée</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>Prêt pour la délivrance</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
