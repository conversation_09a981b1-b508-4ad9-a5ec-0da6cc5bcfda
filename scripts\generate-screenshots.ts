import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON>, KnownDevices } from "puppeteer";
import path from "path";
import fs from "fs/promises";
import { APP_URL } from "../src/config/env";

// Configuration des captures d'écran
const SCREENSHOTS_CONFIG = {
  desktop: [
    {
      name: "desktop-1",
      width: 1920,
      height: 1080,
      path: "/",
      label: "Page d'accueil sur ordinateur",
    },
    // {
    //   name: "desktop-2",
    //   width: 1920,
    //   height: 1080,
    //   path: "/dashboard",
    //   label: "Tableau de bord sur ordinateur",
    //   auth: true,
    // }
  ],
  mobile: [
    {
      name: "mobile-1",
      width: 750,
      height: 1334,
      path: "/",
      label: "Page d'accueil sur mobile",
    },
    // {
    //   name: "mobile-2",
    //   width: 750,
    //   height: 1334,
    //   path: "/dashboard",
    //   label: "Tableau de bord sur mobile",
    //   auth: true,
    // }
  ]
};

const SCREENSHOTS_DIR = path.join(process.cwd(), "public", "screenshots");

async function setupPage(browser: Browser, viewport: { width: number; height: number }): Promise<Page> {
  const page = await browser.newPage();
  await page.setViewport(viewport);

  // Intercepter les requêtes de navigation pour gérer le mode développement
  await page.setRequestInterception(true);
  page.on('request', (request) => {
    if (request.url().includes('localhost') || request.url().includes('127.0.0.1')) {
      request.continue();
    } else {
      request.respond({
        status: 200,
        contentType: 'text/plain',
        body: 'Mocked external request'
      });
    }
  });

  return page;
}

async function authenticatePage(page: Page) {
  // Exemple d'authentification - à adapter selon votre système
  await page.goto(`${APP_URL}/connexion`);
  await page.type('input[name="email"]', process.env.TEST_USER_EMAIL || '');
  await page.type('input[name="password"]', process.env.TEST_USER_PASSWORD || '');
  await page.click('button[type="submit"]');
  await page.waitForNavigation();
}

async function captureScreenshot(
  page: Page,
  config: {
    name: string;
    path: string;
    width: number;
    height: number;
    auth?: boolean;
  }
) {
  try {
    if (config.auth) {
      await authenticatePage(page);
    }

    await page.goto(`${APP_URL}${config.path}`, {
      waitUntil: 'networkidle0',
    });

    // Attendre que le contenu soit chargé
    await page.waitForSelector('main', { timeout: 5000 });

    // Attendre les animations
    await new Promise(resolve => setTimeout(resolve, 1000));

    const screenshotPath = path.join(SCREENSHOTS_DIR, `${config.name}.png`);
    await page.screenshot({
      path: screenshotPath,
      fullPage: false,
      clip: {
        x: 0,
        y: 0,
        width: config.width,
        height: config.height
      }
    });

    console.log(`✓ Capture ${config.name} générée (${config.width}x${config.height})`);
  } catch (error) {
    console.error(`❌ Erreur lors de la capture ${config.name}:`, error);
  }
}

async function generateScreenshots() {
  try {
    // Créer le dossier des captures s'il n'existe pas
    await fs.mkdir(SCREENSHOTS_DIR, { recursive: true });
    console.log("📁 Dossier screenshots créé ou vérifié");

    const browser = await puppeteer.launch({
      headless: true,
      defaultViewport: null,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    // Générer les captures desktop
    for (const config of SCREENSHOTS_CONFIG.desktop) {
      const page = await setupPage(browser, {
        width: config.width,
        height: config.height
      });
      await captureScreenshot(page, config);
      await page.close();
    }

    // Générer les captures mobile
    for (const config of SCREENSHOTS_CONFIG.mobile) {
      const page = await setupPage(browser, {
        width: config.width,
        height: config.height
      });
      // Utiliser KnownDevices pour l'émulation mobile
      await page.emulate(KnownDevices['iPhone 15 Pro Max']);
      await captureScreenshot(page, config);
      await page.close();
    }

    await browser.close();
    console.log("\n✨ Génération des captures d'écran terminée avec succès!");
  } catch (error) {
    console.error("❌ Erreur lors de la génération des captures:", error);
    process.exit(1);
  }
}

// Exécuter le script
generateScreenshots();