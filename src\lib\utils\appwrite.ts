import { APPWRITE_ENDPOINT, APPWRITE_PROJECT_ID } from "@/config/env";
import { DOCUMENTS_BUCKET_ID } from "../server/storage";

export const getAppwriteFileUrl = (fileId: string) => {
  const publicEndpoint = APPWRITE_ENDPOINT;
  const appwriteProject = APPWRITE_PROJECT_ID;
  const appWriteBucket = DOCUMENTS_BUCKET_ID;

  if (!publicEndpoint || !appwriteProject || !appWriteBucket) {
    throw new Error("Configuration Appwrite manquante");
  }

  return `${publicEndpoint}/storage/buckets/${appWriteBucket}/files/${fileId}/view?project=${appwriteProject}`;
};
