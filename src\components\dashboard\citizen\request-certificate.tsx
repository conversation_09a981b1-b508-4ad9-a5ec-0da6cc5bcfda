"use client";

import { createCertificateRequest } from "@/actions/citizen/certificates";
import { initiatePayment } from "@/actions/payment";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { PAYMENT_CONFIG } from "@/config/payment";
import { useUser } from "@/hooks/use-user";
import { formatBytes } from "@/lib/utils/format";
import { AnimatePresence, motion } from "framer-motion";
import { CreditCard, FileText, Loader2, Upload } from "lucide-react";
import { useState } from "react";
import { useDropzone } from "react-dropzone";

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const MAX_FILES = 2;
const ACCEPTED_FILE_TYPES = {
  "application/pdf": [".pdf"],
  "image/*": [".png", ".jpg", ".jpeg"],
};

export function RequestCertificate() {
  const { user } = useUser();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [motif, setMotif] = useState("");
  const [files, setFiles] = useState<File[]>([]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: ACCEPTED_FILE_TYPES,
    maxSize: MAX_FILE_SIZE,
    maxFiles: MAX_FILES,
    onDropAccepted: (acceptedFiles) => {
      const newFiles = acceptedFiles.slice(0, MAX_FILES - files.length);
      if (files.length + acceptedFiles.length > MAX_FILES) {
        toast({
          title: "Limite atteinte",
          description: `Vous ne pouvez ajouter que ${MAX_FILES} fichiers maximum`,
          variant: "warning",
        });
      }
      setFiles((prev) => [...prev, ...newFiles]);
    },
    onDropRejected: (rejectedFiles) => {
      rejectedFiles.forEach(({ file, errors }) => {
        if (errors.some((e) => e.code === "file-too-large")) {
          toast({
            title: "Fichier trop volumineux",
            description: `${file.name} dépasse la limite de ${formatBytes(
              MAX_FILE_SIZE
            )}`,
            variant: "warning",
          });
        } else if (errors.some((e) => e.code === "file-invalid-type")) {
          toast({
            title: "Type de fichier non supporté",
            description: `${file.name} n'est pas un type de fichier accepté (PDF, PNG, JPG)`,
            variant: "warning",
          });
        }
      });
    },
  });

  const CERTIFICATE_PRICE = 10_000_00; // 10_000 OUV in centimes

  const handleSubmit = async () => {
    // Validation du motif
    if (motif.trim().length < 10) {
      toast({
        title: "Motif trop court",
        description:
          "Veuillez fournir un motif plus détaillé (minimum 10 caractères)",
        variant: "warning",
      });
      return;
    }

    if (!user?.quartier?.name) {
      toast({
        title: "Erreur",
        description: "Votre quartier n'est pas défini",
        variant: "error",
      });
      return;
    }

    try {
      setIsLoading(true);

      // 1. Créer d'abord le certificat
      const certificateResult = await createCertificateRequest({
        motif,
        files,
        quartier: user.quartier.name,
      });

      if (!certificateResult.success) {
        throw new Error("Erreur lors de la création du certificat");
      }

      // 2. Initier le paiement avec le certificat créé
      const paymentResult = await initiatePayment({
        amount: Number((CERTIFICATE_PRICE / 100).toFixed(2)),
        provider: "orange-money",
        metadata: {
          certificateId: certificateResult.certificate.$id,
          type: "certificate_request",
          returnUrl: `/dashboard/certificates`,
        },
      });

      if (paymentResult.success && paymentResult.paymentUrl) {
        toast({
          title: "Redirection vers le paiement",
          description:
            "Vous allez être redirigé vers la page de paiement Orange Money",
          variant: "success",
        });

        // Redirect to payment page
        window.location.href = paymentResult.paymentUrl;
      } else {
        throw new Error("Erreur lors de l'initiation du paiement");
      }
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error?.message || "Une erreur est survenue",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="relative overflow-hidden border-0 shadow-none">
      <CardHeader className="space-y-1 pb-8">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2"
        >
          <div className="h-8 w-1 rounded-full bg-gradient-to-b from-accent-primary to-accent-secondary" />
          <div>
            <CardTitle className="text-2xl bg-gradient-to-r from-accent-primary to-accent-secondary bg-clip-text text-transparent">
              Nouvelle demande
            </CardTitle>
            <CardDescription className="text-neutral-600">
              Remplissez ce formulaire pour demander un nouveau certificat
            </CardDescription>
          </div>
        </motion.div>
      </CardHeader>

      <CardContent className="space-y-8">
        {/* Motif de la demande */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="space-y-2"
        >
          <label className="text-sm font-medium text-neutral-900">
            Motif de la demande
          </label>
          <Textarea
            placeholder="Expliquez brièvement le motif de votre demande..."
            value={motif}
            onChange={(e) => setMotif(e.target.value)}
            className="min-h-[120px] resize-none transition-all duration-200
              focus:ring-accent-primary/20 focus:border-accent-primary/30
              hover:border-accent-primary/20"
          />
        </motion.div>

        {/* Upload de documents */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-4"
        >
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-neutral-900">
              Documents justificatifs
            </label>
            <span className="text-xs text-neutral-500">Facultatif</span>
          </div>
          <div
            {...getRootProps()}
            className={`relative border-2 border-dashed rounded-xl p-8 text-center cursor-pointer
              transition-all duration-300 group
              ${
                isDragActive
                  ? "border-accent-primary/50 bg-accent-primary/5"
                  : "border-neutral-200 hover:border-accent-primary/30 hover:bg-accent-primary/5"
              }`}
          >
            <input {...getInputProps()} />
            <div className="space-y-4">
              <div
                className="w-16 h-16 mx-auto rounded-xl bg-accent-primary/10
                flex items-center justify-center group-hover:scale-110 transition-transform"
              >
                <Upload className="w-8 h-8 text-accent-primary" />
              </div>
              <div>
                <p className="text-sm text-neutral-600">
                  Glissez-déposez vos fichiers ici, ou
                  <span className="text-accent-primary font-medium">
                    {" "}
                    parcourir
                  </span>
                </p>
                <div className="text-xs text-neutral-500 mt-1 space-y-1">
                  <p>
                    PDF, PNG, JPG ou JPEG ({formatBytes(MAX_FILE_SIZE)} max)
                  </p>
                  <p>{MAX_FILES} fichiers maximum</p>
                  <p>
                    {files.length} / {MAX_FILES} fichiers ajoutés
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Liste des fichiers */}
          <AnimatePresence>
            {files.length > 0 && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-2"
              >
                {files.map((file, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    className="flex items-center gap-3 p-3 rounded-lg bg-accent-primary/5
                      border border-accent-primary/10"
                  >
                    <FileText className="w-5 h-5 text-accent-primary" />
                    <span className="text-sm text-neutral-600 flex-1 truncate">
                      {file.name}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-500 hover:text-red-600 hover:bg-red-50"
                      onClick={() =>
                        setFiles(files.filter((_, i) => i !== index))
                      }
                    >
                      Supprimer
                    </Button>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Payment Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-4"
        >
          <div className="bg-gradient-to-r from-accent-primary/5 to-accent-secondary/5 rounded-xl p-6 border border-accent-primary/10">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg bg-accent-primary/10 flex items-center justify-center">
                <CreditCard className="w-5 h-5 text-accent-primary" />
              </div>
              <div>
                <h3 className="font-semibold text-neutral-900">
                  Paiement requis
                </h3>
                <p className="text-sm text-neutral-600">
                  Le paiement est nécessaire avant la soumission de votre
                  demande
                </p>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-neutral-600">
                Coût du certificat:
              </span>
              <span className="text-lg font-bold text-accent-primary">
                {(CERTIFICATE_PRICE / 100).toFixed(2)}{" "}
                {PAYMENT_CONFIG.orangeMoney.currency}
              </span>
            </div>
            <div className="mt-3 text-xs text-neutral-500">
              <p>• Paiement sécurisé via Orange Money</p>
              <p>• Votre demande sera traitée après confirmation du paiement</p>
              <p>• Aucun frais supplémentaire</p>
            </div>
          </div>
        </motion.div>
      </CardContent>

      <CardFooter className="pt-6">
        <Button
          className="w-full relative overflow-hidden group bg-gradient-to-r from-accent-primary to-accent-secondary
            hover:from-accent-primary/90 hover:to-accent-secondary/90 text-white font-medium
            shadow-lg shadow-accent-primary/20 hover:shadow-xl hover:shadow-accent-primary/30
            transform hover:-translate-y-0.5 transition-all duration-300"
          onClick={handleSubmit}
          disabled={!motif || isLoading}
        >
          <span
            className="absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(255,255,255,0.2)_50%,transparent_100%)]
            translate-x-[-150%] group-hover:translate-x-[150%] duration-1000 transition-transform"
          />
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Redirection vers le paiement...
            </>
          ) : (
            <>
              <CreditCard className="mr-2 h-4 w-4" />
              Payer et envoyer la demande (
              {(CERTIFICATE_PRICE / 100).toFixed(2)}{" "}
              {PAYMENT_CONFIG.orangeMoney.currency})
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
