import { ROLES } from "@/actions/auth/constants";
import { COLLECTIONS } from "../server/constant";


// Fonction utilitaire pour obtenir la collection en fonction du rôle
export const getCollectionByRole = (role?: ROLES) => {
  switch (role) {
    case ROLES.ADMIN:
      return COLLECTIONS.ADMINS;
    case ROLES.CHEF:
      return COLLECTIONS.CHEFS;
    case ROLES.AGENT:
      return COLLECTIONS.AGENTS;
    case ROLES.CITIZEN:
      return COLLECTIONS.CITIZENS;
    default:
      return null;
  }
};
