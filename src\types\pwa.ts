export interface PWAIcon {
  src: string
  sizes: string
  type: string
  purpose?: 'any' | 'maskable' | 'monochrome'
}

export interface PWAScreenshot {
  src: string
  sizes: string
  type: string
  platform: 'wide' | 'narrow'
  label: string
}

export interface PWAShortcut {
  name: string
  url: string
  description: string
  icons: PWAIcon[]
}

export interface PWARelatedApplication {
  platform: string
  url: string
  id?: string
}

export interface PWAShareTarget {
  action: string
  method: string
  enctype: string
  params: {
    title: string
    text: string
    url: string
    files?: Array<{
      name: string
      accept: string[]
    }>
  }
}

export interface PWAProtocolHandler {
  protocol: string
  url: string
}

export interface PWAFileHandler {
  action: string
  accept: {
    [key: string]: string[]
  }
}

export interface PWAConfig {
  name: string
  short_name: string
  description: string
  id: string
  categories: string[]
  lang: string
  dir: 'ltr' | 'rtl'
  display: 'fullscreen' | 'standalone' | 'minimal-ui' | 'browser'
  display_override?: string[]
  orientation?: 'any' | 'natural' | 'landscape' | 'portrait'
  start_url: string
  scope: string
  scope_extensions?: Array<{ origin: string }>
  background_color: string
  theme_color: string
  prefer_related_applications: boolean
  launch_handler?: {
    client_mode: string[]
  }
  handle_links?: string
  shortcuts?: PWAShortcut[]
  screenshots?: PWAScreenshot[]
  icons: PWAIcon[]
  related_applications?: PWARelatedApplication[]
  share_target?: PWAShareTarget
  protocol_handlers?: PWAProtocolHandler[]
  edge_side_panel?: {
    preferred_width: number
  }
  iarc_rating_id?: string
  file_handlers?: PWAFileHandler[]
  note_taking?: {
    new_note_url: string
  }
}
