"use client";

import { motion } from "framer-motion";

export function FloatingShapes() {
  return (
    <div className="relative h-[500px]">
      {/* Cercle principal avec dégradé */}
      <motion.div
        animate={{
          scale: [1, 1.05, 1],
          rotate: [0, 5, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-72 h-72 rounded-full bg-gradient-to-br from-[#004D40] via-[#00796B] to-[#009688] opacity-20 blur-2xl"
      />

      {/* Formes géométriques flottantes */}
      <motion.div
        animate={{
          y: [-10, 10, -10],
          rotate: [0, -5, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        className="absolute top-20 left-20 w-40 h-40 rounded-2xl bg-gradient-to-r from-yellow-400 to-orange-500 opacity-20 blur-xl"
      />

      <motion.div
        animate={{
          y: [10, -10, 10],
          rotate: [0, 5, 0],
        }}
        transition={{
          duration: 7,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        className="absolute bottom-20 right-20 w-48 h-48 rounded-full bg-gradient-to-r from-[#004D40] to-[#00796B] opacity-20 blur-xl"
      />

      {/* Symbole central */}
      <motion.div
        initial={{ scale: 0.5, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-48 h-48 rounded-2xl bg-white/80 backdrop-blur-xl shadow-2xl border border-white/20 flex items-center justify-center z-10"
      >
        <span className="text-6xl">🏛️</span>
      </motion.div>

      {/* Particules flottantes */}
      <motion.div
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.5, 0.3],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        className="absolute top-40 right-40 w-4 h-4 rounded-full bg-[#004D40]"
      />
      <motion.div
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.5, 0.3],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1,
        }}
        className="absolute bottom-40 left-40 w-3 h-3 rounded-full bg-yellow-400"
      />
    </div>
  );
}
