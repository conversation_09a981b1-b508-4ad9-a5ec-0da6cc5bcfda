"use client";

import { ContactForm } from "@/components/forms/contact-form";
import { JsonLd } from "@/components/seo/json-ld";
import { motion } from "framer-motion";

const contactInfo = [
  {
    title: "<PERSON>ress<PERSON>",
    info: "<PERSON><PERSON><PERSON>, République de Guinée",
    icon: "📍",
    link: "https://goo.gl/maps/VotreLienGoogleMaps",
  },
  {
    title: "Email",
    info: "<EMAIL>",
    icon: "📧",
    link: "mailto:<EMAIL>",
  },
  {
    title: "Téléphone",
    info: "+224 620 15 71 84",
    icon: "📞",
    link: "tel:+224620157184",
  },
  {
    title: "<PERSON>rai<PERSON>",
    info: "Lun-Ven: 8h-17h",
    icon: "🕰️",
  },
];

const officeHours = [
  { day: "Lundi", hours: "8:00 - 17:00" },
  { day: "Mardi", hours: "8:00 - 17:00" },
  { day: "Mercredi", hours: "8:00 - 17:00" },
  { day: "Je<PERSON>", hours: "8:00 - 17:00" },
  { day: "Vendredi", hours: "8:00 - 17:00" },
  { day: "Samedi", hours: "Ferm<PERSON>" },
  { day: "Dimanche", hours: "Fermé" },
];

export default function ContactPage() {
  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-white to-accent-primary/5">
      {/* Motif de fond */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-5" />
      </div>

      {/* Cercles décoratifs */}
      <motion.div
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.2, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -left-32 -top-32 w-96 h-96 bg-accent-primary rounded-full mix-blend-multiply filter blur-3xl opacity-10"
      />
      <motion.div
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.2, 0.3, 0.2],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -right-32 top-1/4 w-96 h-96 bg-accent-secondary rounded-full mix-blend-multiply filter blur-3xl opacity-10"
      />

      <div className="relative z-10 container mx-auto px-6 py-24">
        {/* En-tête */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-20"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-accent-primary via-accent-secondary to-accent-primary bg-clip-text text-transparent mb-6">
            Contactez-nous
          </h1>
          <p className="text-neutral-600 max-w-2xl mx-auto">
            Notre équipe est à votre disposition pour répondre à vos questions
            et vous accompagner dans vos démarches.
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Formulaire de contact */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="relative"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-accent-primary/10 to-accent-secondary/10 rounded-2xl blur-xl" />
            <div className="relative p-8 rounded-2xl bg-white/60 backdrop-blur-lg border border-neutral-200/60">
              <ContactForm />
            </div>
          </motion.div>

          {/* Informations de contact et horaires */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="space-y-8"
          >
            {/* Coordonnées */}
            <div className="relative p-8 rounded-2xl bg-white/60 backdrop-blur-lg border border-neutral-200/60">
              <h2 className="text-2xl font-semibold bg-gradient-to-r from-accent-primary to-accent-secondary bg-clip-text text-transparent mb-6">
                Nos Coordonnées
              </h2>
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}
                    className="group"
                  >
                    {info.link ? (
                      <a
                        href={info.link}
                        className="flex items-center space-x-4 p-3 rounded-xl hover:bg-white/40 transition-colors"
                      >
                        <div className="text-3xl transform group-hover:scale-110 transition-transform">
                          {info.icon}
                        </div>
                        <div>
                          <h3 className="font-semibold text-accent-primary group-hover:text-accent-secondary transition-colors">
                            {info.title}
                          </h3>
                          <p className="text-neutral-600">{info.info}</p>
                        </div>
                      </a>
                    ) : (
                      <div className="flex items-center space-x-4 p-3">
                        <div className="text-3xl">{info.icon}</div>
                        <div>
                          <h3 className="font-semibold text-accent-primary">
                            {info.title}
                          </h3>
                          <p className="text-neutral-600">{info.info}</p>
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Horaires d'ouverture */}
            <div className="relative p-8 rounded-2xl bg-white/60 backdrop-blur-lg border border-neutral-200/60">
              <h2 className="text-2xl font-semibold bg-gradient-to-r from-accent-primary to-accent-secondary bg-clip-text text-transparent mb-6">
                Horaires d'ouverture
              </h2>
              <div className="space-y-3">
                {officeHours.map((schedule, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.8 + index * 0.1 }}
                    className="flex justify-between items-center py-2 border-b border-neutral-100 last:border-0"
                  >
                    <span className="font-medium text-neutral-700">
                      {schedule.day}
                    </span>
                    <span
                      className={
                        schedule.hours === "Fermé"
                          ? "text-red-500"
                          : "text-emerald-600"
                      }
                    >
                      {schedule.hours}
                    </span>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Carte */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1 }}
              className="relative rounded-2xl overflow-hidden h-64 bg-white/60 backdrop-blur-lg border border-neutral-200/60"
            >
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d15707.524050056461!2d-13.6803246!3d9.5357872!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xf1cd6de65b4c3ed%3A0x6c5df8ef55cf2546!2sConakry%2C%20Guinea!5e0!3m2!1sen!2s!4v1656935335545!5m2!1sen!2s"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
              />
            </motion.div>
          </motion.div>
        </div>
      </div>

      <JsonLd
        type="WebPage"
        title="Contactez-nous"
        description="Contactez notre équipe pour toute question ou demande d'assistance"
        path="/contact"
      />
    </div>
  );
}
