"use client";

import { zod<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/zod";
import { Upload } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";

import { register } from "@/actions/auth/register";
import { LocationSelector } from "@/components/forms/location-selector";
import { Button } from "@/components/ui/button";
import { FileUpload } from "@/components/ui/file-upload";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Loader } from "@/components/ui/loader";
import { ProgressSteps } from "@/components/ui/progress-steps";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils/cn";
import { RegisterFormData, RegisterSchema } from "@/schemas/citizen";

const STEPS = ["Informations", "Adresse", "Documents", "Confirmation"] as const;

type StepFields = {
  [key: number]: Array<keyof RegisterFormData>;
};

export function RegisterForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const { toast } = useToast();
  const router = useRouter();

  const form = useForm<RegisterFormData>({
    resolver: zodResolver(RegisterSchema),
    defaultValues: {
      nom: "",
      prenom: "",
      dateNaissance: "",
      lieuNaissance: "",
      nomPere: "",
      nomMere: "",
      nationalite: "",
      profession: "",
      telephone: "",
      email: "",
      motDePasse: "",
      confirmationMotDePasse: "",
      region: "",
      prefecture: "",
      commune: "",
      quartier: "",
      numeroBatiment: "",
      proprietaireBatiment: "",
      adressePrecise: "",
      dateInstallation: "",
      carteElecteur: "",
      pieceIdentite: undefined,
      extraitNaissance: undefined,
      consentement: false,
    },
    mode: "onChange",
    delayError: 500,
  });

  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      if (name === "motDePasse") {
        const password = value.motDePasse as string;
        if (password) {
          const strength = getPasswordStrength(password);
          if (strength < 2) {
            form.setError("motDePasse", {
              type: "manual",
              message: "Le mot de passe est trop faible",
            });
          } else {
            form.clearErrors("motDePasse");
          }
        }
      }

      if (name === "confirmationMotDePasse" || name === "motDePasse") {
        const password = value.motDePasse;
        const confirmation = value.confirmationMotDePasse;
        if (password && confirmation && password !== confirmation) {
          form.setError("confirmationMotDePasse", {
            type: "manual",
            message: "Les mots de passe ne correspondent pas",
          });
        }
      }

      if (name === "email") {
        const email = value.email as string;
        if (email && !email.includes("@")) {
          form.setError("email", {
            type: "manual",
            message: "Format d'email invalide",
          });
        }
      }

      if (name === "telephone") {
        const phone = value.telephone as string;
        if (phone && !/^[0-9]{9}$/.test(phone)) {
          form.setError("telephone", {
            type: "manual",
            message: "Le numéro doit contenir 9 chiffres",
          });
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  const getPasswordStrength = (password: string): number => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
  };

  const PasswordStrengthIndicator = ({ password }: { password: string }) => {
    const strength = getPasswordStrength(password);
    const getColor = () => {
      if (strength <= 2) return "bg-red-500";
      if (strength <= 3) return "bg-yellow-500";
      return "bg-green-500";
    };

    return (
      <div className="mt-1">
        <div className="h-1 w-full bg-gray-200 rounded-full">
          <div
            className={`h-1 rounded-full transition-all ${getColor()}`}
            style={{ width: `${(strength / 5) * 100}%` }}
          />
        </div>
        <p className="text-xs mt-1 text-gray-500">
          {strength <= 2 && "Mot de passe faible"}
          {strength === 3 && "Mot de passe moyen"}
          {strength > 3 && "Mot de passe fort"}
        </p>
      </div>
    );
  };

  const onSubmit = async (data: RegisterFormData) => {
    try {
      setIsLoading(true);
      console.log("Début de la soumission", data);

      const result = await register(data);
      console.log("Résultat de l'inscription:", result);

      if (!result?.success) {
        toast({
          title: "Erreur",
          description:
            result?.error ?? "Une erreur est survenue lors de l'inscription",
          variant: "error",
        });
        return;
      }

      toast({
        title: "Inscription en cours de traitement",
        description:
          "Nous allons examiner votre demande et vous contacter prochainement.",
        variant: "success",
      });

      setTimeout(() => {
        router.push("/inscription/confirmation");
        router.refresh();
      }, 1500);
    } catch (error) {
      console.error("Erreur lors de l'inscription:", error);
      toast({
        title: "Erreur inattendue",
        description:
          "Une erreur est survenue lors du traitement de votre demande. Veuillez réessayer.",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const nextStep = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const STEP_FIELDS: StepFields = {
    0: [
      "nom",
      "prenom",
      "dateNaissance",
      "lieuNaissance",
      "nomPere",
      "nomMere",
      "nationalite",
      "profession",
      "telephone",
      "email",
      "motDePasse",
      "confirmationMotDePasse",
    ],
    1: [
      "region",
      "prefecture",
      "commune",
      "sousPrefecture",
      "quartier",
      "numeroBatiment",
      "proprietaireBatiment",
      "adressePrecise",
      "dateInstallation",
    ],
    2: ["pieceIdentite", "extraitNaissance"],
    3: ["consentement"],
  };

  const isCurrentStepValid = () => {
    const currentFields = STEP_FIELDS[currentStep as keyof typeof STEP_FIELDS];
    if (!currentFields) return true;

    // Pour l'étape adresse
    if (currentStep === 1) {
      const requiredAddressFields = [
        "region",
        "prefecture",
        "commune",
        "sousPrefecture",
        "quartier",
      ] as const;

      const allRequiredAddressFieldsFilled = requiredAddressFields.every(
        (field) => {
          const value = form.getValues(field);
          return value && value.length > 0;
        }
      );

      // Les champs optionnels n'ont pas besoin d'être validés
      const optionalFields = [
        "numeroBatiment",
        "proprietaireBatiment",
      ] as const;

      const otherFields = currentFields.filter(
        (field): field is keyof RegisterFormData =>
          !requiredAddressFields.includes(field as any) &&
          !optionalFields.includes(field as any)
      );

      const otherFieldsValid = otherFields.every((field) => {
        const value = form.getValues(field);
        return value && value.length > 0;
      });

      return allRequiredAddressFieldsFilled && otherFieldsValid;
    }

    // Pour l'étape des documents
    if (currentStep === 2) {
      const requiredDocs = ["pieceIdentite", "extraitNaissance"] as const;

      return requiredDocs.every((docField) => {
        const doc = form.getValues(docField);
        // Vérifie si le document existe et est un fichier
        return doc && doc instanceof File;
      });
    }

    // Pour les autres étapes
    return currentFields.every((field) => {
      const value = form.getValues(field);
      if (typeof value === "boolean") {
        return value === true;
      }
      return value && value.length > 0;
    });
  };

  const handleLocationSelect = ({
    region,
    prefecture,
    commune,
    sousPrefecture,
    quartier,
  }: {
    region: string;
    prefecture: string;
    commune: string;
    sousPrefecture: string;
    quartier: string;
  }) => {
    // Mise à jour silencieuse des valeurs
    form.setValue("region", region, { shouldValidate: false });
    form.setValue("prefecture", prefecture, { shouldValidate: false });
    form.setValue("commune", commune, { shouldValidate: false });
    form.setValue("sousPrefecture", sousPrefecture, { shouldValidate: false });
    form.setValue("quartier", quartier, { shouldValidate: false });

    // Validation unique après toutes les mises à jour
    form.trigger([
      "region",
      "prefecture",
      "commune",
      "sousPrefecture",
      "quartier",
    ]);
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-8">
      <ProgressSteps
        steps={STEPS}
        currentStep={currentStep}
        className="mb-12 pt-8"
      />

      <Form {...form}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit(onSubmit)(e);
          }}
          className="space-y-6"
        >
          <Tabs value={STEPS[currentStep].toLowerCase()} className="w-full">
            {/* Étape 1: Informations Personnelles */}
            <TabsContent value="informations" className="space-y-4">
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">
                  Informations Personnelles
                </h2>
                <Separator />

                <div className="grid gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="prenom"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Prénom</FormLabel>
                        <FormControl>
                          <Input placeholder="Votre prénom" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="nom"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nom</FormLabel>
                        <FormControl>
                          <Input placeholder="Votre nom" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="dateNaissance"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date de naissance</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="lieuNaissance"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Lieu de naissance</FormLabel>
                        <FormControl>
                          <Input placeholder="Lieu de naissance" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="nomPere"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nom du père</FormLabel>
                        <FormControl>
                          <Input placeholder="Nom complet du père" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="nomMere"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nom de la mère</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Nom complet de la mère"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="nationalite"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nationalité</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Sélectionnez votre nationalité" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="guineenne">Guinéenne</SelectItem>
                            <SelectItem value="autre">Autre</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="profession"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Profession</FormLabel>
                        <FormControl>
                          <Input placeholder="Votre profession" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="carteElecteur"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Numéro Carte Électeur
                          <span className="text-sm text-muted-foreground ml-1">
                            (optionnel)
                          </span>
                        </FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Ex: CE-12345" />
                        </FormControl>
                        <FormDescription>
                          Si vous avez une carte d&#39;électeur, veuillez saisir
                          son numéro
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="telephone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Téléphone</FormLabel>
                        <FormControl>
                          <Input placeholder="+224 XXX XXX XXX" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="motDePasse"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Mot de passe</FormLabel>
                        <FormControl>
                          <div className="space-y-2">
                            <Input
                              type="password"
                              {...field}
                              className={cn(
                                form.formState.errors.motDePasse &&
                                  "border-red-500",
                                field.value &&
                                  !form.formState.errors.motDePasse &&
                                  "border-green-500"
                              )}
                            />
                            {field.value && (
                              <PasswordStrengthIndicator
                                password={field.value}
                              />
                            )}
                          </div>
                        </FormControl>
                        <FormDescription>
                          Au moins 8 caractères, une majuscule, une minuscule et
                          un chiffre
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="confirmationMotDePasse"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirmer le mot de passe</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="••••••••"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Étape 2: Adresse */}
            <TabsContent value="adresse" className="space-y-4">
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">Adresse</h2>
                <Separator />

                <div className="grid gap-6">
                  <div className="col-span-2">
                    <FormField
                      control={form.control}
                      name="quartier" // Champ principal pour la validation
                      render={() => (
                        <FormItem className="space-y-1">
                          <FormLabel>Localisation</FormLabel>
                          <FormControl>
                            <LocationSelector
                              onLocationSelect={handleLocationSelect}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid gap-4 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="numeroBatiment"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bâtiment N°</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Numéro du bâtiment"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="proprietaireBatiment"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Appartenant à</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Nom du propriétaire"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="adressePrecise"
                    render={({ field }) => (
                      <FormItem className="col-span-2">
                        <FormLabel>Adresse précise</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Numéro de rue, repère, etc."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="dateInstallation"
                    render={({ field }) => (
                      <FormItem className="col-span-2">
                        <FormLabel>Date d&apos;installation</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Étape 3: Documents */}
            <TabsContent value="documents" className="space-y-4">
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">Pièces Justificatives</h2>
                <Separator />

                <div className="grid gap-6">
                  <FormField
                    control={form.control}
                    name="pieceIdentite"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Pièce d&apos;identité (CNI, Passeport)
                        </FormLabel>
                        <FormControl>
                          <FileUpload
                            endpoint="pieceIdentite"
                            value={field.value}
                            onChange={field.onChange}
                            options={{
                              maxSize: 5 * 1024 * 1024,
                              acceptedTypes: [
                                "image/jpeg",
                                "image/jpg",
                                "image/png",
                                "application/pdf",
                              ],
                              label: "Ajoutez votre pièce d'identité",
                              description: "Format JPG, PNG ou PDF - Max 5MB",
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="extraitNaissance"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Extrait d&apos;acte de naissance</FormLabel>
                        <FormControl>
                          <FileUpload
                            endpoint="extraitNaissance"
                            value={field.value}
                            onChange={field.onChange}
                            options={{
                              maxSize: 5 * 1024 * 1024,
                              acceptedTypes: [
                                "image/jpeg",
                                "image/jpg",
                                "image/png",
                                "application/pdf",
                              ],
                              label:
                                "Ajoutez votre extrait d'acte de naissance",
                              description: "Format JPG, PNG ou PDF - Max 5MB",
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Étape 4: Confirmation */}
            <TabsContent value="confirmation" className="space-y-4">
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">Confirmation</h2>
                <Separator />

                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="consentement"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <input
                            type="checkbox"
                            checked={field.value}
                            onChange={field.onChange}
                            className="h-4 w-4 rounded border-neutral-300 text-emerald-600 focus:ring-emerald-600"
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            Je certifie sur l&apos;honneur l&apos;exactitude des
                            informations fournies
                          </FormLabel>
                          <p className="text-sm text-neutral-400">
                            En cochant cette case, vous confirmez que toutes les
                            informations fournies sont exactes et vous acceptez
                            nos{" "}
                            <Link
                              href="/conditions"
                              className="text-emerald-500 hover:text-emerald-600"
                            >
                              conditions d&apos;utilisation
                            </Link>{" "}
                            et notre{" "}
                            <Link
                              href="/confidentialite"
                              className="text-emerald-500 hover:text-emerald-600"
                            >
                              politique de confidentialité
                            </Link>
                            .
                          </p>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex items-center justify-between gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={previousStep}
              disabled={currentStep === 0 || isLoading}
            >
              Précédent
            </Button>

            {currentStep === STEPS.length - 1 ? (
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full sm:w-auto"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <Loader size="xs" variant="ghost" className="text-white" />
                    <span>Inscription en cours...</span>
                  </div>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Soumettre ma demande d&apos;inscription
                  </>
                )}
              </Button>
            ) : (
              <Button
                type="button"
                onClick={nextStep}
                disabled={!isCurrentStepValid() || isLoading}
              >
                Suivant
              </Button>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
}
