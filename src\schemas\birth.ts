import * as z from "zod";

export const DeclarationNaissanceSchema = z
  .object({
    // Informations sur l'enfant
    nomEnfant: z.string().min(2, "Le nom doit contenir au moins 2 caractères"),
    prenomEnfant: z
      .string()
      .min(2, "Le prénom doit contenir au moins 2 caractères"),
    dateNaissance: z.string().min(1, "La date de naissance est requise"),
    lieuNaissance: z.string().min(2, "Le lieu de naissance est requis"),
    sexe: z.enum(["M", "F"], {
      required_error: "Le sexe de l'enfant est requis",
    }),

    // Informations sur les parents
    nomPere: z.string().min(2, "Le nom du père est requis"),
    prenomPere: z.string().min(2, "Le prénom du père est requis"),
    professionPere: z.string().min(2, "La profession du père est requise"),
    nationalitePere: z.string().min(2, "La nationalité du père est requise"),

    nomMere: z.string().min(2, "Le nom de la mère est requis"),
    prenomMere: z.string().min(2, "Le prénom de la mère est requis"),
    professionMere: z.string().min(2, "La profession de la mère est requise"),
    nationaliteMere: z.string().min(2, "La nationalité de la mère est requise"),

    // Informations sur le déclarant
    nomDeclarant: z.string().min(2, "Le nom du déclarant est requis"),
    prenomDeclarant: z.string().min(2, "Le prénom du déclarant est requis"),
    qualiteDeclarant: z.string().min(2, "La qualité du déclarant est requise"),
    telephoneDeclarant: z.string().min(9, "Le numéro de téléphone est requis"),

    // Documents justificatifs
    certificatNaissance: z
      .any()
      .refine(
        (val) => val != null,
        "Le certificat de naissance est obligatoire"
      )
      .refine(
        (val) => val?.size <= 5 * 1024 * 1024,
        "Le fichier ne doit pas dépasser 5MB"
      ),

    // Consentement
    consentement: z.boolean().optional(),
  })
  .refine(
    (data) => {
      // Ajouter la validation conditionnelle lors de la soumission
      if (data.consentement === false) {
        return false;
      }
      return true;
    },
    {
      message: "Vous devez accepter les conditions",
      path: ["consentement"],
    }
  );

export type DeclarationNaissanceFormData = z.infer<
  typeof DeclarationNaissanceSchema
>;
