# Utiliser Node 22
FROM node:22-alpine AS base

# Configurer pnpm
ENV PNPM_HOME="/var/lib/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

# Étape de dépendances
FROM base AS deps
WORKDIR /app

COPY package.json pnpm-lock.yaml* ./
RUN pnpm install --frozen-lockfile

# Étape de build
FROM base AS builder
WORKDIR /app

COPY --from=deps /app/node_modules ./node_modules
COPY . .

ARG NEXT_PUBLIC_APPWRITE_ENDPOINT="https://cloud.appwrite.io/v1"
ARG NEXT_PUBLIC_APPWRITE_PROJECT_ID="ncr"
ARG NEXT_APPWRITE_API_KEY="default-api-key"
ARG NEXT_TELEMETRY_DISABLED=1
ARG NEXT_PUBLIC_APP_URL="https://ncr.ouestech.com"
ARG NEXT_PUBLIC_APP_NAME="NCR"
ARG NEXT_PUBLIC_APP_ENV="production"

ENV NEXT_PUBLIC_APPWRITE_ENDPOINT=$NEXT_PUBLIC_APPWRITE_ENDPOINT
ENV NEXT_PUBLIC_APPWRITE_PROJECT_ID=$NEXT_PUBLIC_APPWRITE_PROJECT_ID
ENV NEXT_APPWRITE_API_KEY=$NEXT_APPWRITE_API_KEY
ENV NEXT_TELEMETRY_DISABLED=$NEXT_TELEMETRY_DISABLED
ENV NEXT_PUBLIC_APP_URL=$NEXT_PUBLIC_APP_URL
ENV NEXT_PUBLIC_APP_NAME=$NEXT_PUBLIC_APP_NAME
ENV NEXT_PUBLIC_APP_ENV=$NEXT_PUBLIC_APP_ENV


RUN pnpm build

# Étape de production
FROM base AS runner
WORKDIR /app

COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000

CMD ["node", "server.js"]
