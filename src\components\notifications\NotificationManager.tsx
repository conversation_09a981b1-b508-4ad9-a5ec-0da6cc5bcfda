"use client";

import { useEffect } from "react";
import { useNotifications } from "@/hooks/useNotifications";
import { Button } from "@/components/ui/button";

export function NotificationManager() {
  const {
    isSupported,
    permission,
    subscription,
    requestPermission,
    subscribe,
    unsubscribe,
  } = useNotifications();

  useEffect(() => {
    if (permission === "granted" && !subscription) {
      subscribe();
    }
  }, [permission, subscription, subscribe]);

  if (!isSupported) {
    return null;
  }

  const handleEnableNotifications = async () => {
    const newPermission = await requestPermission();
    if (newPermission === "granted") {
      await subscribe();
    }
  };

  const handleDisableNotifications = async () => {
    await unsubscribe();
  };

  return (
    <div className="flex items-center gap-4">
      {permission === "default" && (
        <Button onClick={handleEnableNotifications} variant="outline" size="sm">
          Activer les notifications
        </Button>
      )}

      {permission === "granted" && subscription && (
        <Button
          onClick={handleDisableNotifications}
          variant="outline"
          size="sm"
        >
          Désactiver les notifications
        </Button>
      )}
    </div>
  );
}
