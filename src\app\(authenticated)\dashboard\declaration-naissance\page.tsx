import { Metadata } from "next";

import { DeclarationNaissanceForm } from "@/components/forms/declaration-naissance-form";
import { PageHeader } from "@/components/layout/page-header";

export const metadata: Metadata = {
  title: "Déclaration de naissance | E-Citoyenneté",
  description: "Déclarez une nouvelle naissance dans le registre d'état civil",
};

export default function DeclarationNaissancePage() {
  return (
    <div className="container relative mx-auto px-4 py-8">
      {/* Effet de glassmorphism en arrière-plan */}
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 via-transparent to-teal-50/30 backdrop-blur-3xl rounded-2xl" />

      <div className="relative">
        <PageHeader
          title="Déclaration de naissance"
          description="Enregistrez une nouvelle naissance dans le registre d'état civil"
        />
        <div className="mt-8">
          <div className="bg-white/80 backdrop-blur-lg shadow-xl rounded-2xl border border-emerald-100/50">
            <DeclarationNaissanceForm />
          </div>
        </div>
      </div>
    </div>
  );
}
