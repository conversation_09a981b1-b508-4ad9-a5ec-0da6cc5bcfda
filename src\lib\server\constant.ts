import { createSessionClient } from "./appwrite";

/**
 * Type pour les services Appwrite retournés par les fonctions de création de client
 */
export type AppwriteServices = ReturnType<typeof createSessionClient>;

/**
 * Constantes pour les collections et buckets Appwrite
 */
export const COLLECTIONS = {
  CITIZENS: "citizens",
  ADDRESSES: "addresses",
  DOCUMENTS: "documents",
  CHEFS: "chefs",
  AGENTS: "agents",
  ADMINS: "admins",
  CERTIFICATES: "certificates",
  QUARTIERS: "quartiers",
  BIRTH_DECLARATIONS: "birth_declarations",
  PAYMENTS: "payments",
  PAYMENTS_METADATA: "payments_metadata",
  ADDRESSES_COLLECTION: "verifications_addresses", // Utilisée pour la vérification
  CERTIFICATE_VERIFICATIONS: "certificate_verifications",
  CERTIFICATE_METADATA: "certificate_verifications_metadata",
  // Nouvelle collection pour la distribution des revenus
  REVENUE_DISTRIBUTIONS: "revenue_distributions",
} as const;

export const BUCKETS = {
  DOCUMENTS: "citizen_documents",
  SIGNATURES: "signatures",
} as const;
