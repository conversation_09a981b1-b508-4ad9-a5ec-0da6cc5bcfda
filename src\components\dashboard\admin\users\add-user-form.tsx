"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createAdminUser } from "@/actions/admin/users";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { UserPlus, ArrowLeft, Loader2 } from "lucide-react";
import { ROLES } from "@/actions/auth/constants";
import { cn } from "@/lib/utils/cn";
import { LocationSelector } from "@/components/forms/location-selector";

const adminUserSchema = z
  .object({
    email: z.string().email("Email invalide"),
    password: z
      .string()
      .min(8, "Le mot de passe doit contenir au moins 8 caractères")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        "Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial"
      ),
    firstName: z
      .string()
      .min(2, "Le prénom doit contenir au moins 2 caractères"),
    lastName: z.string().min(2, "Le nom doit contenir au moins 2 caractères"),
    role: z.enum([ROLES.ADMIN, ROLES.CHEF, ROLES.AGENT]),
    phoneNumber: z
      .string()
      .regex(/^\+224\d{9}$/, "Le numéro doit être au format +224XXXXXXXXX"),
    quartier: z.union([
      z.string().min(2, "Le quartier est requis"),
      z.string().length(0),
    ]),
  })
  .refine(
    (data) => {
      // Le quartier est requis seulement pour CHEF et AGENT
      if (data.role !== ROLES.ADMIN) {
        return data.quartier.length >= 2;
      }
      return true;
    },
    {
      message: "Le quartier est requis pour les chefs et agents",
      path: ["quartier"],
    }
  );

export type AdminUserFormValues = z.infer<typeof adminUserSchema>;

const roleOptions = [
  { value: ROLES.ADMIN, label: "Administrateur" },
  { value: ROLES.CHEF, label: "Chef de quartier" },
  { value: ROLES.AGENT, label: "Agent" },
];

export function AddUserForm() {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<AdminUserFormValues>({
    resolver: zodResolver(adminUserSchema),
    defaultValues: {
      email: "",
      password: "",
      firstName: "",
      lastName: "",
      role: ROLES.AGENT,
      phoneNumber: "+224",
      quartier: "",
    },
  });

  async function onSubmit(data: AdminUserFormValues) {
    setIsLoading(true);
    try {
      // Si c'est un admin, on s'assure que quartier est une chaîne vide
      const formData = {
        ...data,
        quartier: data.role === ROLES.ADMIN ? "" : data.quartier,
      };

      const result = await createAdminUser(formData);

      if (!result.success) {
        toast({
          title: "Erreur",
          description: result.error ?? "Impossible de créer l'utilisateur",
          variant: "error",
        });
        return;
      }

      toast({
        title: "Succès",
        description: "L'utilisateur a été créé avec succès",
        variant: "success",
      });
      router.push("/admin/users");
      router.refresh();
    } catch (error) {
      console.error("Erreur lors de la création:", error);
      toast({
        title: "Erreur",
        description: "Impossible de créer l'utilisateur",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  }

  // Réinitialiser le quartier quand le rôle change
  const selectedRole = form.watch("role");
  useEffect(() => {
    if (selectedRole === ROLES.ADMIN) {
      form.setValue("quartier", "");
    }
  }, [selectedRole, form]);

  const handleLocationSelect = ({
    quartier,
  }: {
    region: string;
    prefecture: string;
    commune: string;
    sousPrefecture: string;
    quartier: string;
  }) => {
    // Mise à jour silencieuse de la valeur
    form.setValue("quartier", quartier, { shouldValidate: false });
    // Déclencher la validation après la mise à jour
    form.trigger("quartier");
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-2xl mx-auto"
    >
      <Card className="backdrop-blur-sm bg-white/50 border border-neutral-200/50 shadow-lg">
        <CardHeader className="space-y-1 pb-4">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-emerald-100 rounded-full">
              <UserPlus className="h-4 w-4 text-emerald-600" />
            </div>
            <div className="space-y-1">
              <CardTitle className="text-2xl">Nouvel utilisateur</CardTitle>
              <p className="text-sm text-neutral-500">
                Créez un nouvel utilisateur administratif avec les permissions
                appropriées.
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Section Identité */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-neutral-900">
                  Identité
                </h3>
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Prénom</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            disabled={isLoading}
                            className="bg-white/70"
                            placeholder="John"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nom</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            disabled={isLoading}
                            className="bg-white/70"
                            placeholder="Doe"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Section Contact */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-neutral-900">
                  Contact
                </h3>
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="email"
                            disabled={isLoading}
                            className="bg-white/70"
                            placeholder="<EMAIL>"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phoneNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Téléphone</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            disabled={isLoading}
                            className="bg-white/70"
                            placeholder="+224XXXXXXXXX"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Section Accès */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-neutral-900">
                  Accès
                </h3>
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Mot de passe</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="password"
                            disabled={isLoading}
                            className="bg-white/70"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rôle</FormLabel>
                        <Select
                          disabled={isLoading}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white/70">
                              <SelectValue placeholder="Sélectionner un rôle" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {roleOptions.map((role) => (
                              <SelectItem
                                key={role.value}
                                value={role.value}
                                className="cursor-pointer"
                              >
                                {role.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Section Affectation - Conditionnelle selon le rôle */}
              {selectedRole !== ROLES.ADMIN && (
                <FormField
                  control={form.control}
                  name="quartier"
                  render={() => (
                    <FormItem>
                      <FormLabel>Quartier d&apos;affectation</FormLabel>
                      <FormControl>
                        <LocationSelector
                          onLocationSelect={handleLocationSelect}
                          className="w-full"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Actions */}
              <div className="flex justify-between pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={isLoading}
                  className={cn(
                    "gap-2 transition-all duration-200",
                    isLoading && "opacity-50 cursor-not-allowed"
                  )}
                >
                  <ArrowLeft className="w-4 h-4" />
                  Retour
                </Button>

                <Button
                  type="submit"
                  disabled={isLoading}
                  className={cn(
                    "bg-gradient-to-r from-emerald-600 to-teal-600",
                    "text-white font-medium",
                    "hover:from-emerald-700 hover:to-teal-700",
                    "transition-all duration-200",
                    "flex items-center gap-2",
                    isLoading && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Création en cours...
                    </>
                  ) : (
                    <>
                      <UserPlus className="w-4 h-4" />
                      Créer l&#39;utilisateur
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </motion.div>
  );
}
