"use client";

import { motion } from "framer-motion";
import { ArrowRight, Check, Shield } from "lucide-react";
import Link from "next/link";
import { BackgroundBeams } from "@/components/ui/background-beams";
import { TracingBeam } from "@/components/ui/tracing-beam";
import { containerVariants, itemVariants } from "@/lib/motion-variants";

const TermsPage = () => {
  const sections = [
    {
      title: "1. Acceptation des Conditions",
      content: `En accédant et en utilisant la plateforme NCR (Nouvelle Carte de Résident), vous acceptez d'être lié par les présentes conditions d'utilisation. Si vous n'acceptez pas ces conditions, veuillez ne pas utiliser ce service.`,
    },
    {
      title: "2. Description du Service",
      content: `La plateforme NCR est un service public numérique permettant la gestion et la délivrance des certificats de résidence en Guinée. Elle vise à moderniser et simplifier les démarches administratives.`,
    },
    {
      title: "3. Obligations des Utilisateurs",
      content: `Les utilisateurs s'engagent à :
      - Fournir des informations exactes et à jour
      - Maintenir la confidentialité de leurs identifiants
      - Utiliser le service de manière légale et responsable
      - Respecter les droits des autres utilisateurs`,
    },
    {
      title: "4. Responsabilités",
      content: `L'administration s'efforce d'assurer la disponibilité et la fiabilité du service, mais ne peut être tenue responsable des interruptions techniques ou des pertes de données indépendantes de sa volonté.`,
    },
    {
      title: "5. Protection des Données",
      content: `Nous nous engageons à protéger vos données personnelles conformément à la réglementation en vigueur. Pour plus d'informations, consultez notre politique de confidentialité.`,
    },
  ];

  const features = [
    {
      title: "Sécurité Renforcée",
      description: "Protection maximale de vos données personnelles",
      icon: Shield,
    },
    {
      title: "Service Officiel",
      description: "Plateforme gouvernementale certifiée",
      icon: Check,
    },
  ];

  return (
    <div className="relative min-h-screen w-full bg-gradient-to-b from-neutral-950 via-neutral-900 to-neutral-950">
      <BackgroundBeams className="absolute inset-0" />
      
      <div className="relative isolate z-10 px-6 pt-14 lg:px-8">
        <div className="mx-auto max-w-4xl py-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="text-center"
          >
            <motion.h1 
              variants={itemVariants}
              className="text-4xl font-bold tracking-tight text-white sm:text-6xl"
            >
              Conditions d&apos;Utilisation
            </motion.h1>
            <motion.p 
              variants={itemVariants}
              className="mt-6 text-lg leading-8 text-gray-300"
            >
              Découvrez les modalités d&apos;utilisation de la plateforme NCR
            </motion.p>
          </motion.div>

          <div className="mt-12">
            <TracingBeam>
              {sections.map((section, index) => (
                <motion.div
                  key={index}
                  initial="hidden"
                  animate="visible"
                  variants={containerVariants}
                  className="mb-12 rounded-xl bg-white/5 p-6 backdrop-blur-sm"
                >
                  <motion.h2 
                    variants={itemVariants}
                    className="text-xl font-semibold text-white mb-4"
                  >
                    {section.title}
                  </motion.h2>
                  <motion.p 
                    variants={itemVariants}
                    className="text-gray-300 whitespace-pre-line"
                  >
                    {section.content}
                  </motion.p>
                </motion.div>
              ))}
            </TracingBeam>
          </div>

          <motion.div 
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="mt-16 grid gap-8 md:grid-cols-2"
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="relative overflow-hidden rounded-xl border border-white/10 bg-white/5 p-8 backdrop-blur-sm"
              >
                <div className="flex items-center gap-4">
                  <feature.icon className="h-8 w-8 text-emerald-500" />
                  <h3 className="text-xl font-semibold text-white">{feature.title}</h3>
                </div>
                <p className="mt-4 text-gray-300">{feature.description}</p>
              </motion.div>
            ))}
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="mt-16 text-center"
          >
            <motion.div
              variants={itemVariants}
              className="inline-flex items-center gap-2 rounded-full bg-emerald-500/10 px-6 py-3 text-emerald-500 transition-colors hover:bg-emerald-500/20"
            >
              <Link href="/confidentialite">
                Consulter notre politique de confidentialité
              </Link>
              <ArrowRight className="h-4 w-4" />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default TermsPage;
