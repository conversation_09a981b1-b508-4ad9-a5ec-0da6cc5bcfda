"use client";

import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import {
  downloadCertificate,
  updateCertificateStatus,
  updateDocumentStatus,
} from "@/actions/certificates";
import { uploadCertificateDocument } from "@/actions/citizen/certificates";
import { Certificate, CertificateDocument } from "@/actions/types";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { FileUpload } from "@/components/ui/file-upload";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { PAYMENT_CONFIG } from "@/config/payment";
import { usePaymentFlow } from "@/hooks/use-payment-flow";
import { getAppwriteFileUrl } from "@/lib/utils/appwrite";
import { cn } from "@/lib/utils/cn";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Check, Download, Eye, FileText, Loader2, X } from "lucide-react";
import { useCallback, useState } from "react";

interface CertificateDocumentsProps {
  certificate: Certificate;
  userRole: "chef" | "agent" | "citizen" | "admin";
  user: {
    $id: string;
    name: string;
  };
}

export function CertificateDocuments({
  certificate,
  userRole,
  user,
}: CertificateDocumentsProps) {
  const queryClient = useQueryClient();
  const [isUploading, setIsUploading] = useState(false);
  const [selectedDocument, setSelectedDocument] =
    useState<CertificateDocument | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [documentError, setDocumentError] = useState<boolean>(false);
  const { toast } = useToast();
  const { startPaymentFlow, isProcessing: isPaymentProcessing } =
    usePaymentFlow();
  const [isDownloading, setIsDownloading] = useState(false);

  // Préparation des documents
  const identityDocs = (certificate.citizen?.documents ||
    []) as CertificateDocument[];
  const requestDocs = (certificate.documents || []) as CertificateDocument[];

  // Permissions
  const canDownload =
    certificate.status === CERTIFICATE_STATUS.DELIVERED &&
    (userRole === "admin" || !certificate.downloadedAt);
  const canUpload =
    userRole === "citizen" && certificate.status === CERTIFICATE_STATUS.DRAFT;
  const canApproveDocuments = userRole === "chef" || userRole === "agent";

  // Mutations avec React Query
  const updateDocumentStatusMutation = useMutation({
    mutationFn: async ({
      documentId,
      status,
    }: {
      documentId: string;
      status: "approved" | "rejected";
    }) => {
      return updateDocumentStatus(documentId, status);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["certificate", certificate.$id],
      });
    },
  });

  const updateCertificateStatusMutation = useMutation({
    mutationFn: async (data: {
      certificateId: string;
      status: string;
      verifiedBy: string;
      verifiedAt: string;
      verificationNotes: string;
    }) => {
      return updateCertificateStatus(data.certificateId, {
        status: data.status,
        verifiedBy: data.verifiedBy,
        verifiedAt: data.verifiedAt,
        verificationNotes: data.verificationNotes,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["certificate", certificate.$id],
      });
    },
  });

  const downloadCertificateMutation = useMutation({
    mutationFn: downloadCertificate,
    onSuccess: () => {
      toast({
        title: "Téléchargement réussi",
        description: "Le certificat a été téléchargé avec succès.",
      });
      queryClient.invalidateQueries({
        queryKey: ["certificate", certificate.$id],
      });
    },
    onError: (error) => {
      toast({
        title: "Erreur",
        description:
          error.message || "Une erreur est survenue lors du téléchargement.",
        variant: "error",
      });
    },
  });

  // Gestionnaires d'événements
  const handleViewDocument = useCallback(
    (document: CertificateDocument) => {
      if (!document.fileId) {
        toast({
          title: "Erreur",
          description: "Impossible d'afficher ce document : ID manquant",
          variant: "error",
        });
        return;
      }
      setDocumentError(false);
      setSelectedDocument({
        ...document,
        fileUrl: getAppwriteFileUrl(document.fileId),
      });
    },
    [toast]
  );

  const handleDocumentError = useCallback(() => {
    setDocumentError(true);
    toast({
      title: "Erreur",
      description: "Impossible de charger le document. Veuillez réessayer.",
      variant: "error",
    });
  }, [toast]);

  const handleCloseDialog = (open: boolean) => {
    if (!open) {
      setSelectedDocument(null);
    }
  };

  const handleUpdateStatus = async (
    documentId: string,
    newStatus: "approved" | "rejected"
  ) => {
    try {
      await updateDocumentStatusMutation.mutateAsync({
        documentId,
        status: newStatus,
      });

      // Récupérer tous les documents (identité et demande)
      const allDocuments = [...identityDocs, ...requestDocs].map((doc) =>
        doc.$id === documentId ? { ...doc, status: newStatus } : doc
      );

      // Vérifier le statut global
      const allApproved = allDocuments.every(
        (doc) => doc.status === "approved"
      );
      const anyRejected = allDocuments.some((doc) => doc.status === "rejected");

      // Mettre à jour le statut du certificat si nécessaire
      if (allApproved || anyRejected) {
        await updateCertificateStatusMutation.mutateAsync({
          certificateId: certificate.$id,
          status: allApproved
            ? CERTIFICATE_STATUS.APPROVED
            : CERTIFICATE_STATUS.REJECTED,
          verifiedBy: user.$id,
          verifiedAt: new Date().toISOString(),
          verificationNotes: `Documents vérifiés par ${user.name}${
            anyRejected
              ? " - Au moins un document rejeté"
              : " - Tous les documents approuvés"
          }`,
        });
      }

      toast({
        title: "Statut mis à jour",
        description: `Le document a été ${
          newStatus === "approved" ? "approuvé" : "rejeté"
        } avec succès.`,
      });
    } catch (error) {
      toast({
        title: "Erreur",
        description:
          "Une erreur est survenue lors de la mise à jour du statut.",
        variant: "error",
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-500">Approuvé</Badge>;
      case "rejected":
        return <Badge className="bg-red-500">Rejeté</Badge>;
      default:
        return <Badge className="bg-yellow-500">En attente</Badge>;
    }
  };

  // Rendu d'un document
  const renderDocument = (doc: CertificateDocument) => {
    return (
      <div
        key={doc.$id}
        className="flex items-center justify-between p-3 bg-neutral-50 rounded-lg"
      >
        <div className="flex items-center gap-3">
          <FileText className="h-5 w-5 text-neutral-500" />
          <div>
            <div className="flex items-center gap-2">
              <p className="text-sm font-medium">
                {doc.fileName ?? "Document sans nom"}
              </p>
              {getStatusBadge(doc.status)}
            </div>
            <div className="flex items-center gap-2 text-xs text-neutral-500">
              <span>{Math.round(Number(doc.fileSize) / 1024)} Ko</span>
              {doc.type && <span>•</span>}
              {doc.type && <span>{doc.type}</span>}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleViewDocument(doc)}
          >
            <Eye className="h-4 w-4 mr-2" />
            Voir
          </Button>
          {canApproveDocuments &&
            doc.status !== "approved" &&
            doc.status !== "rejected" && (
              <>
                <Button
                  size="sm"
                  variant="default"
                  className="bg-green-600 hover:bg-green-700"
                  onClick={() => handleUpdateStatus(doc.$id, "approved")}
                  disabled={updateDocumentStatusMutation.isPending}
                >
                  <Check className="h-4 w-4 mr-2" />
                  Approuver
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleUpdateStatus(doc.$id, "rejected")}
                  disabled={updateDocumentStatusMutation.isPending}
                >
                  <X className="h-4 w-4 mr-2" />
                  Rejeter
                </Button>
              </>
            )}
        </div>
      </div>
    );
  };

  // Gestionnaire de téléchargement
  const handleDownload = async () => {
    try {
      if (!certificate.isPaid) {
        // Démarrer le processus de paiement
        await startPaymentFlow({
          amount: certificate.price || 10000, // Prix par défaut si non défini
          certificateId: certificate.$id,
          returnUrl: `/dashboard/certificates/${certificate.$id}?action=download`,
        });
        return;
      }

      // Si déjà payé, procéder au téléchargement
      setIsDownloading(true);
      const response = await downloadCertificate(certificate.$id);

      if (response.success && response.documentUrl) {
        const link = document.createElement("a");
        link.target = "_blank";
        link.href = response.documentUrl;
        link.download = `certificat-${certificate.reference}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast({
          title: "Téléchargement réussi",
          description: "Le certificat a été téléchargé avec succès.",
        });
      }
    } catch (error: any) {
      toast({
        title: "Erreur",
        description:
          error.message || "Une erreur est survenue lors du téléchargement.",
        variant: "error",
      });
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Documents Justificatifs</CardTitle>
        <div className="flex items-center gap-2">
          {canUpload && (
            <div className="w-[200px]">
              <FileUpload
                endpoint={`certificates/${certificate.$id}/documents`}
                value={null}
                onChange={(file) => {
                  if (!file) return;
                  uploadCertificateDocument({
                    certificateId: certificate.$id,
                    file,
                  })
                    .then(() => {
                      queryClient.invalidateQueries({
                        queryKey: ["certificate", certificate.$id],
                      });
                      toast({
                        title: "Document ajouté",
                        description: "Le document a été ajouté avec succès.",
                      });
                    })
                    .catch((error) => {
                      toast({
                        title: "Erreur",
                        description:
                          "Une erreur est survenue lors de l'ajout du document.",
                        variant: "error",
                      });
                    });
                }}
                options={{
                  maxSize: 5 * 1024 * 1024,
                  acceptedTypes: ["application/pdf", "image/jpeg", "image/png"],
                  label: "Ajouter un document",
                  description: "PDF, JPG, PNG jusqu'à 5MB",
                }}
              />
            </div>
          )}
          {canDownload && (
            <Button
              variant="default"
              onClick={handleDownload}
              disabled={isDownloading || isPaymentProcessing}
              className={cn(
                !certificate.isPaid &&
                  "text-muted-foreground hover:text-foreground",
                certificate.isPaid &&
                  "bg-green-600 hover:bg-green-700 text-white",
                {
                  "opacity-50 cursor-not-allowed":
                    certificate.downloadedAt && userRole !== "admin",
                }
              )}
            >
              {isDownloading || isPaymentProcessing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              <span className="ml-2">
                {certificate.isPaid
                  ? "Télécharger"
                  : `Payer ${
                      certificate.price
                        ? (certificate.price / 100).toFixed(2)
                        : "10000,00"
                    } ${PAYMENT_CONFIG.orangeMoney.currency}`}
              </span>
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="identity" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="identity">
              Documents d'identité
              <Badge variant="secondary" className="ml-2">
                {identityDocs.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="request">
              Documents de la demande
              <Badge variant="secondary" className="ml-2">
                {requestDocs.length}
              </Badge>
            </TabsTrigger>
          </TabsList>
          <TabsContent value="identity">
            <div className="space-y-4">
              {identityDocs.length > 0 ? (
                identityDocs.map(renderDocument)
              ) : (
                <div className="text-center py-8 text-neutral-500">
                  Aucun document d'identité attaché
                </div>
              )}
            </div>
          </TabsContent>
          <TabsContent value="request">
            <div className="space-y-4">
              {requestDocs.length > 0 ? (
                requestDocs.map(renderDocument)
              ) : (
                <div className="text-center py-8 text-neutral-500">
                  Aucun document de demande attaché
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>

      <Dialog open={!!selectedDocument} onOpenChange={handleCloseDialog}>
        <DialogContent className="max-w-[95vw] w-full md:max-w-3xl h-[90vh] md:h-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{selectedDocument?.fileName ?? "Document sans nom"}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleCloseDialog(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="relative flex-1 w-full h-[calc(90vh-8rem)] md:h-[600px] bg-neutral-100 rounded-lg overflow-hidden">
            {selectedDocument?.fileUrl && !documentError ? (
              <div className="w-full h-full">
                <iframe
                  src={selectedDocument.fileUrl}
                  className="w-full h-full"
                  onError={handleDocumentError}
                  onLoad={(e) => {
                    const iframe = e.target as HTMLIFrameElement;
                    if (
                      iframe.contentWindow?.document.body.innerHTML.includes(
                        '{"code":401'
                      )
                    ) {
                      handleDocumentError();
                    }
                  }}
                />
                <div
                  className="absolute inset-0 pointer-events-none"
                  onClick={(e) => e.preventDefault()}
                />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-neutral-500 space-y-4">
                <FileText className="h-12 w-12" />
                <p className="text-center px-4">
                  {documentError
                    ? "Impossible de charger le document"
                    : "Document non disponible"}
                </p>
                {documentError && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setDocumentError(false);
                      if (selectedDocument) {
                        handleViewDocument(selectedDocument);
                      }
                    }}
                  >
                    Réessayer
                  </Button>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
