import { createHash } from "crypto";

/**
 * Génère un numéro d'identification unique pour un citoyen
 * Format: YYYYMMDD-YYYYMMDD-XXXXXXXX
 * - Premier bloc: Date de naissance (YYYYMMDD)
 * - Deuxième bloc: Date d'inscription (YYYYMMDD)
 * - Troisième bloc: Hash SHA256 tronqué à 8 caractères pour l'unicité
 *
 * @param birthDate Date de naissance du citoyen
 * @param registrationDate Date d'inscription du citoyen
 * @param databaseId Identifiant unique de la base de données
 * @returns Numéro d'identification unique au format standardisé
 */
export function generateCitizenId(
  birthDate: Date,
  registrationDate: Date,
  databaseId: string
): string {
  // Validation des paramètres d'entrée
  if (!birthDate || !registrationDate || !databaseId) {
    throw new Error(
      "Tous les paramètres sont requis pour générer l'ID citoyen"
    );
  }

  if (birthDate > registrationDate) {
    throw new Error(
      "La date de naissance ne peut pas être postérieure à la date d'inscription"
    );
  }

  // Formatage des dates au format YYYYMMDD
  const birthDateStr = birthDate.toISOString().slice(0, 10).replace(/-/g, "");
  const registrationDateStr = registrationDate
    .toISOString()
    .slice(0, 10)
    .replace(/-/g, "");

  // Génération du hash pour garantir l'unicité
  const hash = createHash("sha256");
  hash.update(`${birthDateStr}-${registrationDateStr}-${databaseId}`);
  const hashedId = hash.digest("hex").slice(0, 8).toUpperCase();

  return `${birthDateStr}-${registrationDateStr}-${hashedId}`;
}

/**
 * Valide le format d'un numéro d'identification citoyen
 * @param citizenId Le numéro d'identification à valider
 * @returns true si le format est valide, false sinon
 */
export function isValidCitizenId(citizenId: string): boolean {
  const pattern = /^\d{8}-\d{8}-[A-F0-9]{8}$/;
  return pattern.test(citizenId);
}

/**
 * Extrait les informations d'un numéro d'identification citoyen
 * @param citizenId Le numéro d'identification à analyser
 * @returns Objet contenant les dates extraites ou null si invalide
 */
export function parseCitizenId(citizenId: string): {
  birthDate: Date;
  registrationDate: Date;
  hash: string;
} | null {
  if (!isValidCitizenId(citizenId)) {
    return null;
  }

  const parts = citizenId.split("-");
  const birthDateStr = parts[0];
  const registrationDateStr = parts[1];
  const hash = parts[2];

  try {
    const birthDate = new Date(
      parseInt(birthDateStr.substring(0, 4)),
      parseInt(birthDateStr.substring(4, 6)) - 1,
      parseInt(birthDateStr.substring(6, 8))
    );

    const registrationDate = new Date(
      parseInt(registrationDateStr.substring(0, 4)),
      parseInt(registrationDateStr.substring(4, 6)) - 1,
      parseInt(registrationDateStr.substring(6, 8))
    );

    return { birthDate, registrationDate, hash };
  } catch {
    return null;
  }
}
