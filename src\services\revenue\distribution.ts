import { createAdminClient } from "@/lib/server/appwrite";
import {
  DATABASE_ID,
  REVENUE_DISTRIBUTIONS_COLLECTION_ID,
} from "@/lib/server/database";
import { ID } from "node-appwrite";

/**
 * Revenue Distribution Percentages
 * - Platform (Project Initiator): 15%
 * - Communes: 10%
 * - Neighborhoods (Quartiers): 25%
 * - Project Funding Partner: 10%
 * - Employee Payroll: 40%
 */
export const REVENUE_PERCENTAGES = {
  PLATFORM: 0.15,
  COMMUNE: 0.1,
  QUARTIER: 0.25,
  PARTNER: 0.1,
  PAYROLL: 0.4,
} as const;

export interface RevenueDistribution {
  paymentId: string;
  orderId: string;
  totalAmount: number;
  platformAmount: number;
  communeAmount: number;
  quartierAmount: number;
  partnerAmount: number;
  payrollAmount: number;
  distributedAt: string;
  status: "pending" | "completed" | "failed";
}

export class RevenueDistributionService {
  private static instance: RevenueDistributionService;

  private constructor() {}

  static getInstance(): RevenueDistributionService {
    if (!this.instance) {
      this.instance = new RevenueDistributionService();
    }
    return this.instance;
  }

  /**
   * Calculate revenue distribution amounts
   */
  calculateDistribution(
    totalAmount: number
  ): Omit<
    RevenueDistribution,
    "paymentId" | "orderId" | "distributedAt" | "status"
  > {
    const platformAmount = Math.round(
      totalAmount * REVENUE_PERCENTAGES.PLATFORM
    );
    const communeAmount = Math.round(totalAmount * REVENUE_PERCENTAGES.COMMUNE);
    const quartierAmount = Math.round(
      totalAmount * REVENUE_PERCENTAGES.QUARTIER
    );
    const partnerAmount = Math.round(totalAmount * REVENUE_PERCENTAGES.PARTNER);
    const payrollAmount = Math.round(totalAmount * REVENUE_PERCENTAGES.PAYROLL);

    // Ensure total adds up correctly (handle rounding differences)
    const calculatedTotal =
      platformAmount +
      communeAmount +
      quartierAmount +
      partnerAmount +
      payrollAmount;
    const difference = totalAmount - calculatedTotal;

    // Add any rounding difference to the largest amount (payroll)
    const adjustedPayrollAmount = payrollAmount + difference;

    return {
      totalAmount,
      platformAmount,
      communeAmount,
      quartierAmount,
      partnerAmount,
      payrollAmount: adjustedPayrollAmount,
    };
  }

  /**
   * Create and store revenue distribution record
   */
  async createDistribution(params: {
    paymentId: string;
    orderId: string;
    totalAmount: number;
  }): Promise<RevenueDistribution> {
    try {
      const { databases } = await createAdminClient();

      const distribution = this.calculateDistribution(params.totalAmount);
      const distributedAt = new Date().toISOString();

      const distributionRecord = await databases.createDocument(
        DATABASE_ID,
        REVENUE_DISTRIBUTIONS_COLLECTION_ID,
        ID.unique(),
        {
          paymentId: params.paymentId,
          orderId: params.orderId,
          totalAmount: params.totalAmount.toString(),
          platformAmount: distribution.platformAmount.toString(),
          communeAmount: distribution.communeAmount.toString(),
          quartierAmount: distribution.quartierAmount.toString(),
          partnerAmount: distribution.partnerAmount.toString(),
          payrollAmount: distribution.payrollAmount.toString(),
          distributedAt,
          status: "completed", // Marquer directement comme complété
          createdAt: distributedAt,
          updatedAt: distributedAt,
        }
      );

      console.log(
        `Revenue distribution created for payment ${params.paymentId}:`,
        {
          total: params.totalAmount,
          platform: distribution.platformAmount,
          commune: distribution.communeAmount,
          quartier: distribution.quartierAmount,
          partner: distribution.partnerAmount,
          payroll: distribution.payrollAmount,
        }
      );

      return {
        paymentId: params.paymentId,
        orderId: params.orderId,
        totalAmount: params.totalAmount,
        platformAmount: distribution.platformAmount,
        communeAmount: distribution.communeAmount,
        quartierAmount: distribution.quartierAmount,
        partnerAmount: distribution.partnerAmount,
        payrollAmount: distribution.payrollAmount,
        distributedAt,
        status: "completed",
      };
    } catch (error) {
      console.error("Error creating revenue distribution:", error);
      throw new Error("Failed to create revenue distribution");
    }
  }

  /**
   * Process revenue distribution (mark as completed)
   */
  async processDistribution(paymentId: string): Promise<void> {
    try {
      const { databases } = await createAdminClient();

      // Find the distribution record
      const distributions = await databases.listDocuments(
        DATABASE_ID,
        REVENUE_DISTRIBUTIONS_COLLECTION_ID,
        [`paymentId="${paymentId}"`]
      );

      if (distributions.documents.length === 0) {
        throw new Error("Distribution record not found");
      }

      const distribution = distributions.documents[0];

      // Update status to completed
      await databases.updateDocument(
        DATABASE_ID,
        REVENUE_DISTRIBUTIONS_COLLECTION_ID,
        distribution.$id,
        {
          status: "completed",
          updatedAt: new Date().toISOString(),
        }
      );

      console.log(`Revenue distribution processed for payment ${paymentId}`);
    } catch (error) {
      console.error("Error processing revenue distribution:", error);
      throw new Error("Failed to process revenue distribution");
    }
  }

  /**
   * Mark distribution as failed
   */
  async markDistributionFailed(
    paymentId: string,
    reason?: string
  ): Promise<void> {
    try {
      const { databases } = await createAdminClient();

      const distributions = await databases.listDocuments(
        DATABASE_ID,
        REVENUE_DISTRIBUTIONS_COLLECTION_ID,
        [`paymentId="${paymentId}"`]
      );

      if (distributions.documents.length === 0) {
        throw new Error("Distribution record not found");
      }

      const distribution = distributions.documents[0];

      await databases.updateDocument(
        DATABASE_ID,
        REVENUE_DISTRIBUTIONS_COLLECTION_ID,
        distribution.$id,
        {
          status: "failed",
          updatedAt: new Date().toISOString(),
          ...(reason && { failureReason: reason }),
        }
      );

      console.error(
        `Revenue distribution failed for payment ${paymentId}: ${reason}`
      );
    } catch (error) {
      console.error("Error marking distribution as failed:", error);
      throw new Error("Failed to update distribution status");
    }
  }

  /**
   * Get distribution by payment ID
   */
  async getDistributionByPaymentId(
    paymentId: string
  ): Promise<RevenueDistribution | null> {
    try {
      const { databases } = await createAdminClient();

      const distributions = await databases.listDocuments(
        DATABASE_ID,
        REVENUE_DISTRIBUTIONS_COLLECTION_ID,
        [`paymentId="${paymentId}"`]
      );

      if (distributions.documents.length === 0) {
        return null;
      }

      const doc = distributions.documents[0];
      return {
        paymentId: doc.paymentId,
        orderId: doc.orderId,
        totalAmount: parseInt(doc.totalAmount),
        platformAmount: parseInt(doc.platformAmount),
        communeAmount: parseInt(doc.communeAmount),
        quartierAmount: parseInt(doc.quartierAmount),
        partnerAmount: parseInt(doc.partnerAmount),
        payrollAmount: parseInt(doc.payrollAmount),
        distributedAt: doc.distributedAt,
        status: doc.status,
      };
    } catch (error) {
      console.error("Error getting distribution:", error);
      return null;
    }
  }

  /**
   * Get revenue summary for a date range
   */
  async getRevenueSummary(
    startDate: string,
    endDate: string
  ): Promise<{
    totalRevenue: number;
    totalDistributions: number;
    platformTotal: number;
    communeTotal: number;
    quartierTotal: number;
    partnerTotal: number;
    payrollTotal: number;
  }> {
    try {
      const { databases } = await createAdminClient();

      // Vérifier si la collection existe et récupérer les distributions
      let distributions;
      try {
        distributions = await databases.listDocuments(
          DATABASE_ID,
          REVENUE_DISTRIBUTIONS_COLLECTION_ID,
          [
            `distributedAt>="${startDate}"`,
            `distributedAt<="${endDate}"`,
            `status="completed"`,
          ]
        );
      } catch (collectionError: any) {
        // Si la collection n'existe pas ou est vide, retourner des valeurs par défaut
        console.warn(
          "Revenue distributions collection not found or empty, returning default values:",
          collectionError.message
        );
        return {
          totalRevenue: 0,
          totalDistributions: 0,
          platformTotal: 0,
          communeTotal: 0,
          quartierTotal: 0,
          partnerTotal: 0,
          payrollTotal: 0,
        };
      }

      // Si aucun document trouvé, retourner des valeurs par défaut
      if (!distributions.documents || distributions.documents.length === 0) {
        return {
          totalRevenue: 0,
          totalDistributions: 0,
          platformTotal: 0,
          communeTotal: 0,
          quartierTotal: 0,
          partnerTotal: 0,
          payrollTotal: 0,
        };
      }

      const summary = distributions.documents.reduce(
        (acc, doc) => ({
          totalRevenue: acc.totalRevenue + parseInt(doc.totalAmount || "0"),
          totalDistributions: acc.totalDistributions + 1,
          platformTotal:
            acc.platformTotal + parseInt(doc.platformAmount || "0"),
          communeTotal: acc.communeTotal + parseInt(doc.communeAmount || "0"),
          quartierTotal:
            acc.quartierTotal + parseInt(doc.quartierAmount || "0"),
          partnerTotal: acc.partnerTotal + parseInt(doc.partnerAmount || "0"),
          payrollTotal: acc.payrollTotal + parseInt(doc.payrollAmount || "0"),
        }),
        {
          totalRevenue: 0,
          totalDistributions: 0,
          platformTotal: 0,
          communeTotal: 0,
          quartierTotal: 0,
          partnerTotal: 0,
          payrollTotal: 0,
        }
      );

      return summary;
    } catch (error) {
      console.error("Error getting revenue summary:", error);
      // Retourner des valeurs par défaut au lieu de lancer une erreur
      return {
        totalRevenue: 0,
        totalDistributions: 0,
        platformTotal: 0,
        communeTotal: 0,
        quartierTotal: 0,
        partnerTotal: 0,
        payrollTotal: 0,
      };
    }
  }

  /**
   * Get revenue distribution analytics
   */
  async getRevenueAnalytics(params: {
    startDate: string;
    endDate: string;
    groupBy?: "day" | "week" | "month";
  }): Promise<{
    totalRevenue: number;
    totalTransactions: number;
    averageTransaction: number;
    distributionBreakdown: {
      platform: number;
      commune: number;
      quartier: number;
      partner: number;
      payroll: number;
    };
    trends: Array<{
      date: string;
      revenue: number;
      transactions: number;
    }>;
  }> {
    try {
      const { databases } = await createAdminClient();

      const distributions = await databases.listDocuments(
        DATABASE_ID,
        REVENUE_DISTRIBUTIONS_COLLECTION_ID,
        [
          `distributedAt>="${params.startDate}"`,
          `distributedAt<="${params.endDate}"`,
          `status="completed"`,
        ]
      );

      const totalRevenue = distributions.documents.reduce(
        (sum, doc) => sum + parseInt(doc.totalAmount),
        0
      );

      const totalTransactions = distributions.documents.length;
      const averageTransaction =
        totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

      const distributionBreakdown = distributions.documents.reduce(
        (acc, doc) => ({
          platform: acc.platform + parseInt(doc.platformAmount),
          commune: acc.commune + parseInt(doc.communeAmount),
          quartier: acc.quartier + parseInt(doc.quartierAmount),
          partner: acc.partner + parseInt(doc.partnerAmount),
          payroll: acc.payroll + parseInt(doc.payrollAmount),
        }),
        { platform: 0, commune: 0, quartier: 0, partner: 0, payroll: 0 }
      );

      // Group by date for trends (simplified - would need more complex logic for week/month grouping)
      const trendsMap = new Map<
        string,
        { revenue: number; transactions: number }
      >();

      distributions.documents.forEach((doc) => {
        const date = doc.distributedAt.split("T")[0]; // Get date part only
        const existing = trendsMap.get(date) || { revenue: 0, transactions: 0 };
        trendsMap.set(date, {
          revenue: existing.revenue + parseInt(doc.totalAmount),
          transactions: existing.transactions + 1,
        });
      });

      const trends = Array.from(trendsMap.entries())
        .map(([date, data]) => ({
          date,
          ...data,
        }))
        .sort((a, b) => a.date.localeCompare(b.date));

      return {
        totalRevenue,
        totalTransactions,
        averageTransaction,
        distributionBreakdown,
        trends,
      };
    } catch (error) {
      console.error("Error getting revenue analytics:", error);
      throw new Error("Failed to get revenue analytics");
    }
  }
}
