"use client";

import { cn } from "@/lib/utils/cn";
import { DashboardNav } from "./nav";
import { UserNav } from "./user-nav";
import { motion, HTMLMotionProps } from "framer-motion";
import { Search } from "@/components/dashboard/search";

interface DashboardShellProps extends HTMLMotionProps<"div"> {}

export function DashboardShell({
  children,
  className,
  ...props
}: DashboardShellProps) {
  return (
    <div className="flex min-h-screen">
      {/* Sidebar */}
      <DashboardNav className="hidden lg:block" />

      {/* Main Content */}
      <main className="flex-1 bg-background">
        <div className="border-b">
          <div className="flex h-16 items-center justify-between px-4">
            <div className="flex items-center gap-4 lg:gap-6">
              {/* Mobile Nav Toggle */}
              <button className="lg:hidden">
                <span className="sr-only">Open sidebar</span>
                <svg
                  className="h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
              {/* Search */}
              <Search />
            </div>
            {/* User Navigation */}
            <UserNav />
          </div>
        </div>

        {/* Page Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className={cn("container space-y-8 py-8", className)}
          {...props}
        >
          {children}
        </motion.div>
      </main>
    </div>
  );
}
