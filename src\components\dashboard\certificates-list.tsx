"use client";

import { motion, AnimatePresence } from "framer-motion";
import { useCertificates } from "@/hooks/use-certificates";
import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { FileText, Clock, CheckCircle, XCircle, ChevronRight, Send, FileCheck, FileSignature, FileOutput, Ban } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import Link from "next/link";
import type { LucideIcon } from "lucide-react";

interface StatusConfig {
  icon: LucideIcon;
  color: string;
  bg: string;
  label: string;
}

const statusConfig: Record<CERTIFICATE_STATUS, StatusConfig> = {
  [CERTIFICATE_STATUS.DRAFT]: {
    icon: FileText,
    color: "text-neutral-600",
    bg: "bg-neutral-100",
    label: "Brouillon",
  },
  [CERTIFICATE_STATUS.SUBMITTED]: {
    icon: Send,
    color: "text-blue-600",
    bg: "bg-blue-50",
    label: "Soumis",
  },
  [CERTIFICATE_STATUS.PENDING]: {
    icon: Clock,
    color: "text-amber-600",
    bg: "bg-amber-50",
    label: "En cours",
  },
  [CERTIFICATE_STATUS.VERIFIED]: {
    icon: FileCheck,
    color: "text-teal-600",
    bg: "bg-teal-50",
    label: "Vérifié",
  },
  [CERTIFICATE_STATUS.APPROVED]: {
    icon: CheckCircle,
    color: "text-emerald-600",
    bg: "bg-emerald-50",
    label: "Approuvé",
  },
  [CERTIFICATE_STATUS.READY]: {
    icon: FileCheck,
    color: "text-violet-600",
    bg: "bg-violet-50",
    label: "Prêt",
  },
  [CERTIFICATE_STATUS.SIGNED]: {
    icon: FileSignature,
    color: "text-indigo-600",
    bg: "bg-indigo-50",
    label: "Signé",
  },
  [CERTIFICATE_STATUS.DELIVERED]: {
    icon: FileOutput,
    color: "text-green-600",
    bg: "bg-green-50",
    label: "Délivré",
  },
  [CERTIFICATE_STATUS.REJECTED]: {
    icon: XCircle,
    color: "text-red-600",
    bg: "bg-red-50",
    label: "Rejeté",
  },
  [CERTIFICATE_STATUS.EXPIRED]: {
    icon: Ban,
    color: "text-neutral-600",
    bg: "bg-neutral-100",
    label: "Expiré",
  },
};

export function CertificatesList() {
  const { certificates, isLoading } = useCertificates();

  return (
    <div className="space-y-6">
      <motion.h2
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-lg font-semibold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent"
      >
        Vos dernières demandes
      </motion.h2>

      <div className="space-y-4">
        <AnimatePresence>
          {certificates?.slice(0, 5).map((certificate, index) => {
            const status = statusConfig[certificate.status];
            const StatusIcon = status.icon;

            return (
              <motion.div
                key={certificate.$id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ delay: index * 0.1 }}
              >
                <Link href={`/certificates/${certificate.$id}`}>
                  <div className="group relative overflow-hidden rounded-xl border border-neutral-200/60 bg-white p-4
                    hover:shadow-lg hover:border-neutral-300/80 transition-all duration-300">
                    {/* Effet de brillance au survol */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent
                      translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000" />

                    <div className="relative flex items-center gap-4">
                      <div className={`w-10 h-10 rounded-lg ${status.bg} flex items-center justify-center
                        group-hover:scale-110 transition-transform duration-300`}>
                        <StatusIcon className={`w-5 h-5 ${status.color}`} />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <p className="font-medium text-neutral-900 truncate">
                            {certificate.reference}
                          </p>
                          <span className={`text-sm ${status.color}`}>
                            {status.label}
                          </span>
                        </div>
                        <p className="text-sm text-neutral-600 truncate">
                          {certificate.motif}
                        </p>
                      </div>

                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className="text-sm text-neutral-900">
                            {formatDistanceToNow(new Date(certificate.createdAt), {
                              addSuffix: true,
                              locale: fr,
                            })}
                          </p>
                        </div>
                        <ChevronRight className="w-5 h-5 text-neutral-400 group-hover:text-neutral-900
                          group-hover:translate-x-1 transition-all duration-300" />
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            );
          })}
        </AnimatePresence>

        {certificates?.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-8 text-neutral-600"
          >
            Aucune demande pour le moment
          </motion.div>
        )}
      </div>
    </div>
  );
}
