"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Certificate } from "@/actions/types";
import { Loader2 } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getChefAgents } from "@/actions/chef/certificates";
import { cn } from "@/lib/utils/cn";

interface ActionDialogProps {
  certificate?: Certificate;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm?: (data: any) => void;
  onSubmit?: (data: any) => void;
  isLoading?: boolean;
}

export function AssignDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
}: ActionDialogProps) {
  const [selectedAgent, setSelectedAgent] = useState("");

  const { data: agentsData, isLoading: isLoadingAgents } = useQuery({
    queryKey: ["chef-agents"],
    queryFn: getChefAgents,
  });

  const agents = agentsData?.agents || [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Assigner le certificat</DialogTitle>
          <DialogDescription>
            Sélectionnez un agent pour traiter ce certificat
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <Select value={selectedAgent} onValueChange={setSelectedAgent}>
            <SelectTrigger>
              <SelectValue placeholder="Sélectionner un agent" />
            </SelectTrigger>
            <SelectContent>
              {isLoadingAgents ? (
                <SelectItem value="loading" disabled>
                  Chargement des agents...
                </SelectItem>
              ) : agents.length === 0 ? (
                <SelectItem value="none" disabled>
                  Aucun agent disponible
                </SelectItem>
              ) : (
                agents.map((agent) => (
                  <SelectItem key={agent.id} value={agent.id}>
                    {`${agent.prenom} ${agent.nom} (${agent.email})`}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Annuler
          </Button>
          <Button
            onClick={() => onSubmit?.({ agentId: selectedAgent })}
            disabled={!selectedAgent || isLoading || isLoadingAgents}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Assigner
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function RejectDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
}: ActionDialogProps) {
  const [reason, setReason] = useState("");
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = () => {
    if (!reason.trim()) {
      setError("La raison du rejet est obligatoire");
      return;
    }
    setError(null);
    onSubmit?.({ reason: reason.trim() });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Rejeter le certificat</DialogTitle>
          <DialogDescription>
            Veuillez fournir une raison détaillée pour le rejet de ce
            certificat. Cette information sera visible par le demandeur.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <Textarea
            placeholder="Raison du rejet..."
            value={reason}
            onChange={(e) => {
              setReason(e.target.value);
              if (error) setError(null);
            }}
            className={cn(
              "min-h-[100px] resize-none",
              error && "border-red-500 focus-visible:ring-red-500"
            )}
          />
          {error && <p className="text-sm text-red-500 mt-2">{error}</p>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Annuler
          </Button>
          <Button
            variant="destructive"
            onClick={handleSubmit}
            disabled={isLoading}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Rejeter
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function VerifyDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
}: ActionDialogProps) {
  const [notes, setNotes] = useState("");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Vérifier le certificat</DialogTitle>
          <DialogDescription>
            Ajoutez des notes optionnelles pour la vérification
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <Textarea
            placeholder="Notes de vérification (optionnel)..."
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Annuler
          </Button>
          <Button onClick={() => onSubmit?.({ notes })} disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Vérifier
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function UnassignDialog({
  certificate,
  open,
  onOpenChange,
  onConfirm,
  isLoading,
}: ActionDialogProps) {
  if (!certificate) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Désassigner l'agent</DialogTitle>
          <DialogDescription>
            Êtes-vous sûr de vouloir retirer l'agent du traitement de ce
            certificat ? Cette action permettra de réassigner le certificat à un
            autre agent.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1 space-y-1">
              <p className="text-sm font-medium leading-none">
                Référence du certificat
              </p>
              <p className="text-sm text-muted-foreground">
                {certificate.reference}
              </p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Annuler
          </Button>
          <Button
            variant="destructive"
            onClick={() => onConfirm?.({})}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Désassignation...
              </>
            ) : (
              "Confirmer la désassignation"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
