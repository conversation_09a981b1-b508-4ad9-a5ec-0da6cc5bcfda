import { UserAvatar } from "@/components/ui/user-avatar";

interface Author {
  name: string;
  avatarUrl?: string;
}

interface Comment {
  author: Author;
  content: string;
}

interface CommentItemProps {
  comment: Comment;
}

export function CommentItem({ comment }: CommentItemProps) {
  return (
    <div className="flex space-x-3">
      <UserAvatar
        name={comment.author.name}
        src={comment.author.avatarUrl}
        size="sm"
      />
      <div>
        <p className="font-medium">{comment.author.name}</p>
        <p className="text-neutral-600">{comment.content}</p>
      </div>
    </div>
  );
}