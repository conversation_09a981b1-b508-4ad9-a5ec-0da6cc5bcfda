import { useCertificateActions } from "@/hooks/use-certificate-actions";
import { useToast } from "@/hooks/use-toast";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { useRouter } from "next/navigation";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { CertificateActions } from "../certificate-actions";

// Mock dependencies
vi.mock("next/navigation");
vi.mock("@/hooks/use-certificate-actions");
vi.mock("@/hooks/use-toast");
vi.mock("@/hooks/use-auth", () => ({
  useAuth: () => ({ user: { $id: "test-user", prefs: { role: "chef" } } }),
}));
vi.mock("@/hooks/use-certificate-navigation", () => ({
  useCertificateNavigation: () => ({
    navigateBack: vi.fn(),
    navigateToDetails: vi.fn(),
    isDetailsPage: false,
  }),
}));
vi.mock("@/hooks/use-certificate-permissions", () => ({
  useCertificatePermissions: () => ({
    canDeliver: true,
    canMarkAsReady: false,
    canAssign: false,
    canUnassign: false,
    canReject: false,
    canVerify: false,
  }),
}));
vi.mock("@/hooks/use-payment-flow", () => ({
  usePaymentFlow: () => ({
    startPaymentFlow: vi.fn(),
    isProcessing: false,
  }),
}));

const mockCertificate = {
  $id: "test-cert-id",
  reference: "NCR-2024-001",
  status: "signed",
  isPaid: true,
  price: 10000,
  chefId: "test-user",
};

describe("CertificateActions - Deliver Functionality", () => {
  let queryClient: QueryClient;
  let mockRouter: any;
  let mockDeliver: any;
  let mockToast: any;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockRouter = {
      push: vi.fn(),
    };
    mockDeliver = vi.fn();
    mockToast = vi.fn();

    (useRouter as any).mockReturnValue(mockRouter);
    (useCertificateActions as any).mockReturnValue({
      deliver: mockDeliver,
      isLoading: false,
    });
    (useToast as any).mockReturnValue({
      toast: mockToast,
    });
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <CertificateActions
          certificate={mockCertificate as any}
          userRole="chef"
        />
      </QueryClientProvider>
    );
  };

  it("should show deliver action for signed certificate", () => {
    renderComponent();

    const deliverButton = screen.getByText("Délivrer");
    expect(deliverButton).toBeDefined();
    expect(deliverButton.textContent).toBe("Délivrer");
  });

  it("should handle successful delivery with timeout protection", async () => {
    mockDeliver.mockResolvedValue({ success: true });

    renderComponent();

    const deliverButton = screen.getByText("Délivrer");
    fireEvent.click(deliverButton);

    // Should show loading toast
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "Délivrance en cours",
        description:
          "Veuillez patienter pendant la délivrance du certificat...",
      });
    });

    // Should call deliver function
    expect(mockDeliver).toHaveBeenCalledWith("test-cert-id");

    // Should show success toast and navigate
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "Certificat délivré",
        description: "Le certificat a été délivré avec succès.",
        variant: "default",
      });
      expect(mockRouter.push).toHaveBeenCalledWith(
        "/dashboard/certificates/test-cert-id"
      );
    });
  });

  it("should handle delivery timeout with fallback navigation", async () => {
    // Simulate timeout
    mockDeliver.mockRejectedValue(
      new Error("Timeout: La délivrance prend trop de temps")
    );

    renderComponent();

    const deliverButton = screen.getByText("Délivrer");
    fireEvent.click(deliverButton);

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "Erreur lors de la délivrance",
        description: "Timeout: La délivrance prend trop de temps",
        variant: "destructive",
      });
      // Should navigate to deliver page as fallback
      expect(mockRouter.push).toHaveBeenCalledWith(
        "/dashboard/certificates/test-cert-id/deliver"
      );
    });
  });

  it("should handle PDF generation errors gracefully", async () => {
    mockDeliver.mockRejectedValue(
      new Error("PDF Generation Failed: Image loading timeout")
    );

    renderComponent();

    const deliverButton = screen.getByText("Délivrer");
    fireEvent.click(deliverButton);

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "Erreur lors de la délivrance",
        description: "PDF Generation Failed: Image loading timeout",
        variant: "destructive",
      });
    });
  });

  it("should prevent multiple simultaneous delivery attempts", async () => {
    mockDeliver.mockImplementation(
      () => new Promise((resolve) => setTimeout(resolve, 1000))
    );

    renderComponent();

    const deliverButton = screen.getByText("Délivrer");

    // Click multiple times rapidly
    fireEvent.click(deliverButton);
    fireEvent.click(deliverButton);
    fireEvent.click(deliverButton);

    // Should only call deliver once
    expect(mockDeliver).toHaveBeenCalledTimes(1);
  });
});
