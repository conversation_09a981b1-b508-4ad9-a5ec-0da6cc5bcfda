import { visionContent } from "@/data/home-content";
import { motion } from "framer-motion";

export function VisionSection() {
  return (
    <section className="py-12 md:py-16 xl:py-24 bg-gradient-to-br from-accent-primary/5 to-accent-secondary/5">
      <div className="container mx-auto px-4 md:px-6 xl:px-8 max-w-7xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-10 md:mb-14 xl:mb-20"
        >
          <h2 className="text-2xl md:text-3xl xl:text-5xl font-bold bg-gradient-to-r from-[#004D40] to-[#00796B] bg-clip-text text-transparent leading-tight">
            {visionContent.title}
          </h2>
          <p className="mt-3 md:mt-4 text-sm md:text-base xl:text-lg text-neutral-600 max-w-2xl md:max-w-3xl mx-auto">
            {visionContent.subtitle}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 xl:gap-8">
          {visionContent.pillars.map((pillar, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 2 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-accent-primary/5 to-accent-secondary/5 rounded-xl md:rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500" />

              <div className="relative p-5 md:p-6 xl:p-8 rounded-xl md:rounded-2xl bg-white/80 backdrop-blur-sm border border-neutral-200/60 shadow-md hover:shadow-xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="inline-flex items-center justify-center w-12 h-12 md:w-14 md:h-14 xl:w-16 xl:h-16 mb-4 md:mb-5 xl:mb-6 rounded-xl bg-gradient-to-br from-accent-primary/10 to-accent-secondary/10">
                  <span className="text-2xl md:text-3xl xl:text-4xl">
                    {pillar.icon}
                  </span>
                </div>

                <motion.h3
                  initial={{ backgroundPosition: "0% 50%" }}
                  whileHover={{ backgroundPosition: "100% 50%" }}
                  className="text-lg md:text-xl xl:text-2xl font-semibold bg-gradient-to-r from-[#004D40] to-[#00796B] bg-clip-text text-transparent mb-2 md:mb-3 transition-all duration-500 bg-[length:200%_auto]"
                >
                  {pillar.title}
                </motion.h3>

                <motion.p
                  initial={{ opacity: 0.8 }}
                  whileHover={{ opacity: 1 }}
                  className="text-sm md:text-base text-neutral-600 leading-relaxed"
                >
                  {pillar.description}
                </motion.p>

                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-accent-primary/20 to-accent-secondary/20 rounded-b-xl md:rounded-b-2xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out" />
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
