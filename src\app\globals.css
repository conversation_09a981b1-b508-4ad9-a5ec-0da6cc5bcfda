@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Système de couleurs HSL */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* Couleurs principales */
    --primary-red: 0 84% 70%;
    --primary-yellow: 45 100% 54%;
    --primary-green: 134 61% 56%;

    /* Couleurs neutres */
    --neutral-50: 210 17% 98%;
    --neutral-100: 210 17% 95%;
    --neutral-200: 210 16% 93%;
    --neutral-300: 210 14% 89%;
    --neutral-400: 210 14% 83%;
    --neutral-500: 210 11% 71%;
    --neutral-600: 210 7% 56%;
    --neutral-700: 210 9% 31%;
    --neutral-800: 210 10% 23%;
    --neutral-900: 210 11% 15%;

    /* États sémantiques */
    --success: var(--primary-green);
    --warning: var(--primary-yellow);
    --error: var(--primary-red);
    --info: 210 100% 56%;

    /* Effets et transitions */
    --ring: var(--primary-green);
    --radius: 0.75rem;

    /* Système d'alertes et états */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --success-base: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;
    --warning-base: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
  }

  /* .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
  } */
}

@layer base {
  * {
    @apply border-neutral-200;
  }

  body {
    @apply bg-background text-foreground antialiased selection:bg-green-200;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Amélioration du scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-neutral-100/50;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-accent-primary/80 hover:bg-accent-primary transition-colors duration-300 rounded-full;
  }

  /* Pour Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: theme("colors.accent.primary") theme("colors.neutral.100");
  }

  /* Pour Edge et autres navigateurs */
  ::-webkit-scrollbar-corner {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-button {
    @apply hidden;
  }

  /* Hover sur le track */
  ::-webkit-scrollbar-track:hover {
    @apply bg-neutral-100;
  }

  /* Active state sur le thumb */
  ::-webkit-scrollbar-thumb:active {
    @apply bg-accent-primary/90;
  }
}

@layer components {
  /* Effets visuels réutilisables */
  .glass-effect {
    @apply backdrop-blur-md bg-white/30 dark:bg-black/30;
  }

  .card-hover {
    @apply hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300;
  }

  .input-focus {
    @apply focus:ring-2 focus:ring-success/20 focus:border-success/80 transition-all duration-200;
  }

  /* Animations de chargement */
  .loading-pulse {
    @apply animate-pulse bg-neutral-200 rounded-md;
  }

  /* Effets de bordure */
  .border-gradient {
    @apply border border-transparent;
    background: linear-gradient(
        to right,
        hsl(var(--primary-red) / 0.2),
        hsl(var(--primary-yellow) / 0.2),
        hsl(var(--primary-green) / 0.2)
      )
      border-box;
  }

  /* Effets de survol */
  .hover-lift {
    @apply transition-transform duration-300 hover:-translate-y-1;
  }

  /* Classes pour l'avatar */
  .avatar-gradient {
    @apply bg-gradient-to-br from-[#004D40] via-[#00796B] to-[#009688];
  }

  .avatar-ring {
    @apply ring-2 ring-[#004D40]/10 shadow-lg shadow-[#004D40]/5;
  }

  .avatar-status-ring {
    @apply ring-2 ring-white shadow-lg shadow-[#004D40]/10;
  }

  /* Animation du gradient pour le bouton avatar */
  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .animate-gradient {
    animation: gradient 3s ease infinite;
    background-size: 200% 200%;
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
      opacity: 0;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  .animate-shimmer {
    animation: shimmer 3s infinite;
  }
}

/* Animations essentielles */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Accessibilité */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Utilitaires d'animation */
@layer utilities {
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }
}
