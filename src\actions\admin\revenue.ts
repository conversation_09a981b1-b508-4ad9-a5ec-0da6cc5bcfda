"use server";

import { getCurrentUser } from "@/actions/auth/session";
import { RevenueDistributionService } from "@/services/revenue/distribution";

/**
 * Server Action pour obtenir les statistiques de revenus
 */
export async function getRevenueStatisticsAction(params: {
  startDate: string;
  endDate: string;
}) {
  try {
    const { user } = await getCurrentUser();
    
    // Vérifier que l'utilisateur est admin
    if (!user || user.prefs?.role !== "admin") {
      return {
        success: false,
        error: "Accès non autorisé",
        statistics: null,
      };
    }

    const revenueService = RevenueDistributionService.getInstance();
    const summary = await revenueService.getRevenueSummary(
      params.startDate,
      params.endDate
    );

    return {
      success: true,
      statistics: summary,
      error: null,
    };
  } catch (error: any) {
    console.error("Erreur lors de la récupération des statistiques:", error);
    return {
      success: false,
      error: error.message || "Erreur lors de la récupération des statistiques",
      statistics: null,
    };
  }
}

/**
 * Server Action pour obtenir les analytics de revenus
 */
export async function getRevenueAnalyticsAction(params: {
  startDate: string;
  endDate: string;
  groupBy?: "day" | "week" | "month";
}) {
  try {
    const { user } = await getCurrentUser();
    
    // Vérifier que l'utilisateur est admin
    if (!user || user.prefs?.role !== "admin") {
      return {
        success: false,
        error: "Accès non autorisé",
        analytics: null,
      };
    }

    const revenueService = RevenueDistributionService.getInstance();
    const analytics = await revenueService.getRevenueAnalytics(params);

    return {
      success: true,
      analytics,
      error: null,
    };
  } catch (error: any) {
    console.error("Erreur lors de la récupération des analytics:", error);
    return {
      success: false,
      error: error.message || "Erreur lors de la récupération des analytics",
      analytics: null,
    };
  }
}

/**
 * Server Action pour obtenir une distribution par ID de paiement
 */
export async function getDistributionByPaymentIdAction(paymentId: string) {
  try {
    const { user } = await getCurrentUser();
    
    // Vérifier que l'utilisateur est admin
    if (!user || user.prefs?.role !== "admin") {
      return {
        success: false,
        error: "Accès non autorisé",
        distribution: null,
      };
    }

    const revenueService = RevenueDistributionService.getInstance();
    const distribution = await revenueService.getDistributionByPaymentId(paymentId);

    return {
      success: true,
      distribution,
      error: null,
    };
  } catch (error: any) {
    console.error("Erreur lors de la récupération de la distribution:", error);
    return {
      success: false,
      error: error.message || "Erreur lors de la récupération de la distribution",
      distribution: null,
    };
  }
}
