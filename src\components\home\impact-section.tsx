import { impactContent } from "@/data/home-content";
import { motion } from "framer-motion";

export function ImpactSection() {
  return (
    <section className="py-12 md:py-16 xl:py-24 bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5">
      <div className="container mx-auto px-4 md:px-6 xl:px-8 max-w-7xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-10 md:mb-14 xl:mb-20"
        >
          <h2 className="text-2xl md:text-3xl xl:text-5xl font-bold bg-gradient-to-r from-[#004D40] to-[#00796B] bg-clip-text text-transparent leading-tight">
            {impactContent.title}
          </h2>
          <p className="mt-3 md:mt-4 text-sm md:text-base xl:text-lg text-neutral-600 max-w-2xl md:max-w-3xl mx-auto">
            {impactContent.subtitle}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6 xl:gap-8">
          {impactContent.categories.map((category, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-accent-primary/5 to-accent-secondary/5 rounded-xl md:rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500" />

              <div className="relative p-5 md:p-6 xl:p-8 rounded-xl md:rounded-2xl bg-white/80 backdrop-blur-sm border border-neutral-200/60 shadow-md hover:shadow-xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="flex items-start gap-3 md:gap-4 mb-5 md:mb-6 xl:mb-8">
                  <div className="text-2xl md:text-3xl xl:text-4xl shrink-0 p-2 md:p-3 bg-accent-primary/5 rounded-lg">
                    {category.icon}
                  </div>
                  <h3 className="text-lg md:text-xl xl:text-2xl font-semibold text-neutral-900 leading-tight pt-2">
                    {category.title}
                  </h3>
                </div>

                <div className="space-y-3 md:space-y-4 xl:space-y-5">
                  {category.benefits.map((benefit, idx) => (
                    <div
                      key={idx}
                      className="group/item p-3 rounded-lg hover:bg-accent-primary/5 transition-all duration-300 border border-transparent hover:border-accent-primary/10"
                    >
                      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-1 md:gap-3 mb-2">
                        <h4 className="font-semibold text-neutral-800 text-base">
                          {benefit.title}
                        </h4>
                        <div className="flex items-center gap-2 text-right shrink-0">
                          <span className="text-sm font-bold text-accent-primary bg-accent-primary/5 px-2 py-0.5 rounded">
                            {benefit.metric}
                          </span>
                          <span className="text-xs text-neutral-500 whitespace-nowrap hidden md:inline-block">
                            {benefit.trend}
                          </span>
                        </div>
                      </div>

                      <motion.p
                        initial={{ opacity: 0.8 }}
                        whileHover={{ opacity: 1 }}
                        className="text-sm text-neutral-600 leading-relaxed"
                      >
                        {benefit.description}
                      </motion.p>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
