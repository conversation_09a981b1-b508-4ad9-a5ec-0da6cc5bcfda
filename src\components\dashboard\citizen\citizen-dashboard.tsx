"use client";

import { motion } from "framer-motion";
import { UserAvatar } from "@/components/ui/user-avatar";
import { CitizenCertificatesList } from "@/components/dashboard/citizen/citizen-certificates-list";
import { CitizenRecentActivity } from "@/components/dashboard/citizen/citizen-recent-activity";
import type { User } from "@/types/auth";
import { CitizenStats } from "@/components/dashboard/citizen/citizen-stats";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import Link from "next/link";

interface CitizenDashboardProps {
  user: User;
}

export function CitizenDashboard({ user }: CitizenDashboardProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5">
      {/* Fond décoratif */}
      <div className="absolute inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(0,77,64,0.08),transparent_50%)]" />
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-[0.02]" />
      </div>

      <div className="container mx-auto px-6 py-8 max-w-[1600px] space-y-6">
        {/* En-tête avec avatar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60 p-8"
        >
          {/* Cercles décoratifs */}
          <div className="absolute -top-12 -right-12 w-32 h-32 bg-accent-primary/5 rounded-full blur-2xl" />
          <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-accent-secondary/5 rounded-full blur-2xl" />

          <div className="relative flex items-center justify-between">
            <div className="flex items-center gap-6">
              <UserAvatar
                name={user.name}
                src={user.prefs?.avatarUrl}
                size="lg"
                showStatus
                status="online"
                className="w-20 h-20 ring-4 ring-white shadow-xl"
              />
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent">
                  Bienvenue, {user.name}
                </h1>
                <p className="text-neutral-600 mt-1">
                  Gérez vos certificats de résidence et suivez leur statut
                </p>
              </div>
            </div>

            <Button
              asChild
              size="lg"
              className="relative overflow-hidden group bg-gradient-to-r from-accent-primary to-accent-secondary
                hover:from-accent-primary/90 hover:to-accent-secondary/90 text-white font-medium
                shadow-lg shadow-accent-primary/20 hover:shadow-xl hover:shadow-accent-primary/30
                transform hover:-translate-y-0.5 transition-all duration-300"
            >
              <Link href="/certificates/new">
                <span
                  className="absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(255,255,255,0.2)_50%,transparent_100%)]
                  translate-x-[-150%] group-hover:translate-x-[150%] duration-1000 transition-transform"
                />
                <PlusCircle className="mr-2 h-5 w-5" />
                Nouvelle demande
              </Link>
            </Button>
          </div>
        </motion.div>

        {/* Contenu principal */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Colonne gauche */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Statistiques */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60 p-8 relative overflow-hidden">
              <div className="absolute -top-12 -right-12 w-32 h-32 bg-accent-primary/5 rounded-full blur-2xl" />
              <CitizenStats />
            </div>

            {/* Liste des certificats */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60 p-8 relative overflow-hidden">
              <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-accent-secondary/5 rounded-full blur-2xl" />
              <CitizenCertificatesList limit={5} />
            </div>
          </motion.div>

          {/* Colonne droite */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-6"
          >
            {/* Activité récente */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60 p-8 relative overflow-hidden">
              <div className="absolute -bottom-12 -right-12 w-32 h-32 bg-accent-primary/5 rounded-full blur-2xl" />
              <CitizenRecentActivity limit={5} />
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
