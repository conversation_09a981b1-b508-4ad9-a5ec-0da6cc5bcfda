"use client";
import { useEffect, useRef } from "react";
import { motion } from "framer-motion";

export const BackgroundBeams = ({ className = "" }: { className?: string }) => {
  const beamRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!beamRef.current) return;

    const updateMousePosition = (ev: MouseEvent) => {
      if (!beamRef.current) return;
      const { clientX, clientY } = ev;
      beamRef.current.style.setProperty("--x", `${clientX}px`);
      beamRef.current.style.setProperty("--y", `${clientY}px`);
    };

    window.addEventListener("mousemove", updateMousePosition);

    return () => {
      window.removeEventListener("mousemove", updateMousePosition);
    };
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 0.5 }}
      transition={{ duration: 0.5 }}
      className={`pointer-events-none fixed inset-0 z-0 h-full w-full ${className}`}
      ref={beamRef}
    >
      <div className="absolute inset-0 z-[-1] bg-gradient-to-b from-neutral-950 via-neutral-900 to-neutral-950" />
      <div
        className="absolute inset-0 z-[-1] bg-[radial-gradient(circle_800px_at_var(--x)_var(--y),rgba(0,77,64,0.15),transparent_100%)]"
        style={{
          "--x": "0px",
          "--y": "0px",
        } as React.CSSProperties}
      />
      <div className="absolute inset-0 z-[-1] opacity-[0.03]">
        <div className="absolute h-full w-full bg-[linear-gradient(to_right,#8080800a_1px,transparent_1px),linear-gradient(to_bottom,#8080800a_1px,transparent_1px)] bg-[size:14px_24px]" />
      </div>
    </motion.div>
  );
};
