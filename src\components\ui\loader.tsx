"use client";

import * as React from "react";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils/cn";

interface LoaderProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  variant?: "default" | "primary" | "secondary" | "ghost" | "muted";
  text?: string;
  fullScreen?: boolean;
  className?: string;
}

const sizeConfig = {
  xs: "h-3 w-3",
  sm: "h-4 w-4",
  md: "h-6 w-6",
  lg: "h-8 w-8",
  xl: "h-12 w-12",
} as const;

const variantConfig = {
  default: "text-gray-400",
  primary: "text-accent-primary",
  secondary: "text-gray-600",
  ghost: "text-gray-300",
  muted: "text-muted-foreground",
} as const;

export function Loader({
  size = "md",
  variant = "primary",
  text,
  fullScreen = false,
  className,
  ...props
}: LoaderProps) {
  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    if (fullScreen) {
      return (
        <div className="fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
          {children}
        </div>
      );
    }
    return <>{children}</>;
  };

  return (
    <Wrapper>
      <div
        className={cn(
          "flex items-center justify-center gap-3",
          fullScreen && "flex-col",
          className
        )}
        {...props}
      >
        <Loader2
          className={cn(
            "animate-spin",
            sizeConfig[size],
            variantConfig[variant]
          )}
        />
        {text && (
          <span
            className={cn(
              "text-sm font-medium",
              variantConfig[variant],
              fullScreen && "text-base"
            )}
          >
            {text}
          </span>
        )}
      </div>
    </Wrapper>
  );
}
