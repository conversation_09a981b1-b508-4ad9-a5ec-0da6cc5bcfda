"use client";

import { useQuery } from "@tanstack/react-query";
import { getLocationData, type LocationFilters } from "@/actions/geo/location";

export function useGeoData(filters?: LocationFilters) {
  return useQuery({
    queryKey: ["geo-data", filters],
    queryFn: () => getLocationData(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000,   // 30 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}