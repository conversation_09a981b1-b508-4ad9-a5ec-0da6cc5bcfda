import { createAdminClient } from "@/lib/server/appwrite";
import {
  DATABASE_ID,
  PAYMENTS_COLLECTION_ID,
  PAYMENTS_METADATA_COLLECTION_ID,
} from "@/lib/server/database";
import { NextRequest, NextResponse } from "next/server";
import { ID, Query } from "node-appwrite";

export async function POST(request: NextRequest) {
  try {
    const payload = await request.json();
    console.log("WEBHOOK appelé!!!!");
    console.log({ request: payload });

    const { searchParams } = new URL(request.url);
    const reference = searchParams.get("transactionId");
    const orderId = searchParams.get("orderId");

    if (!reference) {
      return new NextResponse("Référence de transaction manquante", {
        status: 400,
      });
    }

    if (!orderId) {
      return new NextResponse("ID de commande manquant", { status: 400 });
    }

    const { databases } = await createAdminClient();

    // 1. Rechercher les metadata existantes pour cet orderId
    const existingMetadataQuery = await databases.listDocuments(
      DATABASE_ID,
      PAYMENTS_METADATA_COLLECTION_ID,
      [Query.equal("orderId", orderId), Query.limit(1)]
    );

    let metadataId;

    if (existingMetadataQuery.documents.length > 0) {
      // Mettre à jour les metadata existantes
      const existingMetadata = existingMetadataQuery.documents[0];
      await databases.updateDocument(
        DATABASE_ID,
        PAYMENTS_METADATA_COLLECTION_ID,
        existingMetadata.$id,
        {
          orderId,
          webhookReceivedAt: new Date().toISOString(),
          webhookReceivedPayload: JSON.stringify(payload),
        }
      );
      metadataId = existingMetadata.$id;
    } else {
      // Créer de nouvelles metadata si aucune n'existe (ne devrait jamais arriver)
      const newMetadata = await databases.createDocument(
        DATABASE_ID,
        PAYMENTS_METADATA_COLLECTION_ID,
        ID.unique(),
        {
          orderId,
          paymentReference: reference,
          type: "certificate_download",
          returnUrl: "returnUrl", // Ajout de returnUrl ici
          createdAt: new Date().toISOString(),
          certifcateId: "",
          webhookReceivedAt: new Date().toISOString(),
          webhookReceivedPayload: JSON.stringify(payload),
        }
      );
      metadataId = newMetadata.$id;
    }

    // 2. Mettre à jour la transaction avec la référence aux metadata
    await databases.updateDocument(
      DATABASE_ID,
      PAYMENTS_COLLECTION_ID,
      reference,
      {
        status: payload.status,
        updatedAt: new Date().toISOString(),
        ...(payload.status === "SUCCESS" && {
          completedAt: new Date().toISOString(),
        }),
        metadata: [metadataId],
      }
    );

    return new NextResponse("OK", { status: 200 });
  } catch (error) {
    console.error("Erreur webhook Orange Money:", error);
    return new NextResponse("Erreur interne", { status: 500 });
  }
}
