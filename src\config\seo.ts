import { APP_URL, buildUrl } from "./env"

export const siteConfig = {
	name: 'NCR - Numérisation des Certificats de Résidence',
	description:
		'Plateforme officielle de gestion des certificats de résidence en République de Guinée',
	url: APP_URL,
	ogImage: buildUrl('/images/og.png'),
	links: {
		twitter: 'https://twitter.com/NCRGuinee',
		github: 'https://github.com/NCRGuinee',
	},
	creator: 'Ouestech SARL',
	themeColor: '#00875A',
	manifest: '/site.webmanifest',
	keywords: [
		'certificat de résidence',
		'guinée',
		'numérisation',
		'documents officiels',
		'administration en ligne',
		'e-gouvernement',
		'administration',
		'service public',
		'ncr',
		'république de guinée',
	],
};

export type SiteConfig = typeof siteConfig
