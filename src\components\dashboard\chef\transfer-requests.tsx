"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { motion } from "framer-motion";
import {
  ArrowRight,
  Calendar,
  CheckCircle,
  Loader2,
  MapPin,
  User,
  XCircle,
} from "lucide-react";
import { useEffect, useState } from "react";

interface TransferRequest {
  $id: string;
  $collectionId: string;
  $databaseId: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  transferRequest: any;
  transferStatus?: string;
  transferApprovals: any;
  // Autres propriétés peuvent être présentes
  [key: string]: any;
}

export function TransferRequests() {
  const { toast } = useToast();
  const [requests, setRequests] = useState<TransferRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [processingType, setProcessingType] = useState<string | null>(null);
  const [rejectionReason, setRejectionReason] = useState("");
  const [showRejectForm, setShowRejectForm] = useState<string | null>(null);

  // Données de démonstration si aucune donnée réelle n'est disponible
  const demoRequests: TransferRequest[] = [
    {
      $id: "demo-1",
      $collectionId: "citizens",
      $databaseId: "ncr_database",
      $createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      $updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      $permissions: [],
      nom: "Kouassi",
      prenom: "Jean",
      email: "<EMAIL>",
      telephone: "+225 07 12 34 56 78",
      quartier: "Ancien Quartier",
      transferRequest: {
        newQuartierName: "Nouveau Quartier",
        newQuartierId: "quartier-123",
        newChefId: "chef-456",
        newQuartierInfo: {
          nom: "Nouveau Quartier",
          commune: "Yopougon",
          prefecture: "Abidjan",
          region: "Lagunes",
          sousPrefecture: "Yopougon",
        },
        reason: "Rapprochement familial",
        requestedAt: new Date(
          Date.now() - 2 * 24 * 60 * 60 * 1000
        ).toISOString(),
      },
      transferStatus: "pending",
      transferApprovals: {},
    },
    {
      $id: "demo-2",
      $collectionId: "citizens",
      $databaseId: "ncr_database",
      $createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      $updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      $permissions: [],
      nom: "Traoré",
      prenom: "Marie",
      email: "<EMAIL>",
      telephone: "+225 05 98 76 54 32",
      quartier: "Centre-ville",
      transferRequest: {
        newQuartierName: "Résidentiel",
        newQuartierId: "quartier-789",
        newChefId: "chef-101",
        newQuartierInfo: {
          nom: "Résidentiel",
          commune: "Cocody",
          prefecture: "Abidjan",
          region: "Lagunes",
          sousPrefecture: "Cocody",
        },
        reason: "Changement de travail",
        requestedAt: new Date(
          Date.now() - 5 * 24 * 60 * 60 * 1000
        ).toISOString(),
      },
      transferStatus: "pending",
      transferApprovals: {},
    },
  ];

  const loadRequests = async () => {
    try {
      setIsLoading(true);
      const { getTransferRequestsForChefAction } = await import(
        "@/actions/chef/transfer-requests"
      );
      const result = await getTransferRequestsForChefAction();
      if (result.success) {
        setRequests(result.requests);
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      console.error("Erreur lors du chargement des demandes:", error);

      // Utiliser les données de démonstration en cas d'erreur
      console.warn(
        "Utilisation des données de démonstration pour les demandes de transfert"
      );
      setRequests(demoRequests);

      toast({
        title: "Mode démonstration",
        description:
          "Affichage des données de démonstration (base de données non configurée)",
        variant: "warning",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadRequests();
  }, []);

  const handleApprove = async (
    citizenId: string,
    approvalType: "current_chef" | "new_chef"
  ) => {
    try {
      setProcessingId(citizenId);
      setProcessingType(approvalType);

      const { approveQuartierTransferAction } = await import(
        "@/actions/chef/transfer-requests"
      );
      const result = await approveQuartierTransferAction({
        citizenId,
        approval: approvalType,
        notes: "Approuvé par le chef de quartier",
      });

      if (result.success) {
        toast({
          title: "Demande approuvée",
          description: result.message,
          variant: "success",
        });

        await loadRequests(); // Recharger les demandes
      } else {
        throw new Error(result.error || "Erreur lors de l'approbation");
      }
    } catch (error: any) {
      console.error("Erreur lors de l'approbation du transfert:", error);
      toast({
        title: "Erreur",
        description: error?.message || "Erreur lors de l'approbation",
        variant: "error",
      });
    } finally {
      setProcessingId(null);
      setProcessingType(null);
    }
  };

  const handleReject = async (citizenId: string) => {
    if (!rejectionReason.trim()) {
      toast({
        title: "Motif requis",
        description: "Veuillez fournir un motif de rejet",
        variant: "warning",
      });
      return;
    }

    try {
      setProcessingId(citizenId);

      const { rejectQuartierTransferAction } = await import(
        "@/actions/chef/transfer-requests"
      );
      const result = await rejectQuartierTransferAction({
        citizenId,
        reason: rejectionReason.trim(),
      });

      if (result.success) {
        toast({
          title: "Demande rejetée",
          description: result.message,
          variant: "success",
        });

        setShowRejectForm(null);
        setRejectionReason("");
        await loadRequests(); // Recharger les demandes
      }
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error?.message || "Erreur lors du rejet",
        variant: "error",
      });
    } finally {
      setProcessingId(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "partially_approved":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "En attente";
      case "partially_approved":
        return "Partiellement approuvé";
      case "completed":
        return "Complété";
      case "rejected":
        return "Rejeté";
      default:
        return "Inconnu";
    }
  };

  if (isLoading) {
    return (
      <Card className="border-0 shadow-lg">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-accent-primary" />
            <p className="text-neutral-600">Chargement des demandes...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (requests.length === 0) {
    return (
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5 text-accent-primary" />
            Demandes de transfert
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-12">
          <MapPin className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-neutral-900 mb-2">
            Aucune demande de transfert
          </h3>
          <p className="text-neutral-600">
            Il n'y a actuellement aucune demande de transfert en attente.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="w-5 h-5 text-accent-primary" />
          Demandes de transfert ({requests.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {requests.map((request, index) => (
          <motion.div
            key={request.$id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="border border-neutral-200 rounded-lg p-4 space-y-4"
          >
            {/* En-tête de la demande */}
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-accent-primary/10 flex items-center justify-center">
                  <User className="w-5 h-5 text-accent-primary" />
                </div>
                <div>
                  <h4 className="font-semibold text-neutral-900">
                    {request.nom} {request.prenom}
                  </h4>
                  <p className="text-sm text-neutral-600">
                    Quartier actuel: {request.quartier}
                  </p>
                </div>
              </div>
              <Badge
                className={getStatusColor(request.transferStatus || "pending")}
              >
                {getStatusText(request.transferStatus || "pending")}
              </Badge>
            </div>

            {/* Détails du transfert */}
            {request.transferRequest && (
              <div className="bg-neutral-50 rounded-lg p-4 space-y-3">
                <div className="flex items-center gap-2">
                  <ArrowRight className="w-4 h-4 text-accent-primary" />
                  <span className="font-medium text-neutral-900">
                    Nouveau quartier: {request.transferRequest.newQuartierName}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm text-neutral-600">
                  <Calendar className="w-4 h-4" />
                  Demandé le{" "}
                  {new Date(
                    request.transferRequest.requestedAt
                  ).toLocaleDateString()}
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutral-700">
                    Motif:
                  </label>
                  <p className="text-sm text-neutral-600 bg-white p-3 rounded border">
                    {request.transferRequest.reason}
                  </p>
                </div>
              </div>
            )}

            {/* Approbations */}
            {request.transferApprovals &&
              Object.keys(request.transferApprovals).length > 0 && (
                <div className="space-y-2">
                  <h5 className="font-medium text-neutral-900">
                    Approbations:
                  </h5>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {request.transferApprovals.current_chef && (
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span>Chef actuel: Approuvé</span>
                      </div>
                    )}
                    {request.transferApprovals.new_chef && (
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span>Nouveau chef: Approuvé</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

            {/* Actions */}
            {request.transferStatus === "pending" ||
            request.transferStatus === "partially_approved" ? (
              <div className="flex flex-wrap gap-2 pt-2">
                <Button
                  size="sm"
                  onClick={() => handleApprove(request.$id, "current_chef")}
                  disabled={
                    processingId === request.$id &&
                    processingType === "current_chef"
                  }
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  {processingId === request.$id &&
                  processingType === "current_chef" ? (
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  ) : (
                    <CheckCircle className="w-4 h-4 mr-2" />
                  )}
                  Approuver (Chef actuel)
                </Button>

                <Button
                  size="sm"
                  onClick={() => handleApprove(request.$id, "new_chef")}
                  disabled={
                    processingId === request.$id &&
                    processingType === "new_chef"
                  }
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {processingId === request.$id &&
                  processingType === "new_chef" ? (
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  ) : (
                    <CheckCircle className="w-4 h-4 mr-2" />
                  )}
                  Approuver (Nouveau chef)
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    setShowRejectForm(
                      showRejectForm === request.$id ? null : request.$id
                    )
                  }
                  className="border-red-200 text-red-600 hover:bg-red-50"
                >
                  <XCircle className="w-4 h-4 mr-2" />
                  Rejeter
                </Button>
              </div>
            ) : null}

            {/* Formulaire de rejet */}
            {showRejectForm === request.$id && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="border-t border-neutral-200 pt-4 space-y-3"
              >
                <label className="text-sm font-medium text-neutral-700">
                  Motif du rejet:
                </label>
                <Textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Expliquez pourquoi vous rejetez cette demande..."
                  className="min-h-[80px]"
                />
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleReject(request.$id)}
                    disabled={
                      !rejectionReason.trim() || processingId === request.$id
                    }
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    {processingId === request.$id ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    ) : (
                      <XCircle className="w-4 h-4 mr-2" />
                    )}
                    Confirmer le rejet
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setShowRejectForm(null);
                      setRejectionReason("");
                    }}
                  >
                    Annuler
                  </Button>
                </div>
              </motion.div>
            )}
          </motion.div>
        ))}
      </CardContent>
    </Card>
  );
}
