<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="800px" viewBox="0 0 800 800" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <pattern id="topography" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse">
            <!-- Première couche de lignes -->
            <path d="M0,50 Q50,0 100,50 T200,50" fill="none" stroke="currentColor" stroke-width="1" opacity="0.3"/>
            <path d="M0,100 Q50,50 100,100 T200,100" fill="none" stroke="currentColor" stroke-width="1" opacity="0.2"/>
            <path d="M0,150 Q50,100 100,150 T200,150" fill="none" stroke="currentColor" stroke-width="1" opacity="0.1"/>
            
            <!-- Deuxi<PERSON> couche avec décalage -->
            <path d="M-50,25 Q0,-25 50,25 T150,25" fill="none" stroke="currentColor" stroke-width="0.5" opacity="0.15"/>
            <path d="M-50,75 Q0,25 50,75 T150,75" fill="none" stroke="currentColor" stroke-width="0.5" opacity="0.1"/>
            <path d="M-50,125 Q0,75 50,125 T150,125" fill="none" stroke="currentColor" stroke-width="0.5" opacity="0.05"/>
            
            <!-- Points d'intersection -->
            <circle cx="50" cy="50" r="1" fill="currentColor" opacity="0.4"/>
            <circle cx="150" cy="50" r="1" fill="currentColor" opacity="0.4"/>
            <circle cx="50" cy="150" r="1" fill="currentColor" opacity="0.4"/>
            <circle cx="150" cy="150" r="1" fill="currentColor" opacity="0.4"/>
            
            <!-- Motifs géométriques subtils -->
            <path d="M25,25 L35,25 L30,35 Z" fill="currentColor" opacity="0.1"/>
            <path d="M125,125 L135,125 L130,135 Z" fill="currentColor" opacity="0.1"/>
            <path d="M75,175 L85,175 L80,185 Z" fill="currentColor" opacity="0.1"/>
            
            <!-- Lignes de contour -->
            <path d="M0,0 L200,0" stroke="currentColor" stroke-width="0.5" opacity="0.05"/>
            <path d="M0,200 L200,200" stroke="currentColor" stroke-width="0.5" opacity="0.05"/>
            <path d="M0,0 L0,200" stroke="currentColor" stroke-width="0.5" opacity="0.05"/>
            <path d="M200,0 L200,200" stroke="currentColor" stroke-width="0.5" opacity="0.05"/>
        </pattern>
    </defs>
    <rect width="800" height="800" fill="url(#topography)"/>
</svg>
