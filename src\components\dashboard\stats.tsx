"use client";

import { motion } from "framer-motion";
import { FileText, Clock, CheckCircle, XCircle } from "lucide-react";
import { useCertificates } from "@/hooks/use-certificates";
import { CERTIFICATE_STATUS } from "@/actions/auth/constants";

const stats = [
  {
    name: "Total",
    icon: FileText,
    color: "from-blue-500 to-blue-600",
    bgColor: "bg-blue-50",
    textColor: "text-blue-600",
  },
  {
    name: "En cours",
    icon: Clock,
    color: "from-amber-500 to-amber-600",
    bgColor: "bg-amber-50",
    textColor: "text-amber-600",
  },
  {
    name: "Approuvés",
    icon: CheckCircle,
    color: "from-emerald-500 to-emerald-600",
    bgColor: "bg-emerald-50",
    textColor: "text-emerald-600",
  },
  {
    name: "Rejetés",
    icon: XCircle,
    color: "from-red-500 to-red-600",
    bgColor: "bg-red-50",
    textColor: "text-red-600",
  },
];

export function DashboardStats() {
  const { certificates } = useCertificates();

  const getCount = (status?: CERTIFICATE_STATUS) => {
    if (!status) return certificates?.length || 0;
    return certificates?.filter(cert => cert.status === status).length || 0;
  };

  return (
    <div className="space-y-6">
      <motion.h2
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-lg font-semibold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent"
      >
        Aperçu de vos demandes
      </motion.h2>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          const count = getCount(
            stat.name === "En cours" ? CERTIFICATE_STATUS.PENDING :
            stat.name === "Approuvés" ? CERTIFICATE_STATUS.APPROVED :
            stat.name === "Rejetés" ? CERTIFICATE_STATUS.REJECTED :
            undefined
          );

          return (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative overflow-hidden rounded-xl border border-neutral-200/60 bg-white p-4
                hover:shadow-lg hover:border-neutral-300/80 transition-all duration-300 group"
            >
              {/* Cercle décoratif */}
              <div className={`absolute -right-6 -top-6 w-16 h-16 rounded-full ${stat.bgColor} opacity-20
                group-hover:scale-150 transition-transform duration-700`} />

              <div className="relative space-y-3">
                <div className={`w-12 h-12 rounded-xl ${stat.bgColor} flex items-center justify-center
                  group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className={`w-6 h-6 ${stat.textColor}`} />
                </div>

                <div>
                  <div className="flex items-baseline gap-1">
                    <span className={`text-2xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`}>
                      {count}
                    </span>
                    <span className="text-sm text-neutral-600">demandes</span>
                  </div>
                  <p className="text-sm text-neutral-600">{stat.name}</p>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
}
