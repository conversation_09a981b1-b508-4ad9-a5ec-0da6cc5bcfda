"use client";

import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ROLES, STATUS } from "@/actions/auth/constants";

interface UserFiltersProps {
  filters: {
    role?: ROLES;
    status?: STATUS;
    search: string;
    page: number;
    limit: number;
  };
  onFiltersChange: (filters: any) => void;
}

export function UserFilters({ filters, onFiltersChange }: UserFiltersProps) {
  return (
    <div className="flex items-center space-x-4">
      <Input
        placeholder="Rechercher un utilisateur..."
        value={filters.search}
        onChange={(e) => onFiltersChange({ ...filters, search: e.target.value })}
        className="w-[300px]"
      />

      <Select
        value={filters.role || "all"}
        onValueChange={(value) =>
          onFiltersChange({
            ...filters,
            role: value === "all" ? undefined : value as ROLES
          })
        }
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Filtrer par rôle" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tous les rôles</SelectItem>
          {Object.values(ROLES).map((role) => (
            <SelectItem key={role} value={role}>
              {role.charAt(0).toUpperCase() + role.slice(1)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        value={filters.status || "all"}
        onValueChange={(value) =>
          onFiltersChange({
            ...filters,
            status: value === "all" ? undefined : value as STATUS
          })
        }
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Filtrer par statut" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tous les statuts</SelectItem>
          {Object.values(STATUS).map((status) => (
            <SelectItem key={status} value={status}>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}