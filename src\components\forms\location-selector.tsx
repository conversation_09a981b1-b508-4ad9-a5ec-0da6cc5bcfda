"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useGeoData } from "@/hooks/use-geo-data";
import { cn } from "@/lib/utils/cn";
import { useEffect, useState } from "react";

interface LocationSelectorProps {
  onLocationSelect: (location: {
    region: string;
    prefecture: string;
    commune: string;
    sousPrefecture: string;
    quartier: string;
  }) => void;
  className?: string;
}

export function LocationSelector({
  onLocationSelect,
  className,
}: LocationSelectorProps) {
  const [selectedRegion, setSelectedRegion] = useState<string>("");
  const [selectedPrefecture, setSelectedPrefecture] = useState<string>("");
  const [selectedCommune, setSelectedCommune] = useState<string>("");
  const [selectedSousPrefecture, setSelectedSousPrefecture] =
    useState<string>("");
  const [selectedQuartier, setSelectedQuartier] = useState<string>("");

  // Requêtes séparées avec les bons filtres
  const { data: regionsData } = useGeoData();

  const { data: prefecturesData } = useGeoData(
    selectedRegion ? { region: selectedRegion } : undefined
  );

  const { data: communesData } = useGeoData(
    selectedPrefecture
      ? {
          region: selectedRegion,
          prefecture: selectedPrefecture,
        }
      : undefined
  );

  const { data: locationData } = useGeoData(
    selectedCommune
      ? {
          region: selectedRegion,
          prefecture: selectedPrefecture,
          commune: selectedCommune,
        }
      : undefined
  );

  const handleRegionChange = (value: string) => {
    setSelectedRegion(value);
    setSelectedPrefecture("");
    setSelectedCommune("");
    setSelectedSousPrefecture("");
    setSelectedQuartier("");
  };

  const handlePrefectureChange = (value: string) => {
    setSelectedPrefecture(value);
    setSelectedCommune("");
    setSelectedSousPrefecture("");
    setSelectedQuartier("");
  };

  const handleCommuneChange = (value: string) => {
    setSelectedCommune(value);
    setSelectedSousPrefecture("");
    setSelectedQuartier("");
  };

  const handleSousPrefectureChange = (value: string) => {
    setSelectedSousPrefecture(value);
    setSelectedQuartier("");
  };

  useEffect(() => {
    if (
      selectedRegion &&
      selectedPrefecture &&
      selectedCommune &&
      selectedSousPrefecture &&
      selectedQuartier
    ) {
      onLocationSelect({
        region: selectedRegion,
        prefecture: selectedPrefecture,
        commune: selectedCommune,
        sousPrefecture: selectedSousPrefecture,
        quartier: selectedQuartier,
      });
    }
  }, [selectedQuartier]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Sélection de la région */}
      <Select value={selectedRegion} onValueChange={handleRegionChange}>
        <SelectTrigger>
          <SelectValue placeholder="Sélectionnez une région" />
        </SelectTrigger>
        <SelectContent>
          {regionsData?.success &&
            regionsData.data?.regions?.map((region: string) => (
              <SelectItem key={region} value={region}>
                {region}
              </SelectItem>
            ))}
        </SelectContent>
      </Select>

      {/* Sélection de la préfecture */}
      <Select
        value={selectedPrefecture}
        onValueChange={handlePrefectureChange}
        disabled={!selectedRegion}
      >
        <SelectTrigger>
          <SelectValue placeholder="Sélectionnez une préfecture" />
        </SelectTrigger>
        <SelectContent>
          {prefecturesData?.success &&
            prefecturesData.data?.prefectures?.map((prefecture: string) => (
              <SelectItem key={prefecture} value={prefecture}>
                {prefecture}
              </SelectItem>
            ))}
        </SelectContent>
      </Select>

      {/* Sélection de la commune */}
      <Select
        value={selectedCommune}
        onValueChange={handleCommuneChange}
        disabled={!selectedPrefecture}
      >
        <SelectTrigger>
          <SelectValue placeholder="Sélectionnez une commune" />
        </SelectTrigger>
        <SelectContent>
          {communesData?.success &&
            communesData.data?.communes?.map((commune: string) => (
              <SelectItem key={commune} value={commune}>
                {commune}
              </SelectItem>
            ))}
        </SelectContent>
      </Select>

      {/* Sélection de la sous-préfecture */}
      <Select
        value={selectedSousPrefecture}
        onValueChange={handleSousPrefectureChange}
        disabled={!selectedCommune}
      >
        <SelectTrigger>
          <SelectValue placeholder="Sélectionnez une sous-préfecture" />
        </SelectTrigger>
        <SelectContent>
          {locationData?.success &&
            locationData.data?.sousPrefectures?.map(
              (sousPrefecture: string) => (
                <SelectItem key={sousPrefecture} value={sousPrefecture}>
                  {sousPrefecture}
                </SelectItem>
              )
            )}
        </SelectContent>
      </Select>

      {/* Sélection du quartier */}
      <Select
        value={selectedQuartier}
        onValueChange={setSelectedQuartier}
        disabled={!selectedCommune}
      >
        <SelectTrigger>
          <SelectValue placeholder="Sélectionnez un quartier" />
        </SelectTrigger>
        <SelectContent>
          {locationData?.success &&
            locationData.data?.quartiers?.map((quartier: string) => (
              <SelectItem key={quartier} value={quartier}>
                {quartier}
              </SelectItem>
            ))}
        </SelectContent>
      </Select>
    </div>
  );
}
