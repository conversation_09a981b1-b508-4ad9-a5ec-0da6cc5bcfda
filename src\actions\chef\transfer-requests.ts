"use server";

import { getCurrentUser } from "@/actions/auth/session";
import { createAdminClient } from "@/lib/server/appwrite";
import { CITIZENS_COLLECTION_ID, DATABASE_ID } from "@/lib/server/database";

/**
 * Server Action pour obtenir les demandes de transfert pour un chef
 */
export async function getTransferRequestsForChefAction() {
  try {
    const { user } = await getCurrentUser();

    // Vérifier que l'utilisateur est chef
    if (!user || user.prefs?.role !== "chef") {
      return {
        success: false,
        error: "Accès non autorisé",
        requests: [],
      };
    }

    const { databases } = await createAdminClient();

    // Récupérer les demandes de transfert en attente
    // Pour l'instant, récupérer tous les citoyens et filtrer côté serveur
    // car nous devons vérifier les champs transferStatus qui peuvent être null
    const { Query } = await import("node-appwrite");

    let citizens;
    try {
      citizens = await databases.listDocuments(
        DATABASE_ID,
        CITIZENS_COLLECTION_ID,
        [Query.equal("transferStatus", "pending")]
      );
    } catch (queryError) {
      // Si la requête échoue (champ peut-être null), récupérer tous et filtrer
      console.warn(
        "Requête avec filtre échouée, récupération de tous les citoyens:",
        queryError
      );
      const allCitizens = await databases.listDocuments(
        DATABASE_ID,
        CITIZENS_COLLECTION_ID,
        []
      );

      // Filtrer côté serveur
      citizens = {
        ...allCitizens,
        documents: allCitizens.documents.filter(
          (doc) => doc.transferStatus === "pending"
        ),
      };
    }

    const requests = citizens.documents.map((doc) => {
      // Parser les champs JSON si nécessaire
      let transferRequest = null;
      let transferApprovals = {};

      try {
        if (doc.transferRequest && typeof doc.transferRequest === "string") {
          transferRequest = JSON.parse(doc.transferRequest);
        } else if (
          doc.transferRequest &&
          typeof doc.transferRequest === "object"
        ) {
          transferRequest = doc.transferRequest;
        }
      } catch (error) {
        console.warn("Erreur lors du parsing de transferRequest:", error);
      }

      try {
        if (
          doc.transferApprovals &&
          typeof doc.transferApprovals === "string"
        ) {
          transferApprovals = JSON.parse(doc.transferApprovals);
        } else if (
          doc.transferApprovals &&
          typeof doc.transferApprovals === "object"
        ) {
          transferApprovals = doc.transferApprovals;
        }
      } catch (error) {
        console.warn("Erreur lors du parsing de transferApprovals:", error);
      }

      return {
        $id: doc.$id,
        $collectionId: doc.$collectionId,
        $databaseId: doc.$databaseId,
        $createdAt: doc.$createdAt,
        $updatedAt: doc.$updatedAt,
        $permissions: doc.$permissions,
        transferRequest,
        transferStatus: doc.transferStatus,
        transferApprovals,
        nom: doc.nom,
        prenom: doc.prenom,
        email: doc.email,
        telephone: doc.telephone,
        quartier: doc.quartier,
      };
    });

    return {
      success: true,
      requests,
      error: null,
    };
  } catch (error: any) {
    console.error(
      "Erreur lors de la récupération des demandes de transfert:",
      error
    );
    return {
      success: false,
      error: error.message || "Erreur lors de la récupération des demandes",
      requests: [],
    };
  }
}

/**
 * Server Action pour approuver une demande de transfert
 */
export async function approveQuartierTransferAction(params: {
  citizenId: string;
  approval: "current_chef" | "new_chef";
  notes?: string;
}) {
  try {
    const { user } = await getCurrentUser();

    // Vérifier que l'utilisateur est chef
    if (!user || user.prefs?.role !== "chef") {
      return {
        success: false,
        error: "Accès non autorisé",
        message: "",
      };
    }

    const { databases } = await createAdminClient();

    // Récupérer le citoyen
    const citizen = await databases.getDocument(
      DATABASE_ID,
      CITIZENS_COLLECTION_ID,
      params.citizenId
    );

    if (!citizen) {
      return {
        success: false,
        error: "Citoyen non trouvé",
        message: "",
      };
    }

    // Mettre à jour les approbations
    let currentApprovals = {};
    try {
      // Parser les approbations existantes si elles existent
      if (
        citizen.transferApprovals &&
        typeof citizen.transferApprovals === "string"
      ) {
        currentApprovals = JSON.parse(citizen.transferApprovals);
      } else if (
        citizen.transferApprovals &&
        typeof citizen.transferApprovals === "object"
      ) {
        currentApprovals = citizen.transferApprovals;
      }
    } catch (error) {
      console.warn(
        "Erreur lors du parsing des approbations existantes:",
        error
      );
      currentApprovals = {};
    }

    const updatedApprovals = {
      ...currentApprovals,
      [params.approval]: {
        approved: true,
        approvedBy: user.$id,
        approvedAt: new Date().toISOString(),
        notes: params.notes || "",
      },
    };

    // Vérifier si toutes les approbations sont obtenues
    const hasCurrentChefApproval = (updatedApprovals as any).current_chef
      ?.approved;
    const hasNewChefApproval = (updatedApprovals as any).new_chef?.approved;

    let newStatus = "pending";
    if (hasCurrentChefApproval && hasNewChefApproval) {
      newStatus = "approved";
    } else if (hasCurrentChefApproval || hasNewChefApproval) {
      newStatus = "partially_approved";
    }

    // Mettre à jour le document avec JSON stringifié
    await databases.updateDocument(
      DATABASE_ID,
      CITIZENS_COLLECTION_ID,
      params.citizenId,
      {
        transferApprovals: JSON.stringify(updatedApprovals),
        transferStatus: newStatus,
        updatedAt: new Date().toISOString(),
      }
    );

    return {
      success: true,
      message:
        newStatus === "approved"
          ? "Demande de transfert approuvée et finalisée"
          : "Approbation enregistrée, en attente de l'autre chef",
      error: null,
    };
  } catch (error: any) {
    console.error("Erreur lors de l'approbation du transfert:", error);
    return {
      success: false,
      error: error.message || "Erreur lors de l'approbation",
      message: "",
    };
  }
}

/**
 * Server Action pour rejeter une demande de transfert
 */
export async function rejectQuartierTransferAction(params: {
  citizenId: string;
  reason: string;
}) {
  try {
    const { user } = await getCurrentUser();

    // Vérifier que l'utilisateur est chef
    if (!user || user.prefs?.role !== "chef") {
      return {
        success: false,
        error: "Accès non autorisé",
        message: "",
      };
    }

    const { databases } = await createAdminClient();

    // Mettre à jour le statut de transfert
    await databases.updateDocument(
      DATABASE_ID,
      CITIZENS_COLLECTION_ID,
      params.citizenId,
      {
        transferStatus: "rejected",
        transferRejection: {
          rejectedBy: user.$id,
          rejectedAt: new Date().toISOString(),
          reason: params.reason,
        },
        updatedAt: new Date().toISOString(),
      }
    );

    return {
      success: true,
      message: "Demande de transfert rejetée",
      error: null,
    };
  } catch (error: any) {
    console.error("Erreur lors du rejet du transfert:", error);
    return {
      success: false,
      error: error.message || "Erreur lors du rejet",
      message: "",
    };
  }
}
