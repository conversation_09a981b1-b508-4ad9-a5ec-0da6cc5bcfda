"use client";

import { motion } from "framer-motion";
import { Users, FileText, Building2, AlertCircle } from "lucide-react";

export function AdminDashboardStats() {
  const stats = [
    {
      title: "Utilisateurs",
      value: "1,234",
      change: "+12%",
      icon: Users,
      color: "bg-blue-500",
    },
    {
      title: "Certificats",
      value: "5,678",
      change: "+23%",
      icon: FileText,
      color: "bg-green-500",
    },
    {
      title: "Quartiers",
      value: "45",
      change: "+2",
      icon: Building2,
      color: "bg-purple-500",
    },
    {
      title: "En attente",
      value: "89",
      change: "-5%",
      icon: AlertCircle,
      color: "bg-yellow-500",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className={`p-3 rounded-lg ${stat.color}`}>
              <stat.icon className="w-6 h-6 text-white" />
            </div>
            <span className={`text-sm font-medium ${
              stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
            }`}>
              {stat.change}
            </span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{stat.value}</h3>
          <p className="text-sm text-gray-600 mt-1">{stat.title}</p>
        </motion.div>
      ))}
    </div>
  );
}