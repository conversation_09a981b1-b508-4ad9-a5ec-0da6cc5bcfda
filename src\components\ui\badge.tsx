"use client";

import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils/cn";

const badgeVariants = cva(
  "inline-flex items-center gap-1.5 rounded-full border px-3 py-1 text-xs font-medium transition-all duration-200 shadow-sm backdrop-blur-sm",
  {
    variants: {
      variant: {
        default:
          "border-neutral-200/50 bg-neutral-50/90 text-neutral-700 hover:bg-neutral-100/90",
        primary:
          "border-blue-200/50 bg-blue-50/90 text-blue-700 hover:bg-blue-100/90",
        secondary:
          "border-neutral-200/50 bg-white/80 text-neutral-600 hover:bg-neutral-50/90",
        info:
          "border-sky-200/50 bg-sky-50/90 text-sky-700 hover:bg-sky-100/90",
        success:
          "border-emerald-200/50 bg-emerald-50/90 text-emerald-700 hover:bg-emerald-100/90",
        warning:
          "border-amber-200/50 bg-amber-50/90 text-amber-700 hover:bg-amber-100/90",
        error:
          "border-red-200/50 bg-red-50/90 text-red-700 hover:bg-red-100/90",
        outline:
          "border-neutral-200 bg-white/50 text-neutral-700 hover:bg-neutral-50/90",
        glass:
          "border-white/20 bg-white/10 text-neutral-700 backdrop-blur-md hover:bg-white/20",
      },
      size: {
        sm: "px-2.5 py-0.5 text-xs",
        md: "px-3 py-1 text-sm",
        lg: "px-4 py-1.5 text-base",
      },
      withRing: {
        true: "focus:ring-2 focus:ring-offset-2",
        false: "",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "sm",
      withRing: false,
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  icon?: React.ReactNode;
}

function Badge({ className, variant, size, withRing, icon, children, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant, size, withRing }), className)} {...props}>
      {icon && <span className="shrink-0">{icon}</span>}
      {children}
    </div>
  );
}

export { Badge, badgeVariants };
