"use client";

import { motion } from "framer-motion";
import { HomeIcon, RefreshCcw } from "lucide-react";
import { useEffect } from "react";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="fixed inset-0 z-50 flex min-h-screen items-center justify-center bg-neutral-950/30 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative mx-auto flex w-full max-w-xl flex-col items-center gap-6 rounded-lg border border-red-500/10 bg-neutral-950/50 p-8 shadow-2xl"
      >
        <div className="absolute inset-0 -z-10 bg-gradient-to-b from-red-500/5 to-transparent" />

        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="flex h-20 w-20 items-center justify-center rounded-full bg-red-500/10"
        >
          <div className="text-4xl">⚠️</div>
        </motion.div>

        <div className="text-center">
          <h2 className="mb-2 text-2xl font-bold text-red-500">
            Une erreur est survenue
          </h2>
          <p className="mb-4 text-neutral-300">
            {error.message || "Une erreur inattendue s'est produite"}
          </p>
          <p className="text-sm text-neutral-400">Code: {error.digest}</p>
        </div>

        <div className="flex gap-4">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={reset}
            className="inline-flex items-center gap-2 rounded-full bg-red-500 px-6 py-2 text-sm font-medium text-white transition-colors hover:bg-red-600"
          >
            <RefreshCcw className="h-4 w-4" />
            Réessayer
          </motion.button>

          <motion.a
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            href="/"
            className="inline-flex items-center gap-2 rounded-full bg-neutral-800 px-6 py-2 text-sm font-medium text-white transition-colors hover:bg-neutral-700"
          >
            <HomeIcon className="h-4 w-4" />
            Accueil
          </motion.a>
        </div>
      </motion.div>
    </div>
  );
}
