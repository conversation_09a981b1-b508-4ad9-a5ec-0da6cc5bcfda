"use client";

import { Badge } from "@/components/ui/badge";
import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import {
  CERTIFICATE_STATUS_COLORS,
  CERTIFICATE_STATUS_LABELS,
} from "@/constants/status-labels";
import {
  <PERSON><PERSON><PERSON>,
  CheckCircle2,
  <PERSON>,
  FileCheck,
  FileEdit,
  PenLine,
  FileX2,
  TimerOff,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";

const getStatusIcon = (status: CERTIFICATE_STATUS) => {
  switch (status) {
    case CERTIFICATE_STATUS.DRAFT:
      return FileEdit;
    case CERTIFICATE_STATUS.SUBMITTED:
      return PenLine;
    case CERTIFICATE_STATUS.PENDING:
      return Clock;
    case CERTIFICATE_STATUS.VERIFIED:
      return CheckCircle2;
    case CERTIFICATE_STATUS.APPROVED:
      return FileCheck;
    case CERTIFICATE_STATUS.READY:
      return CheckCircle2;
    case CERTIFICATE_STATUS.SIGNED:
      return FileCheck;
    case CERTIFICATE_STATUS.DELIVERED:
      return FileCheck;
    case CERTIFICATE_STATUS.REJECTED:
      return FileX2;
    case CERTIFICATE_STATUS.EXPIRED:
      return TimerOff;
    default:
      return Clock;
  }
};

interface StatusBadgeProps {
  status: CERTIFICATE_STATUS;
  timestamp?: string;
  size?: "sm" | "md" | "lg";
}

export function StatusBadge({
  status,
  timestamp,
  size = "sm",
}: StatusBadgeProps) {
  const Icon = getStatusIcon(status);

  return (
    <div className="flex items-center gap-2">
      <Badge
        variant={CERTIFICATE_STATUS_COLORS[status] || "default"}
        size={size}
        icon={<Icon className="h-3.5 w-3.5" />}
      >
        {CERTIFICATE_STATUS_LABELS[status] || status}
      </Badge>
      {timestamp && (
        <div className="flex items-center gap-1 text-xs text-neutral-500">
          <CalendarClock className="h-3 w-3" />
          {formatDistanceToNow(new Date(timestamp), {
            addSuffix: true,
            locale: fr,
          })}
        </div>
      )}
    </div>
  );
}
