"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowLef<PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import Link from "next/link";

export function NotAuthorized() {
  const router = useRouter();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5">
      {/* Fond décoratif */}
      <div className="absolute inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(0,77,64,0.08),transparent_50%)]" />
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-[0.02]"
        />
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md mx-auto p-6"
      >
        <div className="relative overflow-hidden bg-white rounded-2xl shadow-xl border border-neutral-200/60 p-8">
          {/* Cercle décoratif */}
          <div className="absolute -top-12 -right-12 w-32 h-32 bg-red-500/10 rounded-full blur-2xl" />
          <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-accent-primary/10 rounded-full blur-2xl" />

          {/* Contenu */}
          <div className="relative space-y-6">
            {/* Icône */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 20,
                delay: 0.2
              }}
              className="mx-auto w-16 h-16 rounded-xl bg-gradient-to-br from-red-500/10 to-red-500/30
                flex items-center justify-center transform hover:scale-105 transition-transform duration-300"
            >
              <ShieldAlert className="h-8 w-8 text-red-600" />
            </motion.div>

            {/* Texte */}
            <div className="text-center space-y-2">
              <motion.h2
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-2xl font-semibold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent"
              >
                Accès non autorisé
              </motion.h2>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="text-neutral-600"
              >
                Vous devez être connecté pour accéder à cette page
              </motion.p>
            </div>

            {/* Actions */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="space-y-3"
            >
              <Link 
                href="/connexion"
                className="block w-full"
              >
                <Button
                  className="w-full bg-gradient-to-r from-accent-primary to-accent-secondary hover:from-accent-primary/90 
                    hover:to-accent-secondary/90 text-white font-medium shadow-lg shadow-accent-primary/10 
                    hover:shadow-xl hover:shadow-accent-primary/20 transform hover:-translate-y-0.5 transition-all duration-300"
                >
                  Se connecter
                </Button>
              </Link>

              <Button
                variant="outline"
                className="w-full border-neutral-200 hover:border-neutral-300 text-neutral-700
                  hover:bg-neutral-50 transform hover:-translate-y-0.5 transition-all duration-300"
                onClick={() => router.back()}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Retour
              </Button>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}