import { VerificationResult } from "@/actions/certificate-verification";
import {
  AlertTriangle,
  Award,
  Calendar,
  CheckCircle,
  Clock,
  FileText,
  Globe,
  MapPin,
  QrCode,
  Shield,
  User,
  XCircle,
} from "lucide-react";

interface VerificationDisplaySSRProps {
  result: VerificationResult;
  hash: string;
  debugInfo?: {
    hash: string;
    certificateId?: string;
    timestamp?: string;
    verificationTime: string;
  };
}

export function VerificationDisplaySSR({
  result,
  hash,
  debugInfo,
}: VerificationDisplaySSRProps) {
  const getStatusIcon = () => {
    switch (result.status) {
      case "valid":
        return <CheckCircle className="w-20 h-20 text-green-500" />;
      case "expired":
        return <Clock className="w-20 h-20 text-orange-500" />;
      case "revoked":
        return <XCircle className="w-20 h-20 text-red-500" />;
      default:
        return <AlertTriangle className="w-20 h-20 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (result.status) {
      case "valid":
        return "from-green-50 to-green-100 border-green-200";
      case "expired":
        return "from-orange-50 to-orange-100 border-orange-200";
      case "revoked":
        return "from-red-50 to-red-100 border-red-200";
      default:
        return "from-gray-50 to-gray-100 border-gray-200";
    }
  };

  const getStatusText = () => {
    switch (result.status) {
      case "valid":
        return "Certificat Valide";
      case "expired":
        return "Certificat Expiré";
      case "revoked":
        return "Certificat Révoqué";
      case "not_found":
        return "Certificat Non Trouvé";
      default:
        return "Statut Inconnu";
    }
  };

  const getExpirationBadge = () => {
    if (!result.documentStatus) return null;

    switch (result.documentStatus.expirationStatus) {
      case "expiring_soon":
        return (
          <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-4 h-4 mr-1" />
            Expire dans {result.documentStatus.daysUntilExpiration} jours
          </div>
        );
      case "expired":
        return (
          <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
            <XCircle className="w-4 h-4 mr-1" />
            Expiré
          </div>
        );
      default:
        return (
          <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
            <CheckCircle className="w-4 h-4 mr-1" />
            Valide
          </div>
        );
    }
  };

  return (
    <div className="space-y-8">
      {/* Main Verification Result */}
      <div
        className={`bg-gradient-to-br ${getStatusColor()} border-2 rounded-2xl p-8 text-center shadow-lg`}
      >
        <div className="flex justify-center mb-6">{getStatusIcon()}</div>

        <h2 className="text-3xl font-bold mb-4 text-gray-800">
          {getStatusText()}
        </h2>

        <p className="text-lg text-gray-700 mb-6">{result.message}</p>

        <div className="flex justify-center mb-6">{getExpirationBadge()}</div>

        {/* Verification Hash */}
        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 mb-6">
          <p className="text-sm text-gray-600 mb-2">Hash de vérification:</p>
          <p className="font-mono text-sm break-all text-gray-800 bg-gray-100 px-3 py-2 rounded-lg">
            {hash}
          </p>
        </div>

        {/* Security Indicator */}
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
          <Shield className="w-5 h-5 text-green-600" />
          <span>Vérification cryptographique sécurisée</span>
        </div>
      </div>

      {/* Citizen Information */}
      {result.citizenInfo && (
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
          <div className="bg-gradient-to-r from-green-600 to-yellow-500 px-6 py-4">
            <h3 className="text-xl font-bold text-white flex items-center">
              <User className="w-6 h-6 mr-2" />
              Informations du Titulaire
            </h3>
          </div>

          <div className="p-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <User className="w-5 h-5 text-gray-500 mt-1" />
                  <div>
                    <p className="text-sm text-gray-600">Nom complet</p>
                    <p className="font-semibold text-lg">
                      {result.citizenInfo.fullName}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Award className="w-5 h-5 text-gray-500 mt-1" />
                  <div>
                    <p className="text-sm text-gray-600">Profession</p>
                    <p className="font-semibold">
                      {result.citizenInfo.profession}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Globe className="w-5 h-5 text-gray-500 mt-1" />
                  <div>
                    <p className="text-sm text-gray-600">Nationalité</p>
                    <p className="font-semibold">
                      {result.citizenInfo.nationality}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <MapPin className="w-5 h-5 text-gray-500 mt-1" />
                  <div>
                    <p className="text-sm text-gray-600">Lieu de résidence</p>
                    <p className="font-semibold">
                      {result.citizenInfo.residenceLocation}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <FileText className="w-5 h-5 text-gray-500 mt-1" />
                  <div>
                    <p className="text-sm text-gray-600">Motif de la demande</p>
                    <p className="font-semibold">
                      {result.citizenInfo.purpose}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Calendar className="w-5 h-5 text-gray-500 mt-1" />
                  <div>
                    <p className="text-sm text-gray-600">Période de validité</p>
                    <p className="font-semibold">
                      {result.citizenInfo.validityPeriod}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Certificate Information */}
      {result.certificateInfo && (
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
          <div className="bg-gradient-to-r from-red-600 via-yellow-500 to-green-600 px-6 py-4">
            <h3 className="text-xl font-bold text-white flex items-center">
              <FileText className="w-6 h-6 mr-2" />
              Détails du Certificat
            </h3>
          </div>

          <div className="p-6">
            <div className="grid md:grid-cols-3 gap-6">
              <div>
                <p className="text-sm text-gray-600 mb-1">Référence</p>
                <p className="font-semibold text-lg">
                  {result.certificateInfo.reference}
                </p>
              </div>

              <div>
                <p className="text-sm text-gray-600 mb-1">Date d'émission</p>
                <p className="font-semibold">
                  {new Date(result.certificateInfo.issuedAt).toLocaleDateString(
                    "fr-FR",
                    {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    }
                  )}
                </p>
              </div>

              <div>
                <p className="text-sm text-gray-600 mb-1">Date d'expiration</p>
                <p className="font-semibold">
                  {new Date(
                    result.certificateInfo.expiresAt
                  ).toLocaleDateString("fr-FR", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </p>
              </div>

              <div>
                <p className="text-sm text-gray-600 mb-1">Autorité émettrice</p>
                <p className="font-semibold">
                  {result.certificateInfo.issuerName}
                </p>
              </div>

              <div>
                <p className="text-sm text-gray-600 mb-1">Localisation</p>
                <p className="font-semibold">
                  {result.certificateInfo.quartier},{" "}
                  {result.certificateInfo.commune}
                </p>
              </div>

              <div>
                <p className="text-sm text-gray-600 mb-1">Région</p>
                <p className="font-semibold">{result.certificateInfo.region}</p>
              </div>
            </div>

            {/* Verification Statistics */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Nombre de vérifications:</span>
                <span className="font-semibold text-lg">
                  {result.certificateInfo.verificationCount}
                </span>
              </div>
              {result.certificateInfo.lastVerifiedAt && (
                <div className="flex items-center justify-between text-sm mt-2">
                  <span className="text-gray-600">Dernière vérification:</span>
                  <span className="font-semibold">
                    {new Date(
                      result.certificateInfo.lastVerifiedAt
                    ).toLocaleDateString("fr-FR")}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Security Information */}
      {result.securityInfo && (
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
          <div className="bg-gradient-to-r from-red-600 to-pink-600 px-6 py-4">
            <h3 className="text-xl font-bold text-white flex items-center">
              <Shield className="w-6 h-6 mr-2" />
              Informations de Sécurité
            </h3>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <span className="text-gray-700">Intégrité du hash:</span>
                <span
                  className={`font-semibold ${
                    result.securityInfo.isIntegrityValid
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {result.securityInfo.isIntegrityValid ? "✓ Valide" : "✗ Invalide"}
                </span>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <span className="text-gray-700">Horodatage:</span>
                <span
                  className={`font-semibold ${
                    result.securityInfo.isTimestampValid
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {result.securityInfo.isTimestampValid ? "✓ Valide" : "✗ Invalide"}
                </span>
              </div>

              {result.securityInfo.errors.length > 0 && (
                <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                  <h4 className="font-semibold text-red-800 mb-2">
                    Erreurs de sécurité détectées:
                  </h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    {result.securityInfo.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
