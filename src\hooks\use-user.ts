"use client";

import { useQuery } from "@tanstack/react-query";
import { getCurrentUser } from "@/actions/auth/session";
import { getUserDetails } from "@/actions/auth/user-details";
import type { Models } from "node-appwrite";
import { STATUS } from "@/actions/auth/constants";

// Interface pour les détails étendus de l'utilisateur
export interface ExtendedUserDetails {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  name: string;
  email: string;
  phone?: string;
  emailVerification: boolean;
  phoneVerification: boolean;
  status: boolean;
  prefs?: Models.Preferences;
  // Champs personnalisés
  quartier?: {
    id: string;
    name: string;
  };
  userStatus?: STATUS;
  role?: string;
  phoneNumber?: string;
}

export function useUser() {
  const {
    data: sessionData,
    isLoading: isSessionLoading,
  } = useQuery({
    queryKey: ["session"],
    queryFn: getCurrentUser,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const {
    data: userDetails,
    isLoading: isDetailsLoading,
    error,
  } = useQuery({
    queryKey: ["userDetails", sessionData?.user?.$id],
    queryFn: () => getUserDetails(sessionData?.user?.$id),
    enabled: !!sessionData?.user,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  return {
    user: userDetails as ExtendedUserDetails | undefined,
    isLoading: isSessionLoading || isDetailsLoading,
    error,
  };
}