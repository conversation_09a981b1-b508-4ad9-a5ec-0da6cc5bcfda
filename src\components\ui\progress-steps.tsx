"use client";

import { motion } from "framer-motion";
import { CheckCircle2, Circle } from "lucide-react";
import { cn } from "@/lib/utils/cn";

interface ProgressStepsProps {
  steps: readonly string[];
  currentStep: number;
  className?: string;
}

export function ProgressSteps({
  steps,
  currentStep,
  className,
}: ProgressStepsProps) {
  return (
    <div className={cn("w-full", className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const isCompleted = index < currentStep;
          const isCurrent = index === currentStep;

          return (
            <div key={step} className="flex flex-1 items-center">
              <div className="relative flex flex-col items-center">
                <motion.div
                  initial={false}
                  animate={{
                    backgroundColor:
                      isCompleted || isCurrent ? "#059669" : "#e5e5e5",
                    scale: isCurrent ? 1.2 : 1,
                  }}
                  className={cn(
                    "flex h-10 w-10 items-center justify-center rounded-full",
                    "border-4 border-white dark:border-neutral-950",
                    "text-white shadow-md"
                  )}
                >
                  {isCompleted ? (
                    <CheckCircle2 className="h-6 w-6" />
                  ) : (
                    <Circle className="h-6 w-6" />
                  )}
                </motion.div>
                <span className="absolute -bottom-6 w-max text-center text-sm font-medium">
                  {step}
                </span>
              </div>
              {index < steps.length - 1 && (
                <motion.div
                  initial={false}
                  animate={{
                    backgroundColor: isCompleted ? "#059669" : "#e5e5e5",
                  }}
                  className="h-[2px] flex-1"
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
