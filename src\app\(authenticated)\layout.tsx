import { getCurrentUser } from "@/actions/auth/session";
import { DashboardShell } from "@/components/dashboard/shell";
import { APP_URL } from "@/config/env";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export const dynamic = "force-dynamic";

export default async function AuthenticatedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { success, user } = await getCurrentUser();

  if (!success || !user) {
    const headersList = await headers();
    const currentPath = new URL(
      headersList.get("x-url") || "",
      APP_URL
    ).pathname;
    const searchParams = new URLSearchParams({
      callbackUrl: currentPath,
    });

    redirect(`/connexion?${searchParams.toString()}`);
  }

  return <DashboardShell>{children}</DashboardShell>;
}
