"use client";

import { getCertificateById } from "@/actions/citizen/certificates";
import { CertificateDetails } from "@/components/certificates/certificate-details";
import { CertificateDocuments } from "@/components/certificates/certificate-documents";
import { CertificateTimeline } from "@/components/certificates/certificate-timeline";
import { DashboardHeader } from "@/components/dashboard/header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useCertificateActions } from "@/hooks/use-certificate-actions";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft, FileCheck, Ban, Loader2 } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { SignButton } from "@/components/certificates/sign-button";
import { SignaturePadComponent } from "@/components/certificates/signature-pad";
import { storeSignature } from "@/actions/chef/certificates";

export default function SignCertificatePage() {
  const params = useParams();
  const router = useRouter();
  const { user, isLoading: isLoadingUser } = useAuth();
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);
  const { sign, reject } = useCertificateActions();

  useEffect(() => {
    if (!isLoadingUser && !user) {
      router.push("/dashboard");
    } else if (
      !isLoadingUser &&
      user &&
      user.prefs?.role !== "chef" &&
      user.prefs?.role !== "admin"
    ) {
      router.push("/dashboard");
    }
  }, [user, router, isLoadingUser]);

  const { data: certificateData, isLoading } = useQuery({
    queryKey: ["certificate", params.id],
    queryFn: async () => {
      const response = await getCertificateById(params.id as string);
      return response.certificate;
    },
    enabled:
      !isLoadingUser &&
      !!user &&
      (user.prefs?.role === "chef" || user.prefs?.role === "admin"),
  });

  useEffect(() => {
    if (
      certificateData &&
      certificateData.status !== CERTIFICATE_STATUS.READY
    ) {
      router.push("/dashboard/certificates/sign");
    }
  }, [certificateData, router]);

  if (isLoadingUser || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-accent-primary" />
      </div>
    );
  }

  if (!user || (user.prefs?.role !== "chef" && user.prefs?.role !== "admin")) {
    return null;
  }

  if (!certificateData) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-2xl font-bold">Certificat non trouvé</h1>
        <p className="text-neutral-500 mt-2">
          Le certificat que vous recherchez n&apos;existe pas ou a été supprimé.
        </p>
      </div>
    );
  }

  const handleSign = async (signatureData: string) => {
    try {
      setIsProcessing(true);

      // Stocker la signature via l'action serveur
      const { signatureFileId } = await storeSignature(
        certificateData.$id,
        signatureData
      );

      // Signer le certificat avec la référence de la signature
      await sign({
        certificateId: certificateData.$id,
        signatureFileId,
      });

      toast({
        title: "Certificat signé",
        description: "Le certificat a été signé avec succès.",
      });
      router.push("/dashboard/certificates/sign");
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la signature.",
        variant: "error",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async () => {
    try {
      setIsProcessing(true);
      await reject({
        certificateId: certificateData.$id,
        reason: "Rejeté lors de la signature",
      });
      toast({
        title: "Certificat rejeté",
        description: "Le certificat a été rejeté avec succès.",
      });
      router.push("/dashboard/certificates/sign");
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors du rejet.",
        variant: "error",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const canReject =
    certificateData.status !== CERTIFICATE_STATUS.READY &&
    certificateData.status !== CERTIFICATE_STATUS.SIGNED &&
    certificateData.status !== CERTIFICATE_STATUS.DELIVERED;

  const handleSignButtonClick = () => {
    const signatureSection = document.querySelector("#signature-section");
    signatureSection?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <div className="space-y-8 p-8">
      <DashboardHeader
        heading={`Signature du certificat ${certificateData.reference}`}
        text="Vérification et signature du certificat"
      >
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.push("/dashboard/certificates/sign")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour à la liste
          </Button>
          {canReject && (
            <Button
              variant="destructive"
              onClick={handleReject}
              disabled={isProcessing}
            >
              <Ban className="h-4 w-4 mr-2" />
              Rejeter
            </Button>
          )}
          <SignButton
            certificateId={certificateData.$id}
            status={certificateData.status}
            isProcessing={isProcessing}
            size="default"
            onClick={handleSignButtonClick}
          />
        </div>
      </DashboardHeader>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Informations principales */}
        <div className="md:col-span-2 space-y-6">
          <CertificateDetails
            certificate={certificateData}
            userRole={user.prefs?.role as "admin" | "chef"}
          />
          <CertificateDocuments
            certificate={certificateData}
            userRole={user.prefs?.role as "admin" | "chef"}
            user={{
              $id: user.$id,
              name: user.name,
            }}
          />
          <SignaturePadComponent
            id="signature-section"
            onSave={handleSign}
            title="Signature du chef de quartier"
            description="Veuillez apposer votre signature pour valider ce certificat"
          />
        </div>

        {/* Timeline et statut */}
        <div className="space-y-6">
          <CertificateTimeline certificate={certificateData} />
          <Card>
            <CardHeader>
              <CardTitle>Vérification finale</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2 text-sm text-neutral-600">
                <FileCheck className="h-4 w-4 text-green-500" />
                <span>Documents vérifiés et approuvés</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-neutral-600">
                <FileCheck className="h-4 w-4 text-green-500" />
                <span>Informations validées</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-neutral-600">
                <FileCheck className="h-4 w-4 text-green-500" />
                <span>Prêt pour signature</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
