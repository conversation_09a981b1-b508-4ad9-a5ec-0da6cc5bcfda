"use server";

import {
  APPWRITE_API_KEY,
  APPWRITE_ENDPOINT,
  APPWRITE_PROJECT_ID,
} from "@/config/env";
import {
  Account,
  Client,
  Databases,
  Storage,
  Teams,
  Users,
} from "node-appwrite";

// Validation des variables d'environnement Appwrite
if (!APPWRITE_ENDPOINT || !APPWRITE_PROJECT_ID || !APPWRITE_API_KEY) {
  throw new Error(
    "Les variables d'environnement APPWRITE_ENDPOINT, APPWRITE_PROJECT_ID et APPWRITE_API_KEY sont requises"
  );
}

/**
 * Crée un client Appwrite pour une session utilisateur
 * @param sessionId - ID de session de l'utilisateur
 * @returns Client Appwrite configuré pour la session
 */
export async function createSessionClient(sessionId: string) {
  const client = new Client()
    .setEndpoint(APPWRITE_ENDPOINT)
    .setProject(APPWRITE_PROJECT_ID);

  if (sessionId) {
    client.setSession(sessionId);
  }

  return {
    get account() {
      return new Account(client);
    },
    get databases() {
      return new Databases(client);
    },
    get storage() {
      return new Storage(client);
    },
    get teams() {
      return new Teams(client);
    },
  };
}

/**
 * Crée un client Appwrite avec les privilèges administrateur
 * @returns Client Appwrite configuré avec les privilèges admin
 */
export async function createAdminClient() {
  const client = new Client()
    .setEndpoint(APPWRITE_ENDPOINT)
    .setProject(APPWRITE_PROJECT_ID)
    .setKey(APPWRITE_API_KEY);

  return {
    get account() {
      return new Account(client);
    },
    get databases() {
      return new Databases(client);
    },
    get storage() {
      return new Storage(client);
    },
    get teams() {
      return new Teams(client);
    },
    get users() {
      return new Users(client);
    },
  };
}
