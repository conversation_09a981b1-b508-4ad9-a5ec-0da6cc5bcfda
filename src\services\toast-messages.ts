export const TOAST_MESSAGES = {
  certificate: {
    assign: {
      success: {
        title: "Agent assigné avec succès",
        description: "L'agent a été assigné au traitement du certificat.",
      },
      error: {
        title: "Erreur lors de l'assignation",
        description:
          "Une erreur est survenue lors de l'assignation de l'agent.",
      },
    },
    unassign: {
      success: {
        title: "Agent désassigné avec succès",
        description: "L'agent a été retiré du traitement du certificat.",
      },
      error: {
        title: "Erreur lors de la désassignation",
        description: "Une erreur est survenue lors du retrait de l'agent.",
      },
    },
    reject: {
      success: {
        title: "Demande rejetée",
        description: "La demande de certificat a été rejetée.",
      },
      error: {
        title: "Erreur lors du rejet",
        description: "Une erreur est survenue lors du rejet de la demande.",
      },
    },
    verify: {
      success: {
        title: "Vérification effectuée",
        description: "Le certificat a été vérifié avec succès.",
      },
      error: {
        title: "Erreur lors de la vérification",
        description: "Une erreur est survenue lors de la vérification.",
      },
    },
    sign: {
      success: {
        title: "Certificat signé",
        description: "Le certificat a été signé avec succès.",
      },
      error: {
        title: "Erreur lors de la signature",
        description:
          "Une erreur est survenue lors de la signature du certificat.",
      },
    },
    deliver: {
      success: {
        title: "Certificat délivré",
        description: "Le certificat a été délivré avec succès.",
      },
      error: {
        title: "Erreur lors de la délivrance",
        description:
          "Une erreur est survenue lors de la délivrance du certificat.",
      },
    },
    download: {
      success: {
        title: "Téléchargement réussi",
        description: "Le certificat a été téléchargé avec succès.",
      },
      error: {
        title: "Erreur de téléchargement",
        description: "Une erreur est survenue lors du téléchargement.",
      },
    },
    markAsReady: {
      success: {
        title: "Certificat prêt",
        description: "Le certificat a été marqué comme prêt pour signature.",
      },
      error: {
        title: "Erreur",
        description: "Une erreur est survenue lors du marquage du certificat.",
      },
    },
  },
} as const;
