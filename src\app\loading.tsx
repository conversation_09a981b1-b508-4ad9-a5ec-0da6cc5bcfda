"use client";

import { motion } from "framer-motion";

export default function Loading() {
  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-[#004D40] via-[#00695C] to-[#00796B] flex items-center justify-center">
      {/* Motif de fond */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-10" />
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-black/5 to-black/20" />
      </div>

      {/* Cercles décoratifs animés */}
      <motion.div
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.2, 0.3],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -left-32 -top-32 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-3xl opacity-30"
      />
      <motion.div
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.2, 0.3, 0.2],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -right-32 top-1/4 w-96 h-96 bg-red-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20"
      />

      {/* Contenu principal */}
      <div className="relative z-10 text-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          {/* Logo animé */}
          <div className="relative w-24 h-24 mx-auto">
            <motion.div
              animate={{
                rotate: 360,
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "linear",
              }}
              className="absolute inset-0 rounded-full border-4 border-t-white/80 border-r-white/40 border-b-white/20 border-l-white/60"
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="absolute inset-4 bg-white/10 backdrop-blur-sm rounded-2xl p-2"
            >
              <svg
                className="w-full h-full text-white/90"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM13 12V7H11V14H17V12H13Z"
                  fill="currentColor"
                />
              </svg>
            </motion.div>
          </div>
        </motion.div>

        {/* Texte de chargement */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="space-y-4"
        >
          <h2 className="text-2xl font-bold text-white">
            Chargement en cours...
          </h2>
          <p className="text-white/80">Nous préparons votre contenu</p>
        </motion.div>

        {/* Barre de progression */}
        <motion.div
          initial={{ width: "0%" }}
          animate={{
            width: "100%",
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="h-1 bg-gradient-to-r from-yellow-400 via-red-500 to-green-400 rounded-full mt-8 max-w-md mx-auto"
        />
      </div>

      {/* Bordures décoratives */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-yellow-400/20 via-red-500/20 to-green-400/20" />
        <div className="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-green-400/20 via-red-500/20 to-yellow-400/20" />
        <div className="absolute top-0 left-0 h-full w-2 bg-gradient-to-b from-yellow-400/20 via-red-500/20 to-green-400/20" />
        <div className="absolute top-0 right-0 h-full w-2 bg-gradient-to-b from-green-400/20 via-red-500/20 to-yellow-400/20" />
      </div>
    </div>
  );
}
