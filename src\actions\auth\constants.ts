export const SESSION_COOKIE = "session_cookie";
export const SESSION_EXPIRY = 30 * 24 * 60 * 60 * 1000; // 30 jours

// Statuts possibles pour les différentes entités
export enum STATUS {
  PENDING = "pending",
  ACTIVE = "active",
  INACTIVE = "inactive",
  BLOCKED = "blocked",
}

// Statuts spécifiques pour les demandes de certificat
export enum CERTIFICATE_STATUS {
  DRAFT = "DRAFT", // Brouillon initial
  SUBMITTED = "SUBMITTED", // Soumis par le citoyen
  PENDING = "PENDING", // En attente de vérification par un agent
  VERIFIED = "VERIFIED", // Vérifié par un agent
  APPROVED = "APPROVED",
  READY = "READY", // Prêt pour signature
  SIGNED = "SIGNED", // Signé par le chef de quartier
  DELIVERED = "DELIVERED", // Délivré au citoyen

  REJECTED = "REJECTED", // Rejeté (à n'importe quelle étape)
  EXPIRED = "EXPIRED", // Expiré (après 90 jours de validité)
}

// Rôles des utilisateurs
export enum ROLES {
  ADMIN = "admin", // Administrateur système
  CHEF = "chef", // Chef de quartier
  AGENT = "agent", // Agent de quartier
  CITIZEN = "citizen", // Citoyen
  USER = "user", // Utilisateur de base
}

export enum CERTIFICATE_TYPE {
  RESIDENCE = "Certificat de résidence",
  NATIONALITY = "Certificat de nationalité",
  BIRTH_CERTIFICATE = "Certificat de naissance",
  DEATH_CERTIFICATE = "Certificat de décès",
  MARRIAGE_CERTIFICATE = "Certificat de mariage",
  DIVORCE_CERTIFICATE = "Certificat de divorce",
  CHILD_BIRTH_CERTIFICATE = "Certificat de naissance d'enfant",
  CHILD_DEATH_CERTIFICATE = "Certificat de décès d'enfant",
}

// Permissions détaillées par rôle
export const ROLE_PERMISSIONS = {
  [ROLES.ADMIN]: [
    "users.*", // Gestion complète des utilisateurs
    "certificates.*", // Gestion complète des certificats
    "quarters.*", // Gestion des quartiers
    "agents.*", // Gestion des agents
    "chiefs.*", // Gestion des chefs
    "citizens.*", // Gestion des citoyens
    "system.*", // Configuration système
  ],
  [ROLES.CHEF]: [
    "certificates.read.quarter", // Voir les certificats de son quartier
    "certificates.sign", // Signer les certificats
    "certificates.reject", // Rejeter les certificats
    "agents.manage.quarter", // Gérer les agents de son quartier
    "citizens.read.quarter", // Voir les citoyens de son quartier
    "reports.quarter", // Rapports sur son quartier
  ],
  [ROLES.AGENT]: [
    "certificates.read.quarter", // Voir les certificats du quartier
    "certificates.verify", // Vérifier les certificats
    "certificates.prepare", // Préparer pour signature
    "citizens.verify.quarter", // Vérifier les citoyens du quartier
    "documents.verify", // Vérifier les documents
  ],
  [ROLES.CITIZEN]: [
    "certificates.request", // Demander un certificat
    "certificates.read.own", // Voir ses certificats
    "profile.manage.own", // Gérer son profil
    "documents.upload.own", // Uploader ses documents
  ],
  [ROLES.USER]: [
    "profile.read.own", // Voir son profil
    "profile.update.own", // Mettre à jour son profil
  ],
} as const;

// Configuration par défaut pour chaque type d'utilisateur
export const USER_DEFAULTS = {
  [ROLES.ADMIN]: {
    role: ROLES.ADMIN,
    status: STATUS.ACTIVE,
    emailVerification: true,
    phoneVerification: true,
    requiresTwoFactor: true,
  },
  [ROLES.CHEF]: {
    role: ROLES.CHEF,
    status: STATUS.PENDING,
    emailVerification: true,
    phoneVerification: true,
    requiresTwoFactor: true,
    requiresQuarterAssignment: true,
  },
  [ROLES.AGENT]: {
    role: ROLES.AGENT,
    status: STATUS.PENDING,
    emailVerification: true,
    phoneVerification: true,
    requiresChiefAssignment: true,
  },
  [ROLES.CITIZEN]: {
    role: ROLES.CITIZEN,
    status: STATUS.PENDING,
    emailVerification: false,
    phoneVerification: true,
    requiresAddressVerification: true,
  },
} as const;

// Types pour les relations entre entités
export interface QuarterAssignment {
  userId: string;
  quarterId: string;
  role: ROLES;
  assignedAt: Date;
  assignedBy: string;
  status: STATUS;
}

// Types pour la validation des changements d'adresse
export interface AddressChangeRequest {
  citizenId: string;
  oldQuarterId: string;
  newQuarterId: string;
  requestDate: Date;
  status: STATUS;
  approvedByOldChief?: string;
  approvedByNewChief?: string;
  approvalDate?: Date;
}

// Configuration des workflows
export const WORKFLOW_CONFIG = {
  certificateValidity: 90, // jours
  addressChangeRequiresBothChiefs: true,
  requireDocumentVerification: true,
  allowAgentPreparation: true,
  requireChiefFinalApproval: true,
} as const;
