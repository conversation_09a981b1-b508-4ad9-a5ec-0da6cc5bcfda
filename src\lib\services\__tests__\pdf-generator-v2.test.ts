import { describe, it, expect, vi, beforeEach } from "vitest";
import { PdfGeneratorV1, PdfGeneratorV2, PdfGenerator } from "../pdf-generator";

// Mock des dépendances
vi.mock("@/actions/certificates", () => ({
  getCertificateWithFullData: vi.fn(),
}));

vi.mock("@/lib/server/appwrite", () => ({
  createAdminClient: vi.fn(),
}));

// Mock data pour les tests
const mockCertificate = {
  $id: "test-cert-id",
  reference: "NCR-CON-MATAM-20241201-12345",
  citizenId: "test-citizen-id",
  chefId: "test-chef-id",
  status: "APPROVED",
  motif: "Demande d'emploi",
  createdAt: "2024-01-10T10:00:00.000Z",
  deliveredAt: "2024-01-15T14:30:00.000Z",
  signatureFileId: null,
  citizen: {
    $id: "citizen-id",
    userId: "user-id",
    nom: "Diallo",
    prenom: "Mamadou",
    dateNaissance: "1990-05-15T00:00:00.000Z",
    lieuNaissance: "Conakry",
    nomPere: "Ibrahima Diallo",
    nomMere: "Fatoumata Camara",
    nationalite: "Guinéenne",
    profession: "Ingénieur",
    telephone: "+224 123 456 789",
    email: "<EMAIL>",
    carteElecteur: "CE123456789",
    adressePrecise: "Quartier Matam, Rue KA-001",
    dateInstallation: "2020-01-01T00:00:00.000Z",
    numeroBatiment: "B-15",
    proprietaireBatiment: "Propriétaire",
    quartier: "Matam",
    numeroIdentificationUnique: "19900515-20240110-A1B2C3D4",
    status: "APPROVED",
    role: "citizen",
  },
  citizenName: "Mamadou Diallo",
  chefQuartier: {
    $id: "chef-id",
    nom: "Touré",
    prenom: "Alpha",
  },
  chefQuartierName: "Alpha Touré",
  quartier: {
    $id: "quartier-id",
    nom: "Matam",
    commune: "Matam",
    region: "Conakry",
    chefId: "test-chef-id",
  },
};

describe("PdfGeneratorV2", () => {
  beforeEach(() => {
    // Reset des mocks avant chaque test
    vi.clearAllMocks();
    
    // Mock de getCertificateWithFullData
    const { getCertificateWithFullData } = require("@/actions/certificates");
    getCertificateWithFullData.mockResolvedValue({
      certificate: mockCertificate,
    });

    // Mock de createAdminClient
    const { createAdminClient } = require("@/lib/server/appwrite");
    createAdminClient.mockResolvedValue({
      storage: {
        getFileDownload: vi.fn().mockResolvedValue(new ArrayBuffer(0)),
      },
    });

    // Mock de fetch pour les images
    global.fetch = vi.fn().mockResolvedValue({
      arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
    });
  });

  describe("generateCertificatePdf", () => {
    it("should generate a PDF buffer", async () => {
      const result = await PdfGeneratorV2.generateCertificatePdf("test-cert-id");
      
      expect(result).toBeInstanceOf(Buffer);
      expect(result.length).toBeGreaterThan(0);
    });

    it("should include citizen ID in the PDF", async () => {
      const result = await PdfGeneratorV2.generateCertificatePdf("test-cert-id");
      
      // Vérifier que le PDF contient l'ID citoyen
      const pdfString = result.toString();
      expect(pdfString).toContain("19900515-20240110-A1B2C3D4");
    });

    it("should include certificate reference", async () => {
      const result = await PdfGeneratorV2.generateCertificatePdf("test-cert-id");
      
      const pdfString = result.toString();
      expect(pdfString).toContain("NCR-CON-MATAM-20241201-12345");
    });

    it("should include citizen information", async () => {
      const result = await PdfGeneratorV2.generateCertificatePdf("test-cert-id");
      
      const pdfString = result.toString();
      expect(pdfString).toContain("Mamadou Diallo");
      expect(pdfString).toContain("Ingénieur");
      expect(pdfString).toContain("Matam");
    });
  });

  describe("Security features", () => {
    it("should include security elements", async () => {
      const result = await PdfGeneratorV2.generateCertificatePdf("test-cert-id");
      
      const pdfString = result.toString();
      // Vérifier la présence d'éléments de sécurité
      expect(pdfString).toContain("SÉCURISÉ");
      expect(pdfString).toContain("GUINÉE");
    });

    it("should generate verification hash", async () => {
      const result = await PdfGeneratorV2.generateCertificatePdf("test-cert-id");
      
      const pdfString = result.toString();
      expect(pdfString).toContain("Hash:");
    });
  });
});

describe("PdfGeneratorV1", () => {
  it("should still be available for compatibility", async () => {
    const result = await PdfGeneratorV1.generateCertificatePdf("test-cert-id");
    
    expect(result).toBeInstanceOf(Buffer);
    expect(result.length).toBeGreaterThan(0);
  });
});

describe("PdfGenerator (Compatibility class)", () => {
  it("should use V2 by default", async () => {
    // Spy sur PdfGeneratorV2
    const v2Spy = vi.spyOn(PdfGeneratorV2, "generateCertificatePdf");
    
    await PdfGenerator.generateCertificatePdf("test-cert-id");
    
    expect(v2Spy).toHaveBeenCalledWith("test-cert-id");
  });

  it("should return same interface as before", async () => {
    const result = await PdfGenerator.generateCertificatePdf("test-cert-id");
    
    expect(result).toBeInstanceOf(Buffer);
    expect(result.length).toBeGreaterThan(0);
  });
});

describe("Design improvements", () => {
  it("should use modern colors", () => {
    // Test des constantes de couleur
    const COLORS_V2 = {
      RED: "#CE1126",
      YELLOW: "#FCD116", 
      GREEN: "#009639",
      DARK_GREEN: "#006B2F",
      LIGHT_GRAY: "#F8F9FA",
      DARK_GRAY: "#343A40",
      BLACK: "#000000",
      WHITE: "#FFFFFF",
    };

    expect(COLORS_V2.RED).toBe("#CE1126");
    expect(COLORS_V2.YELLOW).toBe("#FCD116");
    expect(COLORS_V2.GREEN).toBe("#009639");
  });

  it("should have proper design constants", () => {
    const DESIGN_V2 = {
      PAGE_MARGIN: 15,
      BORDER_WIDTH: 2,
      HEADER_HEIGHT: 80,
      FOOTER_HEIGHT: 60,
      CONTENT_PADDING: 20,
      SECURITY_OPACITY: 0.1,
    };

    expect(DESIGN_V2.PAGE_MARGIN).toBe(15);
    expect(DESIGN_V2.BORDER_WIDTH).toBe(2);
    expect(DESIGN_V2.SECURITY_OPACITY).toBe(0.1);
  });
});

describe("Error handling", () => {
  it("should handle missing signature gracefully", async () => {
    const certificateWithoutSignature = {
      ...mockCertificate,
      signatureFileId: null,
    };

    const { getCertificateWithFullData } = require("@/actions/certificates");
    getCertificateWithFullData.mockResolvedValue({
      certificate: certificateWithoutSignature,
    });

    const result = await PdfGeneratorV2.generateCertificatePdf("test-cert-id");
    
    expect(result).toBeInstanceOf(Buffer);
    expect(result.length).toBeGreaterThan(0);
  });

  it("should handle image loading errors", async () => {
    // Mock fetch pour simuler une erreur
    global.fetch = vi.fn().mockRejectedValue(new Error("Image not found"));

    const result = await PdfGeneratorV2.generateCertificatePdf("test-cert-id");
    
    // Le PDF devrait quand même être généré même sans images
    expect(result).toBeInstanceOf(Buffer);
    expect(result.length).toBeGreaterThan(0);
  });
});
