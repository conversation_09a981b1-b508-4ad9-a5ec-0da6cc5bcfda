import { ContactFormData } from "@/schemas/contact";

export function getContactEmailTemplate(data: ContactFormData) {
  return {
    userTemplate: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #004D40;">Confirmation de votre message</h2>
        <p>Bonjour ${data.name},</p>
        <p>Nous avons bien reçu votre message. Notre équipe vous répondra dans les plus brefs délais.</p>
        <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p><strong>Récapitulatif de votre message :</strong></p>
          <ul style="list-style: none; padding: 0;">
            <li><strong>Sujet :</strong> ${data.subject}</li>
            <li><strong>Service :</strong> ${data.service}</li>
            <li><strong>Message :</strong> ${data.message}</li>
          </ul>
        </div>
        <p>Cordialement,<br>L'équi<PERSON></p>
      </div>
    `,
    adminTemplate: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #004D40;">Nouvelle demande de contact</h2>
        <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p><strong>Informations du contact :</strong></p>
          <ul style="list-style: none; padding: 0;">
            <li><strong>Nom :</strong> ${data.name}</li>
            <li><strong>Email :</strong> ${data.email}</li>
            <li><strong>Service :</strong> ${data.service}</li>
            <li><strong>Sujet :</strong> ${data.subject}</li>
          </ul>
          <p><strong>Message :</strong></p>
          <p style="white-space: pre-wrap;">${data.message}</p>
        </div>
      </div>
    `,
  };
}
