'use client'

import { siteConfig } from '@/config/seo'
import { buildUrl } from '@/config/env'

interface JsonLdProps {
  title?: string
  description?: string
  path?: string
  image?: string
  type?: 'WebSite' | 'WebPage' | 'Article' | 'Organization'
  datePublished?: string
  dateModified?: string
}

export function JsonLd({
  title = siteConfig.name,
  description = siteConfig.description,
  path = '',
  image = siteConfig.ogImage,
  type = 'WebSite',
  datePublished,
  dateModified,
}: JsonLdProps) {
  const url = buildUrl(path)
  const schemaData = {
    '@context': 'https://schema.org',
    '@type': type,
    name: title,
    description: description,
    url: url,
    ...(image && { image: buildUrl(image) }),
    ...(datePublished && { datePublished }),
    ...(dateModified && { dateModified }),
    publisher: {
      '@type': 'Organization',
      name: '<PERSON><PERSON><PERSON>b<PERSON> de Guinée',
      logo: {
        '@type': 'ImageObject',
        url: buildUrl('/logo.png'),
      },
    },
    ...(type === 'WebSite' && {
      potentialAction: {
        '@type': 'SearchAction',
        target: buildUrl('/search?q={search_term_string}'),
        'query-input': 'required name=search_term_string',
      },
    }),
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
    />
  )
}
