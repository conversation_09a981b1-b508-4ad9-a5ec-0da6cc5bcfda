"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { getCurrentUser } from "@/actions/auth/session";
import { logout } from "@/actions/auth/logout";
import { login as loginAction } from "@/actions/auth/login";
import { User } from "@/types/auth";

// Type pour les préférences utilisateur
interface UserPreferences {
  avatarUrl?: string;
  role?: string;
}

// Type pour les credentials de connexion
interface LoginCredentials {
  email: string;
  password: string;
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const { success, user } = await getCurrentUser();
      if (success && user) {
        setUser({
          ...user,
          prefs: user.prefs || {},
        });
      } else {
        setUser(null);
      }
    } catch (error) {
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true);
    const result = await loginAction(credentials);

    if (result.success) {
      await checkAuth(); // Recharger les infos utilisateur
      toast({
        title: "Connexion réussie",
        description: "Vous allez être redirigé vers votre tableau de bord.",
        variant: "success",
      });
      router.push("/dashboard");
      router.refresh();
      setIsLoading(false);
      return result;
    } else {
      console.log("result->", result);
      setIsLoading(false);
      return result;
    }
  };

  const signOut = async () => {
    try {
      const result = await logout();

      if (result.success) {
        setUser(null);
        toast({
          title: "Déconnexion réussie",
          description: "À bientôt !",
          variant: "info",
        });
        router.push("/");
        router.refresh();
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error("Erreur lors de la déconnexion:", error);
      toast({
        variant: "error",
        title: "Erreur",
        description: "Impossible de se déconnecter. Veuillez réessayer.",
      });
    }
  };

  return {
    user,
    isLoading,
    login,
    signOut,
    isAuthenticated: !!user,
  };
}
