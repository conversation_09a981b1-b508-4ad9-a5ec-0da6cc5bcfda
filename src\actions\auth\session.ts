"use server";

import { cookies } from "next/headers";
import { createSessionClient } from "@/lib/server/appwrite";
import { SESSION_COOKIE } from "./constants";

export async function getCurrentUser() {
  try {
    const cookieStore = await cookies();
    const sessionId = cookieStore.get(SESSION_COOKIE)?.value;

    if (!sessionId) {
      return {
        success: false,
        user: null,
      };
    }

    // Création du client avec la session de l'utilisateur
    const { account } = await createSessionClient(sessionId);

    // Récupération de l'utilisateur actuel
    const user = await account.get();

    return {
      success: true,
      user,
    };
  } catch (error) {
    console.error("Erreur de récupération de l'utilisateur:", error);
    return {
      success: false,
      user: null,
    };
  }
}

export async function getSession() {
  try {
    const cookieStore = await cookies();
    const sessionId = cookieStore.get(SESSION_COOKIE)?.value;

    if (!sessionId) {
      return {
        success: false,
        session: null,
      };
    }

    // Création du client avec la session de l'utilisateur
    const { account } = await createSessionClient(sessionId);

    // Récupération de la session actuelle
    const session = await account.getSession(sessionId);

    return {
      success: true,
      session,
    };
  } catch (error) {
    console.error("Erreur de récupération de session:", error);
    return {
      success: false,
      session: null,
    };
  }
}
