import sharp from "sharp";
import path from "path";
import fs from "fs/promises";

const LOGO_PATH = path.join(process.cwd(), "public", "logo.png");
const ICONS_DIR = path.join(process.cwd(), "public", "icons");

// Tailles d'icônes requises pour PWA
const ICON_SIZES = [
  72,
  96,
  128,
  144,
  152,
  192,
  384,
  512
];

async function generateIcons() {
  try {
    // 1. Vérifier/créer le dossier des icônes
    await fs.mkdir(ICONS_DIR, { recursive: true });
    console.log("📁 Dossier icons créé ou vérifié");

    // 2. Charger l'image source
    const sourceImage = sharp(LOGO_PATH);
    const metadata = await sourceImage.metadata();

    if (!metadata.width || !metadata.height) {
      throw new Error("Impossible de lire les dimensions du logo source");
    }

    console.log(`🖼️  Logo source chargé (${metadata.width}x${metadata.height}px)`);

    // 3. Générer les icônes pour chaque taille
    for (const size of ICON_SIZES) {
      const outputPath = path.join(ICONS_DIR, `icon-${size}x${size}.png`);

      await sourceImage
        .clone()
        .resize(size, size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }
        })
        .png({
          quality: 90,
          compressionLevel: 9,
        })
        .toFile(outputPath);

      console.log(`✓ Icône ${size}x${size}px générée`);
    }

    console.log("\n✨ Génération des icônes terminée avec succès!");
  } catch (error) {
    console.error("❌ Erreur lors de la génération des icônes:", error);
    process.exit(1);
  }
}

// Exécuter le script
generateIcons();