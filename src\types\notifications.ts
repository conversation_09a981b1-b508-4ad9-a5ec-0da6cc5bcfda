export type NotificationPermission = "default" | "denied" | "granted";

export interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: {
    [key: string]: any;
  };
  actions?: NotificationAction[];
  tag?: string;
  timestamp?: number;
}

export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

export interface PushSubscriptionKeys {
  [key: string]: string;
  p256dh: string;
  auth: string;
}

export interface SerializedPushSubscription {
  endpoint: string;
  expirationTime: number | null;
  keys: PushSubscriptionKeys;
}

declare global {
  interface PushSubscriptionOptionsInit {
    applicationServerKey?: BufferSource | string | null;
    userVisibleOnly?: boolean;
  }

  interface PushManager {
    subscribe(options?: PushSubscriptionOptionsInit): Promise<PushSubscription>;
    getSubscription(): Promise<PushSubscription | null>;
    permissionState(options?: PushSubscriptionOptionsInit): Promise<PermissionState>;
  }

  interface ServiceWorkerRegistration {
    readonly pushManager: PushManager;
    showNotification(title: string, options?: NotificationOptions): Promise<void>;
  }

  interface ExtendableEvent extends Event {
    waitUntil(fn: Promise<any>): void;
  }

  interface ExtendableEventInit extends EventInit {
    waitUntil?(fn: Promise<any>): void;
  }

  interface PushMessageData {
    arrayBuffer(): ArrayBuffer;
    blob(): Blob;
    json(): any;
    text(): string;
  }

  interface PushEvent extends ExtendableEvent {
    data: PushMessageData;
  }

  interface NotificationEventInit extends ExtendableEventInit {
    notification: Notification;
    action: string;
  }

  interface NotificationEvent extends ExtendableEvent {
    notification: Notification;
    action: string;
  }

  interface ServiceWorkerGlobalScope {
    registration: ServiceWorkerRegistration;
  }
}

export interface NotificationClickEvent extends NotificationEvent {}
