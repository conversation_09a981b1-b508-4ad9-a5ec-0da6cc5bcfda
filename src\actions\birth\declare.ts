"use server";

import { getCurrentUser } from "@/actions/auth/session";
import { createAdminClient } from "@/lib/server/appwrite";
import {
  BIRTH_DECLARATIONS_COLLECTION_ID,
  DATABASE_ID,
  DOCUMENTS_COLLECTION_ID,
} from "@/lib/server/database";
import { DOCUMENTS_BUCKET_ID } from "@/lib/server/storage";
import { generateReference } from "@/lib/utils/reference";
import { DeclarationNaissanceFormData } from "@/schemas/birth";
import { AttachedDocumentStatus } from "@/schemas/citizen";
import { revalidatePath } from "next/cache";
import { ID, Query } from "node-appwrite";
import { CERTIFICATE_STATUS, CERTIFICATE_TYPE } from "../auth/constants";

export async function declareBirth(data: DeclarationNaissanceFormData) {
  try {
    // 1. Vérification de l'authentification
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    // 2. Initialisation des clients Appwrite
    const { databases, storage } = await createAdminClient();

    // 3. Upload du certificat de naissance
    let certificatNaissanceId = null;
    if (data.certificatNaissance) {
      // Upload du fichier
      const uploadedFile = await storage.createFile(
        DOCUMENTS_BUCKET_ID,
        ID.unique(),
        data.certificatNaissance
      );

      // Création du document dans la collection documents
      const document = await databases.createDocument(
        DATABASE_ID,
        DOCUMENTS_COLLECTION_ID,
        ID.unique(),
        {
          type: CERTIFICATE_TYPE.CHILD_BIRTH_CERTIFICATE,
          fileId: uploadedFile.$id,
          fileName: data.certificatNaissance.name,
          fileSize: data.certificatNaissance.size.toString(),
          mimeType: data.certificatNaissance.type,
          status: AttachedDocumentStatus.PENDING,
        }
      );

      certificatNaissanceId = document.$id;
    }

    // 4. Création de la déclaration
    const declaration = await databases.createDocument(
      DATABASE_ID,
      BIRTH_DECLARATIONS_COLLECTION_ID,
      ID.unique(),
      {
        reference: generateReference("DN"),
        userId: user.$id,
        // Informations sur l'enfant
        nomEnfant: data.nomEnfant,
        prenomEnfant: data.prenomEnfant,
        dateNaissance: data.dateNaissance,
        lieuNaissance: data.lieuNaissance,
        sexe: data.sexe,

        // Informations sur les parents
        nomPere: data.nomPere,
        prenomPere: data.prenomPere,
        professionPere: data.professionPere,
        nationalitePere: data.nationalitePere,

        nomMere: data.nomMere,
        prenomMere: data.prenomMere,
        professionMere: data.professionMere,
        nationaliteMere: data.nationaliteMere,

        // Informations sur le déclarant
        nomDeclarant: data.nomDeclarant,
        prenomDeclarant: data.prenomDeclarant,
        qualiteDeclarant: data.qualiteDeclarant,
        telephoneDeclarant: data.telephoneDeclarant,

        // Documents
        certificatNaissanceId,

        // Métadonnées
        status: CERTIFICATE_STATUS.PENDING,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    );

    revalidatePath("/services/declaration-naissance");
    return { success: true, data: declaration };
  } catch (error) {
    console.error("Erreur côté serveur:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Une erreur est survenue",
    };
  }
}

export async function getBirthDeclarations(params: {
  limit?: number;
  offset?: number;
  search?: string;
  status?: string;
}) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();
    const queries = [];

    // Filtres
    if (params.search) {
      queries.push(Query.search("reference", params.search));
    }

    if (params.status) {
      queries.push(Query.equal("status", params.status));
    }

    // Pagination
    if (params.limit) {
      queries.push(Query.limit(params.limit));
    }

    if (params.offset) {
      queries.push(Query.offset(params.offset));
    }

    // Récupération des déclarations
    const { documents, total } = await databases.listDocuments(
      DATABASE_ID,
      BIRTH_DECLARATIONS_COLLECTION_ID,
      queries
    );

    return {
      success: true,
      declarations: documents,
      pagination: {
        total,
        limit: params.limit || 10,
        offset: params.offset || 0,
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des déclarations:", error);
    throw error;
  }
}
