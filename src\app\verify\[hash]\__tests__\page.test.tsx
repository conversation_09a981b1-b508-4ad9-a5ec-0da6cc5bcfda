import { render } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import VerifyPage from "../page";

// Mock the verification action
vi.mock("@/actions/certificate-verification", () => ({
  verifyCertificate: vi.fn(),
}));

// Mock Next.js navigation
vi.mock("next/navigation", () => ({
  notFound: vi.fn(),
}));

// Mock the SSR display component
vi.mock("@/components/verification/verification-display-ssr", () => ({
  VerificationDisplaySSR: ({ result, hash }: any) => (
    <div data-testid="verification-display-ssr">
      <div>Hash: {hash}</div>
      <div>Status: {result.status}</div>
      <div>Message: {result.message}</div>
    </div>
  ),
}));

const mockVerificationResult = {
  isValid: true,
  status: "valid" as const,
  message: "Certificat valide et authentique",
  certificateInfo: {
    reference: "CON-2024-001",
    issuedAt: "2024-01-15T10:00:00Z",
    expiresAt: "2024-04-15T10:00:00Z",
    issuerName: "Chef de Quartier Test",
    region: "Conakry",
    commune: "Kaloum",
    quartier: "Centre-Ville",
    verificationCount: 5,
    lastVerifiedAt: "2024-01-20T15:30:00Z",
  },
  citizenInfo: {
    fullName: "Test Citizen",
    profession: "Ingénieur",
    nationality: "Guinéenne",
    residenceLocation: "Centre-Ville, Kaloum, Conakry",
    certificateType: "Certificat de Résidence",
    purpose: "Démarches administratives",
    validityPeriod: "3 mois",
  },
  documentStatus: {
    isValid: true,
    expirationStatus: "valid" as const,
    legalValidity: "Valide sur tout le territoire de la République de Guinée",
  },
  verificationDetails: {
    verificationTimestamp: "2024-01-20T15:30:00Z",
    qrCodeScanned: true,
    securityBadgeValidated: true,
    cryptographicVerification: true,
  },
};

describe("VerifyPage SSR", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render SSR components without client-side dependencies", async () => {
    const { verifyCertificate } = await import(
      "@/actions/certificate-verification"
    );
    vi.mocked(verifyCertificate).mockResolvedValue(mockVerificationResult);

    const params = Promise.resolve({
      hash: "a1b2c3d4e5f6789012345678901234567",
    });
    const searchParams = Promise.resolve({});

    const { container } = render(await VerifyPage({ params, searchParams }));

    // Check that SSR-safe components are rendered
    expect(container.querySelector("header")).toBeTruthy();
    expect(container.querySelector("footer")).toBeTruthy();

    // Check for Guinea national elements
    expect(container.textContent).toContain("RÉPUBLIQUE DE GUINÉE");
    expect(container.textContent).toContain("Travail - Justice - Solidarité");

    // Check for verification system info
    expect(container.textContent).toContain("Système de Vérification Officiel");
  });

  it("should handle invalid hash format", async () => {
    const { notFound } = await import("next/navigation");

    const params = Promise.resolve({ hash: "invalid-hash" });
    const searchParams = Promise.resolve({});

    await VerifyPage({ params, searchParams });

    expect(notFound).toHaveBeenCalled();
  });

  it("should handle verification errors gracefully", async () => {
    const { verifyCertificate } = await import(
      "@/actions/certificate-verification"
    );
    vi.mocked(verifyCertificate).mockRejectedValue(new Error("Database error"));

    const params = Promise.resolve({
      hash: "a1b2c3d4e5f6789012345678901234567",
    });
    const searchParams = Promise.resolve({});

    const { container } = render(await VerifyPage({ params, searchParams }));

    // Should still render the page structure
    expect(container.querySelector("header")).toBeTruthy();
    expect(container.querySelector("footer")).toBeTruthy();
  });

  it("should include debug info when debug parameter is provided", async () => {
    const { verifyCertificate } = await import(
      "@/actions/certificate-verification"
    );
    vi.mocked(verifyCertificate).mockResolvedValue(mockVerificationResult);

    const params = Promise.resolve({
      hash: "a1b2c3d4e5f6789012345678901234567",
    });
    const searchParams = Promise.resolve({
      debug: "true",
      cert: "cert-id-123",
      ts: "1642680000000",
    });

    const { container } = render(await VerifyPage({ params, searchParams }));

    // Should render without errors even with debug info
    expect(container.querySelector("header")).toBeTruthy();
  });

  it("should generate proper metadata", async () => {
    const { generateMetadata } = await import("../page");
    const params = Promise.resolve({
      hash: "a1b2c3d4e5f6789012345678901234567",
    });

    const metadata = await generateMetadata({ params });

    expect(metadata.title).toContain("Vérification de Certificat");
    expect(metadata.title).toContain("a1b2c3d4");
    expect(metadata.description).toContain("Vérification d'authenticité");
    expect(metadata.robots).toBe("noindex, nofollow");
  });
});
