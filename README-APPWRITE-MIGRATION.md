# 🔄 Migration Appwrite - Métadonnées des Certificats

## 🎯 Vue d'ensemble

Migration réussie du système de vérification des certificats pour être compatible avec les contraintes d'Appwrite. Les métadonnées complexes ont été séparées dans une collection dédiée utilisant uniquement des types primitifs.

## ✅ Objectifs atteints

### 🏗️ **Architecture refactorisée**

- ✅ **Collection séparée** `certificate_verifications_metadata` créée
- ✅ **Relations** entre `certificate_verifications` et `certificate_verifications_metadata`
- ✅ **Types primitifs** uniquement (string, compatible Appwrite)
- ✅ **Pattern inspiré** de `src/actions/payment/index.ts`

### 🔧 **Compatibilité maintenue**

- ✅ **Interface publique** identique préservée
- ✅ **Fonctionnalités** de vérification intactes
- ✅ **Performance** optimisée avec index appropriés
- ✅ **Gestion d'erreurs** robuste

## 📊 Nouvelle structure

### Collections Appwrite

```
┌─────────────────────────────────────┐
│     certificate_verifications      │
├─────────────────────────────────────┤
│ • hash (string, unique)             │
│ • certificateId (string)            │
│ • citizenId (string)                │
│ • issuedAt (string)                 │
│ • expiresAt (string)                │
│ • isValid (string: "true"/"false")  │
│ • isRevoked (string: "true"/"false")│
│ • verificationCount (string)        │
│ • metadataId (string) ──────────────┼─┐
│ • lastVerifiedAt (string?)          │ │
│ • createdAt (string)                │ │
│ • updatedAt (string)                │ │
└─────────────────────────────────────┘ │
                                        │
                                        │ 1:1
                                        │
┌─────────────────────────────────────┐ │
│      certificate_verifications_metadata          │◄┘
├─────────────────────────────────────┤
│ • verificationId (string, unique)   │
│ • issuerType (string)               │
│ • issuerId (string)                 │
│ • issuerName (string)               │
│ • region (string)                   │
│ • commune (string)                  │
│ • quartier (string)                 │
│ • revocationReason (string?)        │
│ • revokedAt (string?)               │
│ • revokedBy (string?)               │
│ • createdAt (string)                │
│ • updatedAt (string)                │
└─────────────────────────────────────┘
```

## 🔧 Adaptations techniques

### 1. **Conversion des types**

```typescript
// Helper functions pour Appwrite
private static booleanToString(value: boolean): string {
  return value.toString();
}

private static stringToBoolean(value: string): boolean {
  return value === "true";
}

private static numberToString(value: number): string {
  return value.toString();
}

private static stringToNumber(value: string): number {
  return parseInt(value, 10) || 0;
}
```

### 2. **Création transactionnelle**

```typescript
static async createVerification(data) {
  // 1. Créer verification avec metadataId temporaire
  const verification = await databases.createDocument(/*...*/);

  // 2. Créer metadata avec verificationId
  const metadata = await this.createMetadata(verification.$id, data.metadata);

  // 3. Mettre à jour verification avec metadataId
  const updated = await databases.updateDocument(verification.$id, {
    metadataId: metadata.$id
  });

  // 4. Retourner objet combiné
  return { ...updated, metadata };
}
```

### 3. **Interface combinée**

```typescript
// Interface pour l'utilisation dans l'application
export interface CertificateVerificationWithMetadata
  extends CertificateVerification {
  metadata: CertificateMetadata;
}
```

## 📁 Fichiers modifiés

### Code principal

- ✅ `src/lib/database/certificate-verification.ts` - Service refactorisé
- ✅ `src/lib/server/database.ts` - Définitions des collections
- ✅ `src/actions/certificate-verification.ts` - Server actions mis à jour
- ✅ `src/lib/services/pdf-generator-v2-enhanced.ts` - Import mis à jour

### Tests

- ✅ `src/lib/services/__tests__/pdf-generator-v2-enhanced.test.ts` - Mocks adaptés

### Scripts et documentation

- ✅ `scripts/create-verification-collections.ts` - Script de création
- ✅ `docs/appwrite-metadata-migration.md` - Documentation technique

## 🚀 Déploiement

### 1. **Créer les collections Appwrite**

```bash
# Configurer les variables d'environnement
export NEXT_PUBLIC_APPWRITE_ENDPOINT="https://your-endpoint"
export NEXT_PUBLIC_APPWRITE_PROJECT="your-project-id"
export APPWRITE_API_KEY="your-api-key"
export APPWRITE_DATABASE_ID="your-database-id"

# Exécuter le script de création
npx tsx scripts/create-verification-collections.ts
```

### 2. **Variables d'environnement requises**

```env
# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://your-appwrite-endpoint
NEXT_PUBLIC_APPWRITE_PROJECT=your-project-id
APPWRITE_API_KEY=your-api-key
APPWRITE_DATABASE_ID=your-database-id

# Certificate Security
CERTIFICATE_SECRET_KEY=your-super-secret-key-change-in-production
NEXT_PUBLIC_APP_URL=https://ncr.ouestech.com
```

### 3. **Validation post-déploiement**

```typescript
// Test de création d'une vérification
const verification = await CertificateVerificationService.createVerification({
  hash: "test-hash-12345678901234567890123456789012",
  certificateId: "cert-123",
  citizenId: "citizen-456",
  issuedAt: new Date(),
  expiresAt: new Date(Date.now() + 3 * 30 * 24 * 60 * 60 * 1000),
  metadata: {
    issuerType: "chef",
    issuerId: "chef-789",
    issuerName: "Alpha Touré",
    region: "Conakry",
    commune: "Matam",
    quartier: "Matam",
  },
});

console.log("✅ Vérification créée:", verification);
```

## 🧪 Tests

### Exécution des tests

```bash
# Tests du service de vérification
pnpm test src/lib/database/__tests__/certificate-verification.test.ts

# Tests du générateur PDF
pnpm test src/lib/services/__tests__/pdf-generator-v2-enhanced.test.ts

# Tests des server actions
pnpm test src/actions/__tests__/certificate-verification.test.ts
```

### Validation manuelle

```typescript
// 1. Créer une vérification
const verification =
  await CertificateVerificationService.createVerification(/*...*/);

// 2. Récupérer par hash
const retrieved = await CertificateVerificationService.getVerificationByHash(
  verification.hash
);

// 3. Valider le certificat
const validation = await CertificateVerificationService.validateCertificate(
  verification.hash
);

// 4. Obtenir les statistiques
const stats = await CertificateVerificationService.getVerificationStats();
```

## 📊 Index et performances

### Collections optimisées

- **certificate_verifications** : 6 index (hash unique, certificateId, citizenId, etc.)
- **certificate_verifications_metadata** : 4 index (verificationId unique, issuer, location, fulltext)

### Requêtes optimisées

- ✅ **Recherche par hash** : Index unique sur `hash`
- ✅ **Jointures** : Index sur `metadataId` et `verificationId`
- ✅ **Recherche géographique** : Index composite sur `region`, `commune`, `quartier`
- ✅ **Recherche textuelle** : Index fulltext sur `issuerName`

## 🔒 Sécurité et robustesse

### Gestion des erreurs

- ✅ **Transactions** : Création atomique des enregistrements liés
- ✅ **Rollback** : Gestion des erreurs de création
- ✅ **Validation** : Vérification de l'intégrité des relations
- ✅ **Logging** : Traçabilité complète des opérations

### Types sécurisés

- ✅ **Conversion** automatique des types boolean/number ↔ string
- ✅ **Validation** des formats de données
- ✅ **Interface** typée pour TypeScript

## ✅ Checklist de migration

- [x] **Architecture** : Collections séparées avec relations 1:1
- [x] **Types** : Conversion automatique des types primitifs
- [x] **Service** : CertificateVerificationService refactorisé
- [x] **Actions** : Server actions mis à jour
- [x] **Tests** : Mocks et tests adaptés
- [x] **Script** : Création automatique des collections
- [x] **Documentation** : Guide complet de migration
- [x] **Validation** : Tests passants et diagnostics propres

## 🎉 Résultat

La migration est **complète et opérationnelle** ! Le système de vérification des certificats est maintenant :

- 🔧 **Compatible** avec les contraintes d'Appwrite
- 📊 **Performant** avec des index optimisés
- 🛡️ **Robuste** avec une gestion d'erreurs améliorée
- 🔄 **Maintenable** avec une architecture claire
- ✅ **Testé** et validé

**Prêt pour la production !** 🚀
