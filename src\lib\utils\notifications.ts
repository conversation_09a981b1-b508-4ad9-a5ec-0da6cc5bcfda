import { NotificationPermission } from '@/types/notifications';

// Types étendus pour les notifications
interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

interface ServiceWorkerNotificationOptions extends NotificationOptions {
  vibrate?: number[];
  requireInteraction?: boolean;
  actions?: NotificationAction[];
  data?: any;
}

export async function requestNotificationPermission(): Promise<NotificationPermission> {
  if (!('Notification' in window)) {
    return 'denied';
  }

  try {
    const permission = await Notification.requestPermission();
    return permission;
  } catch (error) {
    console.error('Erreur lors de la demande de permission:', error);
    return 'denied';
  }
}

export async function subscribeToPushNotifications(registration: ServiceWorkerRegistration) {
  try {
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY
    });

    // Envoyer la subscription au serveur
    await fetch('/api/notifications/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(subscription),
    });

    return subscription;
  } catch (error) {
    console.error('Erreur lors de l\'inscription aux notifications:', error);
    throw error;
  }
}

export async function showNotification(title: string, options: Partial<ServiceWorkerNotificationOptions> = {}) {
  try {
    if (!("Notification" in window)) {
      console.warn("Ce navigateur ne supporte pas les notifications desktop");
      return;
    }

    if (Notification.permission === "granted") {
      if ('serviceWorker' in navigator && navigator.serviceWorker.ready) {
        const registration = await navigator.serviceWorker.ready;
        const notificationOptions: ServiceWorkerNotificationOptions = {
          ...options,
          icon: "/icons/icon-192x192.png",
          badge: "/icons/icon-96x96.png",
          vibrate: [100, 50, 100],
          requireInteraction: true,
          actions: [
            {
              action: 'open',
              title: 'Ouvrir',
            },
            {
              action: 'close',
              title: 'Fermer',
            },
          ],
          // Données additionnelles pour le handler
          data: {
            type: 'NOTIFICATION_CLICK',
            url: options.data?.url,
          },
        };

        await registration.showNotification(title, notificationOptions);
      } else {
        // Fallback pour les navigateurs sans ServiceWorker
        const basicOptions: NotificationOptions = {
          ...options,
          icon: "/icons/icon-192x192.png",
          badge: "/icons/icon-96x96.png",
        };

        const notification = new Notification(title, basicOptions);
        notification.onclick = function() {
          window.focus();
          notification.close();
        };
      }
    } else if (Notification.permission !== "denied") {
      const permission = await Notification.requestPermission();
      if (permission === "granted") {
        await showNotification(title, options);
      }
    }
  } catch (error) {
    console.error("Erreur lors de l'affichage de la notification:", error);
  }
}

export function setupNotificationClickHandler() {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data.type === 'NOTIFICATION_CLICK') {
        const { action, url } = event.data;
        
        switch (action) {
          case 'open':
            if (url) {
              window.location.href = url;
            }
            break;
          case 'close':
            // Fermer la notification est géré automatiquement
            break;
          default:
            window.focus();
            break;
        }
      }
    });
  }
}
