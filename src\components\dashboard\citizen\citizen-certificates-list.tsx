"use client";

import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { CertificateActions } from "@/components/certificates/certificate-actions";
import { StatusBadge } from "@/components/certificates/status-badge";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Loader } from "@/components/ui/loader";
import { useCitizenCertificates } from "@/hooks/use-citizen-certificates";
import { cn } from "@/lib/utils/cn";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { AnimatePresence, motion } from "framer-motion";
import {
  AlertTriangle,
  Ban,
  Bell,
  Calendar,
  CheckCircle,
  Clock,
  FileCheck,
  FileOutput,
  FileSignature,
  FileText,
  Send,
  Timer,
  XCircle,
} from "lucide-react";
import Link from "next/link";

interface StatusConfig {
  icon: any;
  color: string;
  bg: string;
  label: string;
}

const statusConfig: Record<CERTIFICATE_STATUS, StatusConfig> = {
  [CERTIFICATE_STATUS.DRAFT]: {
    icon: FileText,
    color: "text-neutral-600",
    bg: "bg-neutral-100",
    label: "Brouillon",
  },
  [CERTIFICATE_STATUS.SUBMITTED]: {
    icon: Send,
    color: "text-blue-600",
    bg: "bg-blue-50",
    label: "Soumis",
  },
  [CERTIFICATE_STATUS.PENDING]: {
    icon: Clock,
    color: "text-amber-600",
    bg: "bg-amber-50",
    label: "En cours",
  },
  [CERTIFICATE_STATUS.VERIFIED]: {
    icon: FileCheck,
    color: "text-teal-600",
    bg: "bg-teal-50",
    label: "Vérifié",
  },
  [CERTIFICATE_STATUS.APPROVED]: {
    icon: CheckCircle,
    color: "text-emerald-600",
    bg: "bg-emerald-50",
    label: "Approuvé",
  },
  [CERTIFICATE_STATUS.READY]: {
    icon: FileCheck,
    color: "text-violet-600",
    bg: "bg-violet-50",
    label: "Prêt",
  },
  [CERTIFICATE_STATUS.SIGNED]: {
    icon: FileSignature,
    color: "text-indigo-600",
    bg: "bg-indigo-50",
    label: "Signé",
  },
  [CERTIFICATE_STATUS.DELIVERED]: {
    icon: FileOutput,
    color: "text-green-600",
    bg: "bg-green-50",
    label: "Délivré",
  },
  [CERTIFICATE_STATUS.REJECTED]: {
    icon: XCircle,
    color: "text-red-600",
    bg: "bg-red-50",
    label: "Rejeté",
  },
  [CERTIFICATE_STATUS.EXPIRED]: {
    icon: Ban,
    color: "text-neutral-600",
    bg: "bg-neutral-100",
    label: "Expiré",
  },
};

export function CitizenCertificatesList({ limit = 5 }: { limit?: number }) {
  const { certificates, isLoading } = useCitizenCertificates(limit);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <Loader
          variant="primary"
          size="lg"
          text="Chargement de vos demandes..."
        />
      </div>
    );
  }

  if (!certificates || certificates.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="flex flex-col items-center justify-center min-h-[200px] text-neutral-500"
      >
        <FileText className="w-12 h-12 mb-4 text-neutral-400" />
        <p className="text-lg font-medium">Aucune demande pour le moment</p>
        <p className="text-sm">Commencez par créer une nouvelle demande</p>
        <Button asChild className="mt-4" variant="outline">
          <Link href="/certificates/new">Nouvelle demande</Link>
        </Button>
      </motion.div>
    );
  }

  return (
    <div className="space-y-6">
      <motion.h2
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-lg font-semibold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent"
      >
        Vos dernières demandes
      </motion.h2>

      <div className="space-y-4">
        <AnimatePresence mode="popLayout">
          {certificates.map((certificate, index) => {
            const status = statusConfig[certificate.status];
            const StatusIcon = status.icon;

            return (
              <motion.div
                key={certificate.$id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ delay: index * 0.1 }}
              >
                <div
                  className={cn(
                    "group relative overflow-hidden rounded-xl border border-neutral-200/60 bg-white p-4",
                    "hover:shadow-lg hover:border-neutral-300/80 transition-all duration-300",
                    certificate.stats.isUrgent && "ring-2 ring-red-500/20"
                  )}
                >
                  {/* Effet de brillance au survol */}
                  <div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent
                    translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000"
                  />

                  {/* Badges de statut spéciaux */}
                  <div className="absolute top-4 right-4 flex flex-wrap items-center gap-2 max-w-[50%] justify-end">
                    {certificate.stats.isNew && (
                      <Badge
                        variant="info"
                        size="sm"
                        icon={<Bell className="w-3 h-3" />}
                      >
                        Nouveau
                      </Badge>
                    )}
                    {certificate.stats.hasUpdates && (
                      <Badge
                        variant="warning"
                        size="sm"
                        icon={<Clock className="w-3 h-3" />}
                      >
                        Mis à jour
                      </Badge>
                    )}
                    {certificate.stats.isUrgent && (
                      <Badge
                        variant="error"
                        size="sm"
                        icon={<AlertTriangle className="w-3 h-3" />}
                      >
                        Urgent
                      </Badge>
                    )}
                    {certificate.stats.timeToExpire !== undefined &&
                      certificate.stats.timeToExpire <= 30 && (
                        <Badge
                          variant="warning"
                          size="sm"
                          icon={<Timer className="w-3 h-3" />}
                        >
                          Expire dans {certificate.stats.timeToExpire}j
                        </Badge>
                      )}
                  </div>

                  <div className="relative flex items-start gap-4 pt-8">
                    <div
                      className={cn(
                        "w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0",
                        "group-hover:scale-110 transition-transform duration-300",
                        status.bg
                      )}
                    >
                      <StatusIcon className={cn("w-5 h-5", status.color)} />
                    </div>

                    <div className="flex-1 min-w-0 space-y-1">
                      <Link
                        href={`/dashboard/certificates/${certificate.$id}`}
                        className="block space-y-2"
                      >
                        <div className="flex items-center gap-2 flex-wrap">
                          <p className="font-medium text-neutral-900">
                            {certificate.reference}
                          </p>
                          <StatusBadge status={certificate.status} />
                        </div>
                        <p className="text-sm text-neutral-600 line-clamp-2">
                          {certificate.motif || "Aucun motif spécifié"}
                        </p>
                      </Link>
                    </div>

                    <div className="flex flex-col items-end gap-4 ml-4">
                      <div className="text-right whitespace-nowrap">
                        <div className="flex items-center gap-1 text-sm text-neutral-500">
                          <Calendar className="w-4 h-4 flex-shrink-0" />
                          {formatDistanceToNow(
                            new Date(certificate.$updatedAt),
                            {
                              addSuffix: true,
                              locale: fr,
                            }
                          )}
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        <CertificateActions
                          certificate={certificate}
                          userRole="citizen"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </AnimatePresence>

        {certificates.length > limit && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center mt-6"
          >
            <Button asChild variant="outline" className="gap-2">
              <Link href="/certificates">
                <FileText className="w-4 h-4" />
                Voir toutes vos demandes
              </Link>
            </Button>
          </motion.div>
        )}
      </div>
    </div>
  );
}
