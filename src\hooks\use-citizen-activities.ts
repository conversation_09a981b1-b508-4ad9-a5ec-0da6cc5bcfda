import { useAuth } from "@/hooks/use-auth";
import { useMemo } from "react";
import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { useCertificates } from "@/hooks/use-certificates";

export type ActivityType =
  | "certificate_created"
  | "certificate_submitted"
  | "certificate_updated"
  | "certificate_verified"
  | "certificate_approved"
  | "certificate_rejected"
  | "certificate_signed"
  | "certificate_delivered"
  | "certificate_expired"
  | "certificate_downloaded";

export interface Activity {
  id: string;
  type: ActivityType;
  timestamp: Date;
  certificateId: string;
  certificateReference: string;
  certificateType: string;
  actor?: {
    id: string;
    name: string;
    role: string;
  };
  metadata?: {
    status?: CERTIFICATE_STATUS;
    reason?: string;
    notes?: string;
  };
}

export function useCitizenActivities(limit?: number) {
  const { user } = useAuth();
  const { certificates, isLoading, error } = useCertificates();

  const activities = useMemo(() => {
    if (!certificates || !user) return null;

    // Filtrer les certificats du citoyen
    const userCertificates = certificates.filter(
      (cert) => cert.citizenId === user.$id
    );

    // Générer les activités à partir des certificats
    const allActivities: Activity[] = userCertificates.flatMap((cert) => {
      const activities: Activity[] = [];

      // Création du certificat
      activities.push({
        id: `${cert.$id}_created`,
        type: "certificate_created",
        timestamp: new Date(cert.$createdAt),
        certificateId: cert.$id,
        certificateReference: cert.reference,
        certificateType: cert.type,
      });

      // Soumission (si pas en brouillon)
      if (cert.status !== CERTIFICATE_STATUS.DRAFT) {
        activities.push({
          id: `${cert.$id}_submitted`,
          type: "certificate_submitted",
          timestamp: new Date(cert.$createdAt),
          certificateId: cert.$id,
          certificateReference: cert.reference,
          certificateType: cert.type,
        });
      }

      // Vérification
      if (cert.status === CERTIFICATE_STATUS.VERIFIED) {
        activities.push({
          id: `${cert.$id}_verified`,
          type: "certificate_verified",
          timestamp: new Date(cert.$updatedAt),
          certificateId: cert.$id,
          certificateReference: cert.reference,
          certificateType: cert.type,
          actor: cert.agentId
            ? {
                id: cert.agentId,
                name: cert.agentName || "Agent",
                role: "agent",
              }
            : undefined,
          metadata: {
            notes: cert.notes,
          },
        });
      }

      // Approbation
      if (cert.status === CERTIFICATE_STATUS.APPROVED) {
        activities.push({
          id: `${cert.$id}_approved`,
          type: "certificate_approved",
          timestamp: new Date(cert.$updatedAt),
          certificateId: cert.$id,
          certificateReference: cert.reference,
          certificateType: cert.type,
          actor: cert.chefId
            ? {
                id: cert.chefId,
                name: cert.chefName || "Chef de quartier",
                role: "chef",
              }
            : undefined,
        });
      }

      // Rejet
      if (cert.status === CERTIFICATE_STATUS.REJECTED) {
        activities.push({
          id: `${cert.$id}_rejected`,
          type: "certificate_rejected",
          timestamp: new Date(cert.$updatedAt),
          certificateId: cert.$id,
          certificateReference: cert.reference,
          certificateType: cert.type,
          actor: cert.chefId
            ? {
                id: cert.chefId,
                name: cert.chefName || "Chef de quartier",
                role: "chef",
              }
            : undefined,
          metadata: {
            reason: cert.rejectionReason ?? undefined,
          },
        });
      }

      // Signature
      if (cert.signedAt) {
        activities.push({
          id: `${cert.$id}_signed`,
          type: "certificate_signed",
          timestamp: new Date(cert.signedAt),
          certificateId: cert.$id,
          certificateReference: cert.reference,
          certificateType: cert.type,
          actor: cert.chefId
            ? {
                id: cert.chefId,
                name: cert.chefName || "Chef de quartier",
                role: "chef",
              }
            : undefined,
        });
      }

      // Délivrance
      if (cert.deliveredAt) {
        activities.push({
          id: `${cert.$id}_delivered`,
          type: "certificate_delivered",
          timestamp: new Date(cert.deliveredAt),
          certificateId: cert.$id,
          certificateReference: cert.reference,
          certificateType: cert.type,
        });
      }

      // Téléchargements
      if (cert.downloads?.length) {
        cert.downloads.forEach((download, index) => {
          activities.push({
            id: `${cert.$id}_download_${index}`,
            type: "certificate_downloaded",
            timestamp: new Date(download), // Supposant que downloads contient des timestamps
            certificateId: cert.$id,
            certificateReference: cert.reference,
            certificateType: cert.type,
          });
        });
      }

      // Expiration
      if (cert.status === CERTIFICATE_STATUS.EXPIRED) {
        activities.push({
          id: `${cert.$id}_expired`,
          type: "certificate_expired",
          timestamp: new Date(cert.$updatedAt),
          certificateId: cert.$id,
          certificateReference: cert.reference,
          certificateType: cert.type,
        });
      }

      return activities;
    });

    // Trier par date décroissante
    const sortedActivities = allActivities.sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
    );

    return limit ? sortedActivities.slice(0, limit) : sortedActivities;
  }, [certificates, user, limit]);

  return {
    activities,
    isLoading,
    error,
  };
}
