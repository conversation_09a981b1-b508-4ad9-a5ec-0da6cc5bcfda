"use server";

import { initializeDatabase } from "./database";
import { initializeStorage } from "./storage";

// Clé pour le localStorage
const INIT_STATUS_KEY = "appwrite_initialized";

// Fonction pour vérifier l'état d'initialisation
export async function checkInitializationStatus(): Promise<boolean> {
  try {
    // Cette fonction sera appelée côté client
    if (typeof window !== 'undefined') {
      return localStorage.getItem(INIT_STATUS_KEY) === 'true';
    }
    return false;
  } catch {
    return false;
  }
}

// Fonction pour marquer comme initialisé
export async function markAsInitialized() {
  if (typeof window !== 'undefined') {
    localStorage.setItem(INIT_STATUS_KEY, 'true');
  }
}

// Fonction pour réinitialiser le statut
export async function resetInitializationStatus() {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(INIT_STATUS_KEY);
  }
}

export async function initializeAppwrite(force: boolean = false) {
  // Si déjà initialisé et pas de force, on skip
  if (!force && await checkInitializationStatus()) {
    console.log("⏭️ Appwrite already initialized, skipping...");
    return;
  }

  console.log("\n🔧 Starting Appwrite initialization...\n");

  try {
    // Initialize database collections
    await initializeDatabase();

    console.log("\n"); // Separator for better readability

    // Initialize storage buckets
    await initializeStorage();

    // Marquer comme initialisé
    await markAsInitialized();

    console.log("\n✨ Appwrite initialization completed successfully");
  } catch (error) {
    // En cas d'erreur, on réinitialise le statut
    await resetInitializationStatus();
    console.error("\n💥 Error during Appwrite initialization:", error);
    throw error;
  }
}
