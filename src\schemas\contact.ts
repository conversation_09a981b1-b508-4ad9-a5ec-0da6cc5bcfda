import { z } from "zod";

export const contactFormSchema = z.object({
  name: z
    .string()
    .min(2, "Le nom doit contenir au moins 2 caractères")
    .max(50, "Le nom ne peut pas dépasser 50 caractères"),
  email: z
    .string()
    .email("Veuillez entrer une adresse email valide")
    .min(5, "L'email doit contenir au moins 5 caractères")
    .max(100, "L'email ne peut pas dépasser 100 caractères"),
  service: z
    .enum(["support", "certificate", "legal", "other"])
    .default("support"),
  subject: z
    .string()
    .min(3, "Le sujet doit contenir au moins 3 caractères")
    .max(100, "Le sujet ne peut pas dépasser 100 caractères"),
  message: z
    .string()
    .min(10, "Le message doit contenir au moins 10 caractères")
    .max(1000, "Le message ne peut pas dépasser 1000 caractères"),
  consent: z
    .boolean()
    .refine(
      (val) => val === true,
      "Vous devez accepter la politique de confidentialité"
    ),
});

export type ContactFormData = z.infer<typeof contactFormSchema>;

export const serviceOptions = [
  {
    value: "support",
    label: "Support Technique",
    description: "Assistance technique et questions générales",
  },
  {
    value: "certificate",
    label: "Certificats",
    description: "Questions relatives aux certificats et documents",
  },
  {
    value: "legal",
    label: "Service Juridique",
    description: "Questions légales et réglementaires",
  },
  {
    value: "other",
    label: "Autre",
    description: "Autres demandes (Veuillez préciser dans le message)",
  },
] as const;
