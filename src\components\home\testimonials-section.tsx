import { motion } from "framer-motion";
import { User } from "lucide-react";

export function TestimonialsSection() {
  const testimonials = [
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Citoyen",
      text: "La plateforme a grandement simplifié mes démarches administratives. Un vrai gain de temps !",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Chef de quartier",
      text: "Un outil moderne qui facilite notre travail quotidien. La gestion des certificats n'a jamais été aussi efficace.",
    },
    {
      name: "<PERSON>",
      role: "Agent administratif",
      text: "Une solution complète qui répond parfaitement aux besoins de digitalisation de nos services.",
    },
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
              className="relative p-6 rounded-2xl bg-white/80 backdrop-blur-sm border border-neutral-200/60 shadow-lg"
            >
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-accent-primary/10 flex items-center justify-center">
                  <User className="w-6 h-6 text-accent-primary" />
                </div>
                <div>
                  <div className="font-semibold text-neutral-900">
                    {testimonial.name}
                  </div>
                  <div className="text-sm text-neutral-500">
                    {testimonial.role}
                  </div>
                </div>
              </div>
              <p className="text-neutral-600">{testimonial.text}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
