import sharp from "sharp";
import path from "path";
import fs from "fs/promises";

const OG_DIR = path.join(process.cwd(), "public", "images");
const LOGO_PATH = path.join(process.cwd(), "public", "logo.png");

const OG_CONFIG = {
  width: 1200,
  height: 630,
  colors: {
    background: {
      from: "#0A1F1C",
      via: "#002B25",
      to: "#004D40"
    },
    glass: {
      fill: "rgba(255, 255, 255, 0.08)",
      stroke: "rgba(255, 255, 255, 0.12)"
    },
    accent: {
      primary: "#00E5BE",
      secondary: "#00C4A3"
    },
    text: {
      title: "#FFFFFF",
      subtitle: "#E0F2F1",
      body: "rgba(255, 255, 255, 0.92)",
      muted: "rgba(255, 255, 255, 0.75)"
    }
  },
  logo: {
    width: 80,
    height: 80,
    position: {
      x: 64,
      y: 64
    }
  }
};

async function generateOGImage() {
  try {
    await fs.mkdir(OG_DIR, { recursive: true });

    const svgImage = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
      <svg width="${OG_CONFIG.width}" height="${OG_CONFIG.height}"
        viewBox="0 0 ${OG_CONFIG.width} ${OG_CONFIG.height}"
        xmlns="http://www.w3.org/2000/svg">
        <defs>
          <!-- Dégradé sophistiqué pour le fond -->
          <linearGradient id="bgGradient" x1="0" y1="0" x2="1" y2="1">
            <stop offset="0%" stop-color="${OG_CONFIG.colors.background.from}" />
            <stop offset="50%" stop-color="${OG_CONFIG.colors.background.via}" />
            <stop offset="100%" stop-color="${OG_CONFIG.colors.background.to}" />
          </linearGradient>

          <!-- Dégradé pour l'accent -->
          <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="1">
            <stop offset="0%" stop-color="${OG_CONFIG.colors.accent.primary}" />
            <stop offset="100%" stop-color="${OG_CONFIG.colors.accent.secondary}" />
          </linearGradient>

          <!-- Effet de flou pour la profondeur -->
          <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur in="SourceGraphic" stdDeviation="50" />
          </filter>

          <!-- Effet de verre -->
          <filter id="glass">
            <feGaussianBlur in="SourceAlpha" stdDeviation="6" result="blur" />
            <feColorMatrix in="blur" type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7" result="goo" />
          </filter>
        </defs>

        <!-- Fond principal avec motif -->
        <rect width="100%" height="100%" fill="url(#bgGradient)" />

        <!-- Formes décoratives avec effet de flou -->
        <circle cx="1100" cy="-50" r="400" fill="url(#accentGradient)"
          opacity="0.15" filter="url(#blur)" />
        <circle cx="100" cy="700" r="350" fill="url(#accentGradient)"
          opacity="0.12" filter="url(#blur)" />

        <!-- Conteneur en verre -->
        <g filter="url(#glass)">
          <rect x="64" y="180" width="1072" height="360" rx="24"
            fill="${OG_CONFIG.colors.glass.fill}"
            stroke="${OG_CONFIG.colors.glass.stroke}"
            stroke-width="1" />
        </g>

        <!-- Accent line avec dégradé -->
        <rect x="64" y="180" width="6" height="360" rx="3"
          fill="url(#accentGradient)" />

        <!-- Textes avec ombres subtiles -->
        <g transform="translate(200, 260)">
          <!-- Label -->
          <text
            font-family="system-ui, -apple-system, sans-serif"
            font-size="24"
            font-weight="600"
            fill="${OG_CONFIG.colors.accent.primary}"
            letter-spacing="0.1em"
            style="filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2))"
          >RÉPUBLIQUE DE GUINÉE</text>

          <!-- Titre principal -->
          <text
            y="65"
            font-family="system-ui, -apple-system, sans-serif"
            font-size="56"
            font-weight="700"
            fill="${OG_CONFIG.colors.text.title}"
            letter-spacing="-0.02em"
            style="filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2))"
          >Numérisation des</text>

          <text
            y="130"
            font-family="system-ui, -apple-system, sans-serif"
            font-size="56"
            font-weight="700"
            fill="${OG_CONFIG.colors.text.title}"
            letter-spacing="-0.02em"
            style="filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2))"
          >Certificats de Résidence</text>

          <!-- Description -->
          <text
            y="190"
            font-family="system-ui, -apple-system, sans-serif"
            font-size="26"
            font-weight="400"
            fill="${OG_CONFIG.colors.text.body}"
            letter-spacing="0"
          >Plateforme officielle de gestion des certificats</text>

          <!-- URL -->
          <text
            y="245"
            font-family="system-ui, -apple-system, sans-serif"
            font-size="20"
            font-weight="500"
            fill="${OG_CONFIG.colors.text.muted}"
          >ncr.ouestech.com</text>
        </g>
      </svg>
    `;

    // Image de base
    const baseImage = sharp(Buffer.from(svgImage));

    // Logo avec ombre
    const logo = await sharp(LOGO_PATH)
      .resize(OG_CONFIG.logo.width, OG_CONFIG.logo.height, {
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 }
      })
      .toBuffer();

    // Composition finale
    await baseImage
      .composite([
        {
          input: logo,
          top: OG_CONFIG.logo.position.y,
          left: OG_CONFIG.logo.position.x,
        }
      ])
      .toFile(path.join(OG_DIR, "og.png"));

    console.log("✨ Image OG moderne générée avec succès!");
  } catch (error) {
    console.error("❌ Erreur lors de la génération de l'image OG:", error);
    process.exit(1);
  }
}

generateOGImage();