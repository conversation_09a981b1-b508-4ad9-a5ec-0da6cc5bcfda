"use client";

import Link from "next/link";
import { LogOut, Settings, User } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/hooks/use-auth";
import { UserAvatar } from "@/components/ui/user-avatar";

export function UserNav() {
  const { user, signOut } = useAuth();

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="relative h-10 w-10 rounded-full overflow-hidden bg-accent-primary hover:bg-accent-secondary ring-2 ring-accent-primary/10 hover:ring-accent-primary/20 transition-all duration-300 shadow-sm hover:shadow-md"
        >
          <UserAvatar
            name={user?.name}
            src={user?.prefs?.avatarUrl}
            className="h-10 w-10"
            size="md"
            solid
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-56 z-50 mt-1 bg-white/95 backdrop-blur-sm border border-accent-primary/10 shadow-lg shadow-accent-primary/10 rounded-xl"
      >
        <div className="flex items-center justify-start gap-2 p-2">
          <div className="flex flex-col space-y-1 leading-none">
            {user?.name && (
              <p className="font-medium text-neutral-900">
                {user.name}
              </p>
            )}
            {user?.email && (
              <p className="w-[200px] truncate text-sm text-neutral-600">
                {user.email}
              </p>
            )}
          </div>
        </div>
        <DropdownMenuSeparator className="bg-accent-primary/5" />
        <DropdownMenuItem asChild>
          <Link
            href="/profile"
            className="cursor-pointer flex items-center px-3 py-2 text-sm hover:bg-accent-primary/5 transition-colors duration-200"
          >
            <User className="mr-2 h-4 w-4 text-accent-primary/70" />
            <span>Profile</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link
            href="/settings"
            className="cursor-pointer flex items-center px-3 py-2 text-sm hover:bg-accent-primary/5 transition-colors duration-200"
          >
            <Settings className="mr-2 h-4 w-4 text-accent-primary/70" />
            <span>Paramètres</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator className="bg-accent-primary/5" />
        <DropdownMenuItem
          onClick={handleSignOut}
          className="cursor-pointer flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 focus:bg-red-50 focus:text-red-600 transition-colors duration-200"
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>Déconnexion</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
