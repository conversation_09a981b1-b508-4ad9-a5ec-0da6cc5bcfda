"use client";

import { Certificate } from "@/actions/types";
import { ActionButton } from "@/components/ui/action-button";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/components/ui/use-toast";
import { PAYMENT_CONFIG } from "@/config/payment";
import { useAuth } from "@/hooks/use-auth";
import { useCertificateActions } from "@/hooks/use-certificate-actions";
import { useCertificateNavigation } from "@/hooks/use-certificate-navigation";
import { useCertificatePermissions } from "@/hooks/use-certificate-permissions";
import { usePaymentFlow } from "@/hooks/use-payment-flow";
import { cn } from "@/lib/utils/cn";
import { TOAST_MESSAGES } from "@/services/toast-messages";
import {
  ArrowLeft,
  Ban,
  Download,
  Eye,
  FileCheck,
  MoreHorizontal,
  Send,
  User<PERSON><PERSON><PERSON>,
  UserX,
} from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { useCallback, useMemo, useState } from "react";
import {
  AssignDialog,
  RejectDialog,
  UnassignDialog,
  VerifyDialog,
} from "./action-dialogs";

interface CertificateActionsProps {
  certificate: Certificate;
  userRole: "chef" | "agent" | "citizen" | "admin";
  alwaysVisible?: boolean;
}

export function CertificateActions({
  certificate,
  userRole,
  alwaysVisible = false,
}: CertificateActionsProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const { navigateBack, navigateToDetails, isDetailsPage } =
    useCertificateNavigation();
  const [openDialog, setOpenDialog] = useState<
    "assign" | "unassign" | "reject" | "verify" | null
  >(null);
  const router = useRouter();
  const pathname = usePathname();
  const isSignPage = pathname?.includes("/sign");

  const {
    assign,
    unassign,
    reject,
    verify,
    deliver,
    download,
    markAsReady,
    isLoading,
  } = useCertificateActions();

  const permissions = useCertificatePermissions(
    certificate,
    userRole,
    user,
    isSignPage
  );

  const { startPaymentFlow, isProcessing: isPaymentProcessing } =
    usePaymentFlow();

  const handleMarkAsReady = useCallback(async () => {
    try {
      await markAsReady(certificate.$id);
      toast(TOAST_MESSAGES.certificate.markAsReady.success);
    } catch (error: any) {
      toast({
        ...TOAST_MESSAGES.certificate.markAsReady.error,
        description:
          error.message ||
          TOAST_MESSAGES.certificate.markAsReady.error.description,
        variant: "error",
      });
    }
  }, [certificate.$id, markAsReady, toast]);

  const handleDeliver = useCallback(async () => {
    try {
      // Show loading toast
      toast({
        title: "Délivrance en cours",
        description:
          "Veuillez patienter pendant la délivrance du certificat...",
      });

      // Call the deliver function with timeout
      await Promise.race([
        deliver(certificate.$id),
        new Promise((_, reject) =>
          setTimeout(
            () =>
              reject(new Error("Timeout: La délivrance prend trop de temps")),
            60000
          )
        ),
      ]);

      // Success toast
      toast({
        title: "Certificat délivré",
        description: "Le certificat a été délivré avec succès.",
      });

      // Navigate to details page
      router.push(`/dashboard/certificates/${certificate.$id}`);
    } catch (error: any) {
      // Error toast
      toast({
        title: "Erreur lors de la délivrance",
        description:
          error?.message || "Une erreur est survenue lors de la délivrance.",
        variant: "destructive",
      });

      // Fallback: navigate to deliver page for manual retry
      router.push(`/dashboard/certificates/${certificate.$id}/deliver`);
    }
  }, [router, certificate.$id, deliver, toast]);

  // Calcul des actions disponibles
  const availableActions = useMemo(() => {
    const actions = [];

    if (userRole === "chef") {
      console.log("permissions.canAssign", permissions.canAssign);
      if (permissions.canAssign) {
        actions.push({
          id: "assign",
          label: "Assigner un agent",
          icon: UserCheck,
          onClick: () => setOpenDialog("assign"),
        });
      }

      if (permissions.canUnassign) {
        actions.push({
          id: "unassign",
          label: "Désassigner l'agent",
          icon: UserX,
          onClick: () => setOpenDialog("unassign"),
        });
      }

      console.log("permissions.canMarkAsReady", permissions.canMarkAsReady);
      if (permissions.canMarkAsReady) {
        actions.push({
          id: "markAsReady",
          label: "Marquer comme prêt",
          icon: FileCheck,
          onClick: handleMarkAsReady,
        });
      }
    }

    if (permissions.canReject) {
      actions.push({
        id: "reject",
        label: "Rejeter",
        icon: Ban,
        onClick: () => setOpenDialog("reject"),
      });
    }

    if (permissions.canVerify) {
      actions.push({
        id: "verify",
        label: "Vérifier",
        icon: FileCheck,
        onClick: () => setOpenDialog("verify"),
      });
    }

    if (permissions.canDeliver) {
      actions.push({
        id: "deliver",
        label: "Délivrer",
        icon: Send,
        onClick: handleDeliver,
      });
    }

    return actions;
  }, [permissions, userRole, handleMarkAsReady, handleDeliver]);

  const handleAssign = async (data: { agentId: string }) => {
    try {
      await assign({ certificateId: certificate.$id, agentId: data.agentId });
      toast(TOAST_MESSAGES.certificate.assign.success);
    } catch (error: any) {
      toast({
        ...TOAST_MESSAGES.certificate.assign.error,
        description:
          error.message || TOAST_MESSAGES.certificate.assign.error.description,
        variant: "error",
      });
    }
    setOpenDialog(null);
  };

  const handleUnassign = async () => {
    try {
      await unassign(certificate.$id);
      toast(TOAST_MESSAGES.certificate.unassign.success);
    } catch (error: any) {
      toast({
        ...TOAST_MESSAGES.certificate.unassign.error,
        description:
          error.message ||
          TOAST_MESSAGES.certificate.unassign.error.description,
        variant: "error",
      });
    }
    setOpenDialog(null);
  };

  const handleReject = async (data: { reason: string }) => {
    try {
      await reject({ certificateId: certificate.$id, reason: data.reason });
      toast(TOAST_MESSAGES.certificate.reject.success);
    } catch (error: any) {
      toast({
        ...TOAST_MESSAGES.certificate.reject.error,
        description:
          error.message || TOAST_MESSAGES.certificate.reject.error.description,
        variant: "error",
      });
    }
    setOpenDialog(null);
  };

  const handleVerify = async (data: { notes?: string }) => {
    try {
      await verify({ certificateId: certificate.$id, notes: data.notes });
      toast(TOAST_MESSAGES.certificate.verify.success);
    } catch (error: any) {
      toast({
        ...TOAST_MESSAGES.certificate.verify.error,
        description:
          error.message || TOAST_MESSAGES.certificate.verify.error.description,
        variant: "error",
      });
    }
    setOpenDialog(null);
  };

  const handleDownload = async () => {
    try {
      if (!certificate.isPaid) {
        // Démarrer le processus de paiement
        await startPaymentFlow({
          amount: certificate.price || 10000, // Prix par défaut si non défini
          certificateId: certificate.$id,
          returnUrl: `/dashboard/certificates/${certificate.$id}?action=download`,
        });
        return;
      }

      // Si déjà payé, procéder au téléchargement
      const response = await download(certificate.$id);

      if (response.success && response.documentUrl) {
        const link = document.createElement("a");
        link.target = "_blank";
        link.href = response.documentUrl;
        link.download = `certificat-${certificate.reference}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast(TOAST_MESSAGES.certificate.download.success);
      }
    } catch (error: any) {
      toast({
        ...TOAST_MESSAGES.certificate.download.error,
        description:
          error.message ||
          TOAST_MESSAGES.certificate.download.error.description,
        variant: "error",
      });
    }
  };

  return (
    <>
      <div
        className={cn(
          "flex items-center gap-2 transition-all duration-200",
          !alwaysVisible &&
            !isDetailsPage() &&
            "opacity-0 group-hover/row:opacity-100"
        )}
      >
        {isDetailsPage() ? (
          <ActionButton
            size="sm"
            variant="outline"
            icon={ArrowLeft}
            onClick={() => navigateBack()}
            className="bg-white/50 backdrop-blur-sm hover:bg-white/80"
          >
            Retour à la liste
          </ActionButton>
        ) : (
          <ActionButton
            size="sm"
            variant="outline"
            icon={Eye}
            onClick={() => navigateToDetails(certificate.$id)}
            className="bg-white/50 backdrop-blur-sm hover:bg-white/80"
          >
            Détails
          </ActionButton>
        )}

        {permissions.canDownload && (
          <ActionButton
            size="sm"
            variant={certificate.isPaid ? "default" : "outline"}
            icon={Download}
            onClick={handleDownload}
            isLoading={isLoading || isPaymentProcessing}
            loadingText={
              isPaymentProcessing ? "Redirection..." : "Téléchargement..."
            }
            className={cn(
              "transition-all duration-200",
              !certificate.isPaid &&
                "text-muted-foreground hover:text-foreground",
              certificate.isPaid &&
                "bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white"
            )}
          >
            {certificate.isPaid
              ? "Télécharger"
              : `Payer ${
                  certificate.price
                    ? (certificate.price / 100).toFixed(2)
                    : "10000,00"
                } ${PAYMENT_CONFIG.orangeMoney.currency}`}
          </ActionButton>
        )}

        {/* N'affiche le menu que s'il y a des actions disponibles */}
        {availableActions.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="bg-white/50 backdrop-blur-sm"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-[200px] bg-white/90 backdrop-blur-sm"
            >
              {availableActions.map((action) => (
                <DropdownMenuItem
                  key={action.id}
                  onClick={action.onClick}
                  disabled={isLoading}
                >
                  <action.icon className="h-4 w-4 mr-2" />
                  {action.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* Dialogs */}
      <AssignDialog
        open={openDialog === "assign"}
        onOpenChange={(open) => !open && setOpenDialog(null)}
        onSubmit={handleAssign}
        isLoading={isLoading}
      />

      <UnassignDialog
        open={openDialog === "unassign"}
        onOpenChange={(open) => !open && setOpenDialog(null)}
        onConfirm={handleUnassign}
        isLoading={isLoading}
      />

      <RejectDialog
        open={openDialog === "reject"}
        onOpenChange={(open) => !open && setOpenDialog(null)}
        onSubmit={handleReject}
        isLoading={isLoading}
      />

      <VerifyDialog
        open={openDialog === "verify"}
        onOpenChange={(open) => !open && setOpenDialog(null)}
        onSubmit={handleVerify}
        isLoading={isLoading}
      />
    </>
  );
}
