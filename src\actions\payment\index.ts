"use server";

import {
  PaymentMetadata,
  PaymentProvider,
  PaymentStatus,
} from "@/config/payment";
import { createAdminClient } from "@/lib/server/appwrite";
import {
  CERTIFICATES_COLLECTION_ID,
  DATABASE_ID,
  PAYMENTS_COLLECTION_ID,
  PAYMENTS_METADATA_COLLECTION_ID,
} from "@/lib/server/database";
import { generateReference } from "@/lib/utils/reference";
import { OrangeMoneyService } from "@/services/payment/orange-money";
import { ID, Query } from "node-appwrite";
import { getCurrentUser } from "../auth/session";

export async function initiatePayment(params: {
  amount: number;
  provider: PaymentProvider;
  metadata: PaymentMetadata;
}) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Générer l'ID de commande unique
    const orderId = generateReference("PAY");

    // Créer la transaction dans la base de données
    const transaction = await databases.createDocument(
      DATABASE_ID,
      PAYMENTS_COLLECTION_ID,
      ID.unique(),
      {
        orderId,
        userId: user.$id,
        amount: params.amount,
        provider: params.provider,
        status: PaymentStatus.INITIATED,
        metadata: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    );

    // Initier le paiement selon le provider
    if (params.provider === "orange-money") {
      const orangeMoneyService = OrangeMoneyService.getInstance();
      const paymentResult = await orangeMoneyService.initiatePayment({
        orderId,
        amount: params.amount,
        reference: transaction.$id,
        metadata: params.metadata,
      });

      // Mettre à jour la transaction avec les informations de paiement
      await databases.updateDocument(
        DATABASE_ID,
        PAYMENTS_COLLECTION_ID,
        transaction.$id,
        {
          paymentUrl: paymentResult.paymentUrl,
          payToken: paymentResult.payToken,
          status: PaymentStatus.PENDING,
          updatedAt: new Date().toISOString(),
        }
      );

      // Mettre à jour le certificat avec les informations de paiement
      await databases.updateDocument(
        DATABASE_ID,
        CERTIFICATES_COLLECTION_ID,
        params.metadata.certificateId,
        {
          paymentStatus: PaymentStatus.PENDING,
          paymentId: transaction.$id,
          updatedAt: new Date().toISOString(),
        }
      );

      return {
        success: true,
        paymentUrl: paymentResult.paymentUrl,
        transaction: {
          id: transaction.$id,
          orderId,
          status: PaymentStatus.PENDING,
        },
      };
    }

    throw new Error("Provider de paiement non supporté");
  } catch (error) {
    console.error("Erreur lors de l'initiation du paiement:", error);
    throw error;
  }
}

export async function verifyPayment(transactionId: string): Promise<{
  success: boolean;
  status: PaymentStatus;
  returnUrl: string;
}> {
  try {
    const { databases } = await createAdminClient();

    // Récupérer la transaction et ses metadata
    const transaction = await databases.getDocument(
      DATABASE_ID,
      PAYMENTS_COLLECTION_ID,
      transactionId
    );

    // Récupérer les metadata associées
    const metadataQuery = await databases.listDocuments(
      DATABASE_ID,
      PAYMENTS_METADATA_COLLECTION_ID,
      [Query.equal("orderId", transaction.orderId), Query.limit(1)]
    );

    const metadata = metadataQuery.documents[0];
    if (!metadata?.certificateId) {
      throw new Error(
        "Impossible de trouver le certificat associé au paiement"
      );
    }

    if (transaction.provider === "orange-money") {
      const orangeMoneyService = OrangeMoneyService.getInstance();
      const statusResult = await orangeMoneyService.checkTransactionStatus({
        orderId: transaction.orderId,
        amount: transaction.amount,
        payToken: transaction.payToken,
      });

      // Mettre à jour le statut de la transaction
      let status = PaymentStatus.PENDING;
      switch (statusResult.status.toUpperCase()) {
        case "SUCCESS":
          status = PaymentStatus.SUCCESS;
          break;
        case "FAILED":
          status = PaymentStatus.FAILED;
          break;
        case "INITIATED":
          status = PaymentStatus.INITIATED;
          break;
        case "EXPIRED":
          status = PaymentStatus.EXPIRED;
          break;
      }

      // Mettre à jour la transaction
      await databases.updateDocument(
        DATABASE_ID,
        PAYMENTS_COLLECTION_ID,
        transactionId,
        {
          status,
          updatedAt: new Date().toISOString(),
          ...(status === PaymentStatus.SUCCESS && {
            completedAt: new Date().toISOString(),
          }),
        }
      );

      // Mettre à jour le certificat si le paiement est complété
      if (status === PaymentStatus.SUCCESS) {
        await databases.updateDocument(
          DATABASE_ID,
          CERTIFICATES_COLLECTION_ID,
          metadata.certificateId,
          {
            isPaid: true,
            paymentStatus: status,
            paymentId: transaction.$id,
            price: parseInt(transaction.amount),
            paidAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        );

        // Créer la distribution des revenus
        try {
          const { RevenueDistributionService } = await import(
            "@/services/revenue/distribution"
          );
          const revenueService = RevenueDistributionService.getInstance();
          await revenueService.createDistribution({
            paymentId: transaction.$id,
            orderId: transaction.orderId,
            totalAmount: parseInt(transaction.amount),
          });

          console.log(
            `Revenue distribution created for payment ${transaction.$id}`
          );
        } catch (error) {
          console.error("Error creating revenue distribution:", error);
          // Ne pas faire échouer le paiement pour cette erreur
        }
      }

      return {
        success: true,
        status,
        returnUrl: metadata.returnUrl,
      };
    }

    throw new Error("Provider de paiement non supporté");
  } catch (error) {
    console.error("Erreur lors de la vérification du paiement:", error);
    throw error;
  }
}

/**
 * Traiter le succès d'un paiement et distribuer les revenus
 */
export async function processPaymentSuccess(params: {
  orderId: string;
  transactionId?: string;
}) {
  try {
    const { databases } = await createAdminClient();

    // Récupérer le paiement
    const paymentsQuery = await databases.listDocuments(
      DATABASE_ID,
      PAYMENTS_COLLECTION_ID,
      [Query.equal("orderId", params.orderId), Query.limit(1)]
    );

    if (paymentsQuery.documents.length === 0) {
      throw new Error("Paiement non trouvé");
    }

    const payment = paymentsQuery.documents[0];

    // Récupérer les métadonnées du paiement
    const metadataQuery = await databases.listDocuments(
      DATABASE_ID,
      PAYMENTS_METADATA_COLLECTION_ID,
      [Query.equal("orderId", params.orderId), Query.limit(1)]
    );

    if (metadataQuery.documents.length === 0) {
      throw new Error("Métadonnées du paiement non trouvées");
    }

    const metadata = metadataQuery.documents[0];

    // Mettre à jour le statut du paiement
    await databases.updateDocument(
      DATABASE_ID,
      PAYMENTS_COLLECTION_ID,
      payment.$id,
      {
        status: "SUCCESS",
        completedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...(params.transactionId && { transactionId: params.transactionId }),
      }
    );

    // Mettre à jour le certificat
    if (metadata.certificateId) {
      await databases.updateDocument(
        DATABASE_ID,
        CERTIFICATES_COLLECTION_ID,
        metadata.certificateId,
        {
          isPaid: true,
          paymentStatus: "SUCCESS",
          paymentId: payment.$id,
          price: parseInt(payment.amount),
          paidAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );

      // Créer la distribution des revenus
      const { RevenueDistributionService } = await import(
        "@/services/revenue/distribution"
      );
      const revenueService = RevenueDistributionService.getInstance();
      await revenueService.createDistribution({
        paymentId: payment.$id,
        orderId: params.orderId,
        totalAmount: parseInt(payment.amount),
      });

      console.log(
        `Payment processed successfully for certificate ${metadata.certificateId}`
      );
    }

    return {
      success: true,
      message: "Paiement traité avec succès",
      certificateId: metadata.certificateId,
    };
  } catch (error: any) {
    console.error("Erreur lors du traitement du paiement:", error);
    throw error;
  }
}

/**
 * Relancer un paiement échoué
 */
export async function retryFailedPayment(certificateId: string) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupérer le certificat
    const certificate = await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    );

    // Vérifier la propriété
    if (certificate.citizenId !== user.$id) {
      throw new Error("Vous n'êtes pas autorisé à relancer ce paiement");
    }

    // Vérifier que le paiement peut être relancé
    if (certificate.isPaid) {
      throw new Error("Ce certificat est déjà payé");
    }

    if (!certificate.price) {
      throw new Error("Aucun montant défini pour ce certificat");
    }

    // Utiliser l'action de paiement existante
    const paymentResult = await initiatePayment({
      amount: certificate.price,
      provider: "orange-money",
      metadata: {
        certificateId: certificateId,
        type: "certificate_request_retry",
        returnUrl: `/payment/success?certificateId=${certificateId}`,
      },
    });

    return paymentResult;
  } catch (error: any) {
    console.error("Erreur lors de la relance du paiement:", error);
    throw error;
  }
}
