"use client";

import { FloatingShapes } from "@/components/about/floating-shapes";
import { AnimatedFeature } from "@/components/about/animated-feature";
import { StatsCard } from "@/components/about/stats-card";
import { TeamMember } from "@/components/about/team-member";
import { SectionTitle } from "@/components/about/section-title";
import { CompanySpotlight } from "@/components/about/company-spotlight";
import { motion } from "framer-motion";

const features = [
  {
    title: "Innovation",
    description: "Modernisation des services administratifs grâce au numérique",
    icon: "💡",
  },
  {
    title: "Sécurité",
    description: "Protection des données et authentification sécurisée",
    icon: "🔒",
  },
  {
    title: "Accessibilité",
    description: "Services disponibles 24/7 depuis n'importe où",
    icon: "🌍",
  },
];

const stats = [
  { value: "50K+", label: "Certificats Délivrés" },
  { value: "98%", label: "Taux de Satisfaction" },
  { value: "24/7", label: "Disponibilité" },
  { value: "<2h", label: "Temps de Traitement" },
];

const team = [
  {
    name: "Dr. <PERSON><PERSON>",
    role: "Directeur du <PERSON>jet",
    imageSrc: "/images/team/director.jpg",
    socialLinks: {
      linkedin: "https://linkedin.com",
      twitter: "https://twitter.com",
    },
  },
  {
    name: "Aïssatou Barry",
    role: "Responsable Innovation",
    imageSrc: "/images/team/innovation-lead.jpg",
    socialLinks: {
      linkedin: "https://linkedin.com",
    },
  },
  {
    name: "Ibrahim Camara",
    role: "Chef de la Sécurité",
    imageSrc: "/images/team/security-lead.jpg",
    socialLinks: {
      linkedin: "https://linkedin.com",
      twitter: "https://twitter.com",
    },
  },
];

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50/50 overflow-hidden">
      {/* Hero Section */}
      <section className="relative pt-24 lg:pt-32 pb-16">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-20">
            <motion.h1
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-[#004D40] via-[#00796B] to-[#009688] bg-clip-text text-transparent mb-6"
            >
              À Propos de NCR
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-neutral-600 text-lg md:text-xl max-w-2xl mx-auto"
            >
              NCR (Numérisation des Certificats de Résidence) représente
              l'avenir de l'administration guinéenne, alliant innovation
              technologique et service public.
            </motion.p>
          </div>

          {/* Section principale avec éléments visuels */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto mb-24">
            <FloatingShapes />

            <div className="space-y-8">
              <div className="prose prose-green">
                <h2 className="text-2xl font-semibold text-[#004D40] mb-4">
                  Notre Mission
                </h2>
                <p className="text-neutral-600">
                  NCR s'engage à moderniser les services administratifs de la
                  Guinée en offrant une plateforme numérique innovante pour la
                  gestion des certificats de résidence. Notre objectif est de
                  simplifier les démarches administratives tout en garantissant
                  la sécurité et l'authenticité des documents.
                </p>
              </div>

              <div className="space-y-6">
                {features.map((feature, index) => (
                  <AnimatedFeature
                    key={index}
                    icon={feature.icon}
                    title={feature.title}
                    description={feature.description}
                    delay={0.6 + index * 0.1}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section Ouestech */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6">
          <SectionTitle
            title="Propulsé par Ouestech"
            subtitle="Une solution conçue par des experts de la transformation digitale"
            centered
            delay={0.1}
          />
          <div className="max-w-6xl mx-auto">
            <CompanySpotlight delay={0.3} />
          </div>
        </div>
      </section>

      {/* Statistiques Section */}
      <section className="py-16 bg-neutral-50">
        <div className="container mx-auto px-4 sm:px-6">
          <SectionTitle
            title="NCR en Chiffres"
            subtitle="Des résultats qui parlent d'eux-mêmes"
            centered
          />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            {stats.map((stat, index) => (
              <StatsCard
                key={index}
                value={stat.value}
                label={stat.label}
                delay={0.2 + index * 0.1}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Notre Équipe Section */}
      {/* <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6">
          <SectionTitle
            title="Notre Équipe"
            subtitle="Des experts dévoués à la transformation numérique de l'administration"
            centered
          />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {team.map((member, index) => (
              <TeamMember
                key={index}
                name={member.name}
                role={member.role}
                imageSrc={member.imageSrc}
                socialLinks={member.socialLinks}
                delay={0.2 + index * 0.1}
              />
            ))}
          </div>
        </div>
      </section> */}

      {/* Contact Section */}
      <section className="py-16 bg-neutral-50">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-3xl mx-auto text-center">
            <SectionTitle
              title="Travaillons Ensemble"
              subtitle="Vous avez des questions ou des suggestions ? N'hésitez pas à nous contacter."
              centered
            />
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <a
                href="/contact"
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-[#004D40] hover:bg-[#00695C] transition-colors duration-300"
              >
                Contactez-nous
              </a>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}
