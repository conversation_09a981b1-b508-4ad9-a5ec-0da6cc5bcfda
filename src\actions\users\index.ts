'use server';

import { createAdminClient } from "@/lib/server/appwrite";
import { ID, Query, Models } from "node-appwrite";
import {
  DATABASE_ID,
  CITIZENS_COLLECTION_ID,
  CHEFS_COLLECTION_ID,
  AGENTS_COLLECTION_ID,
  ADMINS_COLLECTION_ID
} from "@/lib/server/database";
import { UserDetails, UsersFilters } from "@/types/user";
import { ROLES, STATUS } from "@/actions/auth/constants";
import { getCollectionByRole } from "@/lib/utils/get-collection-by-role";

// Récupère les utilisateurs d'Appwrite et les détails associés
async function getUsersWithDetails(appwriteUsers: Models.User<Models.Preferences>[], filters: UsersFilters) {
  const { databases } = await createAdminClient();
  const detailedUsers: UserDetails[] = [];

  for (const user of appwriteUsers) {
    try {
      // Recherche dans la collection appropriée selon le rôle
      const collectionId = filters.role ? getCollectionByRole(filters.role) : null;

      // Si un rôle est spécifié, chercher uniquement dans cette collection
      if (collectionId) {
        const { documents } = await databases.listDocuments(
          DATABASE_ID,
          collectionId,
          [
            Query.equal("userId", user.$id),
            ...(filters.status ? [Query.equal("status", filters.status)] : []),
          ]
        );

        if (documents.length > 0) {
          const details = documents[0];
          detailedUsers.push({
            $id: user.$id,
            name: user.name,
            email: user.email,
            avatarUrl: user.prefs?.avatarUrl,
            role: details.role,
            status: details.status,
            district: { name: details.quartier, id: user.$id },
            createdAt: new Date(user.$createdAt),
            lastLoginAt: new Date(user.accessedAt || user.$createdAt),
            phoneNumber: details.phoneNumber,
            documents: details.documents,
          });
        }
      } else {
        // Si aucun rôle spécifié, chercher dans toutes les collections
        const collections = [
          ADMINS_COLLECTION_ID,
          CHEFS_COLLECTION_ID,
          AGENTS_COLLECTION_ID,
          CITIZENS_COLLECTION_ID
        ];

        for (const collection of collections) {
          const { documents } = await databases.listDocuments(
            DATABASE_ID,
            collection,
            [
              Query.equal("userId", user.$id),
              ...(filters.status ? [Query.equal("status", filters.status)] : []),
            ]
          );

          if (documents.length > 0) {
            const details = documents[0];
            detailedUsers.push({
              $id: user.$id,
              name: user.name,
              email: user.email,
              avatarUrl: user.prefs?.avatarUrl,
              role: details.role,
              status: details.status,
              district: { name: details.quartier, id: user.$id },
              createdAt: new Date(user.$createdAt),
              lastLoginAt: new Date(user.accessedAt || user.$createdAt),
              phoneNumber: details.phoneNumber,
              documents: details.documents,
            });
            break;
          }
        }
      }
    } catch (error) {
      console.error(`Erreur lors de la récupération des détails pour l'utilisateur ${user.$id}:`, error);
    }
  }

  return detailedUsers;
}

export async function getUsers(filters: UsersFilters = {}) {
  const { users } = await createAdminClient();

  // Construction des requêtes de base
  const queries = [];

  // Recherche par nom dans les utilisateurs Appwrite
  if (filters.search) {
    queries.push(Query.search("name", filters.search));
  }

  // Pagination
  if (filters.limit) {
    queries.push(Query.limit(filters.limit));
  }

  if (filters.page) {
    const offset = filters.page * (filters.limit || 10);
    queries.push(Query.offset(offset));
  }

  // Récupération des utilisateurs
  const { total, users: appwriteUsers } = await users.list(queries);

  // Enrichissement et filtrage
  const enrichedUsers = await getUsersWithDetails(appwriteUsers, filters);

  // Filtrage supplémentaire
  const filteredUsers = enrichedUsers.filter(user => {
    if (filters.district && user.district?.id !== filters.district) {
      return false;
    }
    return true;
  });

  return {
    users: filteredUsers,
    pagination: {
      total,
      page: filters.page || 0,
      limit: filters.limit || 10,
    },
  };
}

export async function updateUserStatus(userId: string, status: STATUS) {
  const { databases, users } = await createAdminClient();

  try {
    // 1. Mise à jour du statut dans Appwrite Users
    switch (status) {
      case STATUS.ACTIVE:
        // Activer le compte et marquer l'email comme vérifié
        await users.updateEmailVerification(userId, true);
        await users.updateStatus(userId, true);
        break;
      case STATUS.BLOCKED:
        // Bloquer le compte
        await users.updateStatus(userId, false);
        break;
      case STATUS.PENDING:
        // Réinitialiser la vérification d'email
        await users.updateEmailVerification(userId, false);
        await users.updateStatus(userId, true);
        break;
    }

    // 2. Mise à jour dans la collection appropriée
    const collections = [
			ADMINS_COLLECTION_ID,
			CHEFS_COLLECTION_ID,
			AGENTS_COLLECTION_ID,
			CITIZENS_COLLECTION_ID,
		];

    for (const collectionId of collections) {
      try {
        const { documents } = await databases.listDocuments(
          DATABASE_ID,
          collectionId,
          [Query.equal("userId", userId)]
        );

        if (documents.length > 0) {
          await databases.updateDocument(
            DATABASE_ID,
            collectionId,
            documents[0].$id,
            {
              status,
              updatedAt: new Date().toISOString(),
            }
          );
          break;
        }
      } catch (error) {
        console.error(`Erreur lors de la mise à jour du statut dans la collection ${collectionId}:`, error);
      }
    }

    // 3. Mise à jour des préférences utilisateur
    await users.updatePrefs(userId, {
      status,
      lastStatusUpdate: new Date().toISOString(),
    });

    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la mise à jour du statut:", error);
    throw error;
  }
}

interface CreateUserData {
  email: string;
  password: string;
  name: string;
  role: ROLES;
  phoneNumber?: string;
  district?: {
    id: string;
    name: string;
  };
}

export async function createUser(data: CreateUserData) {
  const { users, databases } = await createAdminClient();

  try {
    // 1. Créer l'utilisateur dans Appwrite
    const newUser = await users.create(
      ID.unique(),
      data.email,
      data.password,
      data.name
    );

    // 2. Définir les préférences initiales
    await users.updatePrefs(newUser.$id, {
      role: data.role,
      status: STATUS.PENDING,
      avatarUrl: null,
    });

    // 3. Créer le document dans la collection appropriée
    const collectionId = getCollectionByRole(data.role);
    if (!collectionId) {
      throw new Error("Rôle invalide");
    }

    await databases.createDocument(
      DATABASE_ID,
      collectionId,
      ID.unique(),
      {
        userId: newUser.$id,
        role: data.role,
        status: STATUS.PENDING,
        phoneNumber: data.phoneNumber,
        district: data.district,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    );

    return {
      success: true,
      user: {
        $id: newUser.$id,
        name: newUser.name,
        email: newUser.email,
        role: data.role,
        status: STATUS.PENDING,
        district: data.district,
        phoneNumber: data.phoneNumber,
        createdAt: new Date(newUser.$createdAt),
        lastLoginAt: new Date(newUser.$createdAt),
      },
    };
  } catch (error) {
    console.error("Erreur lors de la création de l'utilisateur:", error);
    throw error;
  }
}

// Autres fonctions d'interaction avec les utilisateurs...