import { CertificateVerificationService } from "@/lib/database/certificate-verification";
import { CertificateCrypto } from "@/lib/utils/certificate-crypto";
import { QRCodeGenerator } from "@/lib/utils/qr-generator";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { PdfGeneratorV2Enhanced } from "../pdf-generator-v2-enhanced";

// Mock des dépendances
vi.mock("@/actions/certificates", () => ({
  getCertificateWithFullData: vi.fn(),
}));

vi.mock("@/lib/server/appwrite", () => ({
  createAdminClient: vi.fn(),
}));

vi.mock("@/lib/utils/certificate-crypto");
vi.mock("@/lib/utils/qr-generator");
vi.mock("@/lib/database/certificate-verification");

// Mock data pour les tests
const mockCertificate = {
  $id: "test-cert-id",
  reference: "NCR-CON-MATAM-20241201-12345",
  citizenId: "test-citizen-id",
  chefId: "test-chef-id",
  status: "APPROVED",
  motif: "Demande d'emploi",
  createdAt: "2024-01-10T10:00:00.000Z",
  deliveredAt: "2024-01-15T14:30:00.000Z",
  signatureFileId: null,
  citizen: {
    $id: "citizen-id",
    userId: "user-id",
    nom: "Diallo",
    prenom: "Mamadou",
    dateNaissance: "1990-05-15T00:00:00.000Z",
    lieuNaissance: "Conakry",
    nomPere: "Ibrahima Diallo",
    nomMere: "Fatoumata Camara",
    nationalite: "Guinéenne",
    profession: "Ingénieur",
    telephone: "+224 123 456 789",
    email: "<EMAIL>",
    carteElecteur: "CE123456789",
    adressePrecise: "Quartier Matam, Rue KA-001",
    dateInstallation: "2020-01-01T00:00:00.000Z",
    numeroBatiment: "B-15",
    proprietaireBatiment: "Propriétaire",
    quartier: "Matam",
    numeroIdentificationUnique: "19900515-20240110-A1B2C3D4",
    status: "APPROVED",
    role: "citizen",
  },
  citizenName: "Mamadou Diallo",
  chefQuartier: {
    $id: "chef-id",
    nom: "Touré",
    prenom: "Alpha",
  },
  chefQuartierName: "Alpha Touré",
  quartier: {
    $id: "quartier-id",
    nom: "Matam",
    commune: "Matam",
    region: "Conakry",
    chefId: "test-chef-id",
  },
};

const mockSecurityData = {
  verificationHash: "a1b2c3d4e5f6789012345678",
  displayHash: "A1B2C3D4",
  watermark: "19900515-NCR-MAT-2024",
  timestamp: {
    timestamp: 1704967200000,
    timestampHash: "abc123def456",
    readableDate: "2024-01-11T10:00:00.000Z",
  },
  verificationUrl: "https://ncr.ouestech.com/verify/a1b2c3d4e5f6789012345678",
  salt: "randomsalt123",
  signature: "signature123",
};

describe("PdfGeneratorV2Enhanced", () => {
  beforeEach(() => {
    // Reset des mocks avant chaque test
    vi.clearAllMocks();

    // Mock de getCertificateWithFullData
    const { getCertificateWithFullData } = require("@/actions/certificates");
    getCertificateWithFullData.mockResolvedValue({
      certificate: mockCertificate,
    });

    // Mock de createAdminClient
    const { createAdminClient } = require("@/lib/server/appwrite");
    createAdminClient.mockResolvedValue({
      storage: {
        getFileDownload: vi.fn().mockResolvedValue(new ArrayBuffer(0)),
      },
    });

    // Mock de fetch pour les images
    global.fetch = vi.fn().mockResolvedValue({
      arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
    });

    // Mock des services de sécurité
    vi.mocked(CertificateCrypto.generateCertificateSecurity).mockReturnValue(
      mockSecurityData
    );
    vi.mocked(QRCodeGenerator.generateCertificateQR).mockResolvedValue(
      "base64qrcode"
    );
    vi.mocked(
      CertificateVerificationService.createVerification
    ).mockResolvedValue({
      $id: "verification-id",
      hash: mockSecurityData.verificationHash,
      certificateId: mockCertificate.$id,
      citizenId: mockCertificate.citizenId,
      issuedAt: new Date().toISOString(),
      expiresAt: new Date(
        Date.now() + 3 * 30 * 24 * 60 * 60 * 1000
      ).toISOString(),
      isValid: true,
      isRevoked: false,
      verificationCount: 0,
      metadataId: "metadata-id",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        $id: "metadata-id",
        verificationId: "verification-id",
        issuerType: "chef",
        issuerId: mockCertificate.chefId,
        issuerName: mockCertificate.chefQuartierName || "",
        region: mockCertificate.quartier?.region || "",
        commune: mockCertificate.quartier?.commune || "",
        quartier: mockCertificate.quartier?.nom || "",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    });
  });

  describe("generateCertificatePdf", () => {
    it("should generate a PDF buffer with security features", async () => {
      const result = await PdfGeneratorV2Enhanced.generateCertificatePdf(
        "test-cert-id"
      );

      expect(result).toBeInstanceOf(Buffer);
      expect(result.length).toBeGreaterThan(0);
    });

    it("should generate security data", async () => {
      await PdfGeneratorV2Enhanced.generateCertificatePdf("test-cert-id");

      expect(
        CertificateCrypto.generateCertificateSecurity
      ).toHaveBeenCalledWith({
        certificateId: mockCertificate.$id,
        citizenId: mockCertificate.citizenId,
        citizenIdUnique: mockCertificate.citizen.numeroIdentificationUnique,
        timestamp: expect.any(Number),
        reference: mockCertificate.reference,
        issuerInfo: {
          type: "chef",
          id: mockCertificate.chefId,
          name: mockCertificate.chefQuartierName,
        },
        locationInfo: {
          region: mockCertificate.quartier?.region,
          commune: mockCertificate.quartier?.commune,
          quartier: mockCertificate.quartier?.nom,
        },
      });
    });

    it("should create verification record in database", async () => {
      await PdfGeneratorV2Enhanced.generateCertificatePdf("test-cert-id");

      expect(
        CertificateVerificationService.createVerification
      ).toHaveBeenCalledWith({
        hash: mockSecurityData.verificationHash,
        certificateId: mockCertificate.$id,
        citizenId: mockCertificate.citizenId,
        issuedAt: expect.any(Date),
        expiresAt: expect.any(Date),
        metadata: {
          issuerType: "chef",
          issuerId: mockCertificate.chefId,
          issuerName: mockCertificate.chefQuartierName,
          region: mockCertificate.quartier?.region,
          commune: mockCertificate.quartier?.commune,
          quartier: mockCertificate.quartier?.nom,
        },
      });
    });

    it("should generate QR code", async () => {
      await PdfGeneratorV2Enhanced.generateCertificatePdf("test-cert-id");

      expect(QRCodeGenerator.generateCertificateQR).toHaveBeenCalledWith(
        mockSecurityData.verificationUrl,
        mockSecurityData.verificationHash
      );
    });

    it("should handle missing signature gracefully", async () => {
      const certificateWithoutSignature = {
        ...mockCertificate,
        signatureFileId: null,
      };

      const { getCertificateWithFullData } = require("@/actions/certificates");
      getCertificateWithFullData.mockResolvedValue({
        certificate: certificateWithoutSignature,
      });

      const result = await PdfGeneratorV2Enhanced.generateCertificatePdf(
        "test-cert-id"
      );

      expect(result).toBeInstanceOf(Buffer);
      expect(result.length).toBeGreaterThan(0);
    });

    it("should handle QR code generation errors gracefully", async () => {
      vi.mocked(QRCodeGenerator.generateCertificateQR).mockRejectedValue(
        new Error("QR generation failed")
      );

      const result = await PdfGeneratorV2Enhanced.generateCertificatePdf(
        "test-cert-id"
      );

      // Le PDF devrait quand même être généré même sans QR code
      expect(result).toBeInstanceOf(Buffer);
      expect(result.length).toBeGreaterThan(0);
    });

    it("should handle verification service errors gracefully", async () => {
      vi.mocked(
        CertificateVerificationService.createVerification
      ).mockRejectedValue(new Error("Database error"));

      const result = await PdfGeneratorV2Enhanced.generateCertificatePdf(
        "test-cert-id"
      );

      // Le PDF devrait quand même être généré même en cas d'erreur de base de données
      expect(result).toBeInstanceOf(Buffer);
      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe("Security features", () => {
    it("should include all security elements", async () => {
      const result = await PdfGeneratorV2Enhanced.generateCertificatePdf(
        "test-cert-id"
      );

      const pdfString = result.toString();

      // Vérifier la présence d'éléments de sécurité
      expect(pdfString).toContain("SÉCURISÉ");
      expect(pdfString).toContain("GUINÉE");
      expect(pdfString).toContain("2024");
    });

    it("should include verification hash in PDF", async () => {
      const result = await PdfGeneratorV2Enhanced.generateCertificatePdf(
        "test-cert-id"
      );

      const pdfString = result.toString();
      expect(pdfString).toContain(mockSecurityData.displayHash);
    });

    it("should include timestamp hash in PDF", async () => {
      const result = await PdfGeneratorV2Enhanced.generateCertificatePdf(
        "test-cert-id"
      );

      const pdfString = result.toString();
      expect(pdfString).toContain(mockSecurityData.timestamp.timestampHash);
    });
  });

  describe("Content verification", () => {
    it("should include citizen ID prominently", async () => {
      const result = await PdfGeneratorV2Enhanced.generateCertificatePdf(
        "test-cert-id"
      );

      const pdfString = result.toString();
      expect(pdfString).toContain("ID CITOYEN:");
      expect(pdfString).toContain(
        mockCertificate.citizen.numeroIdentificationUnique
      );
    });

    it("should include certificate reference", async () => {
      const result = await PdfGeneratorV2Enhanced.generateCertificatePdf(
        "test-cert-id"
      );

      const pdfString = result.toString();
      expect(pdfString).toContain(mockCertificate.reference);
    });

    it("should include citizen information", async () => {
      const result = await PdfGeneratorV2Enhanced.generateCertificatePdf(
        "test-cert-id"
      );

      const pdfString = result.toString();
      expect(pdfString).toContain(mockCertificate.citizenName);
      expect(pdfString).toContain(mockCertificate.citizen.profession);
      expect(pdfString).toContain(mockCertificate.quartier?.commune);
    });

    it("should include validity period", async () => {
      const result = await PdfGeneratorV2Enhanced.generateCertificatePdf(
        "test-cert-id"
      );

      const pdfString = result.toString();
      expect(pdfString).toContain("Validité: 03 Mois");
    });
  });

  describe("Error handling", () => {
    it("should handle image loading errors", async () => {
      // Mock fetch pour simuler une erreur
      global.fetch = vi.fn().mockRejectedValue(new Error("Image not found"));

      const result = await PdfGeneratorV2Enhanced.generateCertificatePdf(
        "test-cert-id"
      );

      // Le PDF devrait quand même être généré même sans images
      expect(result).toBeInstanceOf(Buffer);
      expect(result.length).toBeGreaterThan(0);
    });

    it("should handle crypto service errors", async () => {
      vi.mocked(
        CertificateCrypto.generateCertificateSecurity
      ).mockImplementation(() => {
        throw new Error("Crypto error");
      });

      // Le test devrait échouer car la sécurité est critique
      await expect(
        PdfGeneratorV2Enhanced.generateCertificatePdf("test-cert-id")
      ).rejects.toThrow("Crypto error");
    });
  });
});
