"use client";

import Image from "next/image";
import { motion } from "framer-motion";
import { useState } from "react";

const containerVariants = {
  initial: { opacity: 0 },
  animate: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

const privacySections = [
  {
    id: "collecte",
    icon: "📊",
    title: "Collecte des Données",
    content: `Nous collectons uniquement les informations nécessaires à la délivrance de votre certificat de résidence :
    • Informations d'identité (nom, prénom, date de naissance)
    • Coordonnées (adresse, email, téléphone)
    • Documents justificatifs (pièce d'identité, justificatif de domicile)
    • Données de connexion pour la sécurité de votre compte`,
  },
  {
    id: "utilisation",
    icon: "🔍",
    title: "Utilisation des Données",
    content: `Vos données sont utilisées exclusivement pour :
    • Traiter votre demande de certificat de résidence
    • Vérifier votre identité et votre domicile
    • Vous contacter concernant votre demande
    • Améliorer nos services et assurer leur sécurité`,
  },
  {
    id: "protection",
    icon: "🔒",
    title: "Protection des Données",
    content: `Nous mettons en œuvre des mesures de sécurité robustes :
    • Chiffrement SSL/TLS des données
    • Authentification forte des utilisateurs
    • Stockage sécurisé sur des serveurs protégés
    • Accès restreint aux données personnelles`,
  },
  {
    id: "droits",
    icon: "⚖️",
    title: "Vos Droits",
    content: `Conformément au RGPD, vous disposez des droits suivants :
    • Droit d'accès à vos données
    • Droit de rectification
    • Droit à l'effacement
    • Droit à la portabilité
    • Droit d'opposition au traitement`,
  },
  {
    id: "conservation",
    icon: "📅",
    title: "Conservation des Données",
    content: `Nous conservons vos données :
    • Pendant la durée légale requise pour les certificats
    • 3 ans après votre dernière activité pour votre compte
    • Les documents justificatifs sont supprimés après validation`,
  },
  {
    id: "cookies",
    icon: "🍪",
    title: "Politique de Cookies",
    content: `Nous utilisons des cookies pour :
    • Assurer le fonctionnement technique du site
    • Améliorer votre expérience utilisateur
    • Sécuriser votre connexion
    • Analyser l'utilisation de nos services`,
  },
];

export default function PrivacyPage() {
  const [activeSection, setActiveSection] = useState<string | null>(null);

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-[#004D40] via-[#00695C] to-[#00796B]">
      {/* Motif de fond */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-10" />
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-black/5 to-black/20" />
      </div>

      {/* Cercles décoratifs */}
      <motion.div
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.2, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -left-32 -top-32 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-3xl opacity-30"
      />
      <motion.div
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.2, 0.3, 0.2],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -right-32 top-1/4 w-96 h-96 bg-red-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20"
      />

      {/* Contenu principal */}
      <div className="relative z-10 container mx-auto px-4 py-16">
        <motion.div
          variants={containerVariants}
          initial="initial"
          animate="animate"
          className="max-w-4xl mx-auto"
        >
          {/* En-tête */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <div className="flex justify-center mb-6">
              <div className="relative w-20 h-20 bg-white/10 rounded-2xl p-3 backdrop-blur-sm">
                <Image
                  src="/logo.png"
                  alt="Logo NCR"
                  width={64}
                  height={64}
                  className="object-contain"
                  priority
                />
                <motion.div
                  animate={{
                    rotate: 360,
                  }}
                  transition={{
                    duration: 20,
                    repeat: Infinity,
                    ease: "linear",
                  }}
                  className="absolute inset-0 rounded-2xl border-2 border-white/20"
                />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-white mb-4">
              Politique de Confidentialité
            </h1>
            <p className="text-xl text-white/80">
              Protection et transparence de vos données personnelles
            </p>
          </motion.div>

          {/* Date de mise à jour */}
          <motion.div variants={itemVariants} className="text-center mb-12">
            <div className="inline-block bg-white/10 backdrop-blur-sm rounded-full px-6 py-2 border border-white/10">
              <p className="text-white/60 text-sm">
                Dernière mise à jour : {new Date().toLocaleDateString()}
              </p>
            </div>
          </motion.div>

          {/* Sections de confidentialité */}
          <motion.div variants={itemVariants} className="space-y-6">
            {privacySections.map((section) => (
              <motion.div
                key={section.id}
                initial={false}
                animate={{
                  backgroundColor:
                    activeSection === section.id
                      ? "rgba(255, 255, 255, 0.1)"
                      : "rgba(255, 255, 255, 0.05)",
                }}
                className="rounded-xl backdrop-blur-sm border border-white/10 overflow-hidden"
              >
                <button
                  onClick={() =>
                    setActiveSection(
                      activeSection === section.id ? null : section.id
                    )
                  }
                  className="w-full px-6 py-4 flex items-center gap-4 text-left"
                >
                  <span className="text-2xl">{section.icon}</span>
                  <span className="flex-1 text-white font-medium">
                    {section.title}
                  </span>
                  <motion.span
                    animate={{ rotate: activeSection === section.id ? 180 : 0 }}
                    className="text-white/60"
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </motion.span>
                </button>
                <motion.div
                  initial={false}
                  animate={{
                    height: activeSection === section.id ? "auto" : 0,
                    opacity: activeSection === section.id ? 1 : 0,
                  }}
                  className="overflow-hidden"
                >
                  <div className="px-6 pb-4 text-white/80 whitespace-pre-line">
                    {section.content}
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>

          {/* Contact DPO */}
          <motion.div
            variants={itemVariants}
            className="mt-12 bg-white/10 backdrop-blur-sm rounded-xl border border-white/10 p-6 text-center"
          >
            <h3 className="text-xl font-semibold text-white mb-4">
              Contact Délégué à la Protection des Données
            </h3>
            <p className="text-white/80 mb-4">
              Pour toute question concernant vos données personnelles, contactez
              notre DPO :
            </p>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center gap-2 text-yellow-400 hover:text-yellow-300 transition-colors duration-200"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
              <EMAIL>
            </a>
          </motion.div>
        </motion.div>
      </div>

      {/* Bordures décoratives */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-yellow-400/20 via-red-500/20 to-green-400/20" />
        <div className="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-green-400/20 via-red-500/20 to-yellow-400/20" />
        <div className="absolute top-0 left-0 h-full w-2 bg-gradient-to-b from-yellow-400/20 via-red-500/20 to-green-400/20" />
        <div className="absolute top-0 right-0 h-full w-2 bg-gradient-to-b from-green-400/20 via-red-500/20 to-yellow-400/20" />
      </div>
    </div>
  );
}
