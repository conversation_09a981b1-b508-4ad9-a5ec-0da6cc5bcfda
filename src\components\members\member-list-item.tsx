import { UserAvatar } from "@/components/ui/user-avatar";

interface Member {
  name: string;
  avatarUrl?: string;
  role: string;
  status?: "online" | "offline" | "away" | "busy";
}

interface MemberListItemProps {
  member: Member;
}

export function MemberListItem({ member }: MemberListItemProps) {
  return (
    <div className="flex items-center space-x-3 p-2">
      <UserAvatar
        name={member.name}
        src={member.avatarUrl}
        size="md"
        showStatus
        status={member.status}
      />
      <div>
        <p className="font-medium">{member.name}</p>
        <p className="text-sm text-neutral-600">{member.role}</p>
      </div>
    </div>
  );
}