"use client";

import { useRef, useState } from "react";
import SignaturePad from "react-signature-canvas";
import { Button } from "../ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Eraser, Save } from "lucide-react";

interface SignaturePadProps {
  onSave: (signature: string) => void;
  onClear?: () => void;
  title?: string;
  description?: string;
  id?: string;
}

export function SignaturePadComponent({
  onSave,
  onClear,
  title = "Votre signature",
  description = "Veuillez signer dans le cadre ci-dessous",
  id,
}: SignaturePadProps) {
  const signaturePadRef = useRef<SignaturePad>(null);
  const [isEmpty, setIsEmpty] = useState(true);

  const handleClear = () => {
    if (signaturePadRef.current) {
      signaturePadRef.current.clear();
      setIsEmpty(true);
      onClear?.();
    }
  };

  const handleSave = () => {
    if (signaturePadRef.current && !isEmpty) {
      const signatureData = signaturePadRef.current.toDataURL("image/png");
      onSave(signatureData);
    }
  };

  const handleBegin = () => {
    setIsEmpty(false);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto" id={id}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <p className="text-sm text-muted-foreground">{description}</p>
      </CardHeader>
      <CardContent>
        <div className="border rounded-lg p-2 bg-white">
          <SignaturePad
            ref={signaturePadRef}
            canvasProps={{
              className: "w-full h-64 border rounded-md",
              style: {
                width: "100%",
                height: "250px",
                backgroundColor: "#fff",
              },
            }}
            onBegin={handleBegin}
          />
        </div>
      </CardContent>
      <CardFooter className="flex justify-end gap-2">
        <Button variant="outline" onClick={handleClear} disabled={isEmpty}>
          <Eraser className="h-4 w-4 mr-2" />
          Effacer
        </Button>
        <Button onClick={handleSave} disabled={isEmpty}>
          <Save className="h-4 w-4 mr-2" />
          Valider la signature
        </Button>
      </CardFooter>
    </Card>
  );
}
