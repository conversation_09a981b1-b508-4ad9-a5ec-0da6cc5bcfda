{"name": "ncr-new", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "generate:fonts": "node scripts/generate-fonts.js", "generate:manifest": "node -r @swc-node/register src/lib/utils/generate-manifest.ts", "generate:icons": "tsx scripts/generate-icons.ts", "generate:screenshots": "tsx scripts/generate-screenshots.ts", "generate:og": "tsx scripts/generate-og-image.ts", "generate:pwa": "pnpm generate:icons && pnpm generate:og && pnpm generate:fonts && pnpm generate:manifest", "build:dev": "pnpm generate:pwa && next build", "build": "next build", "start": "next start", "lint": "next lint", "init:appwrite": "tsx scripts/init-appwrite.ts"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "@tanstack/react-table": "^8.20.5", "@testing-library/react": "^16.3.0", "@types/nodemailer": "^6.4.17", "@types/react-signature-canvas": "^1.0.6", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "commander": "^12.1.0", "date-fns": "^4.1.0", "framer-motion": "^10.18.0", "jspdf": "^2.5.2", "lucide-react": "^0.475.0", "next": "^15.1.7", "node-appwrite": "^13.0.0", "nodemailer": "^6.10.0", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.54.2", "react-signature-canvas": "^1.0.6", "sharp": "^0.32.6", "sonner": "^1.7.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tsx": "^3.11.0", "use-debounce": "^10.0.4", "vitest": "^3.1.4", "zod": "^3.24.2"}, "devDependencies": {"@swc-node/register": "^1.6.8", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/node": "^20.9.0", "@types/qrcode": "^1.5.5", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^9.16.0", "eslint-config-next": "15.1.0", "postcss": "^8", "puppeteer": "^22.4.1", "sharp": "^0.33.2", "tailwindcss": "^3.3.0", "typescript": "^5"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}