"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  MoreHorizontal,
  UserPlus,
  Shield,
  UserCog,
  Building2,
  Loader2,
  CheckCircle,
  Ban
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { useUsers } from "@/hooks/use-users";
import { ROLES, STATUS } from "@/actions/auth/constants";
import { cn } from "@/lib/utils/cn";
import { motion } from "framer-motion";
import { useState } from "react";
import { UserFilters } from "./user-filters";
import Link from "next/link";

interface RoleColors {
  [key: string]: string;
}

const roleColors: RoleColors = {
  [ROLES.ADMIN]: "bg-red-100 text-red-800",
  [ROLES.CHEF]: "bg-purple-100 text-purple-800",
  [ROLES.AGENT]: "bg-blue-100 text-blue-800",
  [ROLES.CITIZEN]: "bg-green-100 text-green-800",
};

const statusVariants: Record<STATUS, "success" | "warning" | "error" | "secondary"> = {
  [STATUS.ACTIVE]: "success",
  [STATUS.PENDING]: "warning",
  [STATUS.BLOCKED]: "error",
  [STATUS.INACTIVE]: "secondary",
};

// Définition des transitions de statut autorisées
const statusTransitions: Record<STATUS, STATUS[]> = {
  [STATUS.BLOCKED]: [STATUS.ACTIVE],    // De bloqué vers actif uniquement
  [STATUS.ACTIVE]: [STATUS.BLOCKED],    // D'actif vers bloqué uniquement
  [STATUS.PENDING]: [STATUS.ACTIVE, STATUS.BLOCKED], // De en attente vers actif ou bloqué
  [STATUS.INACTIVE]: [STATUS.ACTIVE],   // D'inactif vers actif uniquement
};

// Composant pour afficher le badge de statut
function StatusBadge({ status }: { status: STATUS }) {
  const variant = statusVariants[status];
  const statusLabels: Record<STATUS, string> = {
    [STATUS.ACTIVE]: "Actif",
    [STATUS.PENDING]: "En attente",
    [STATUS.BLOCKED]: "Bloqué",
    [STATUS.INACTIVE]: "Inactif",
  };

  return (
    <Badge variant={variant}>
      {status === STATUS.ACTIVE && <CheckCircle className="w-3 h-3 mr-1" />}
      {status === STATUS.BLOCKED && <Ban className="w-3 h-3 mr-1" />}
      {statusLabels[status]}
    </Badge>
  );
}

export function UsersTable() {
  const [filters, setFilters] = useState({
    role: undefined as ROLES | undefined,
    status: undefined as STATUS | undefined,
    search: "",
    page: 0,
    limit: 10,
  });

  const { users, pagination, isLoading, updateStatus } = useUsers(filters);

  const handleStatusChange = async (userId: string, newStatus: STATUS) => {
    try {
      await updateStatus.mutate({ userId, status: newStatus });
    } catch (error) {
      console.error("Erreur lors du changement de statut:", error);
    }
  };

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <UserFilters filters={filters} onFiltersChange={setFilters} />
        <Link href="/admin/users/new">
          <Button className="w-full sm:w-auto">
            <UserPlus className="mr-2 h-4 w-4" />
            Ajouter un utilisateur
          </Button>
        </Link>
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="rounded-lg border border-neutral-200/60 overflow-hidden bg-white"
      >
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nom</TableHead>
              <TableHead>Rôle</TableHead>
              <TableHead>Statut</TableHead>
              <TableHead>Quartier</TableHead>
              <TableHead>Date d'inscription</TableHead>
              <TableHead>Dernière connexion</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  <Loader2 className="h-6 w-6 animate-spin mx-auto text-emerald-600" />
                </TableCell>
              </TableRow>
            ) : !users?.length ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center text-neutral-500">
                  Aucun utilisateur trouvé
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.$id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-2">
                      <span>{user.name}</span>
                      {user.role === ROLES.ADMIN && (
                        <Shield className="h-4 w-4 text-red-500" />
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={cn(roleColors[user.role], "border-none")}>
                      {user.role === ROLES.ADMIN && <Shield className="w-3 h-3 mr-1" />}
                      {user.role === ROLES.CHEF && <UserCog className="w-3 h-3 mr-1" />}
                      {user.role === ROLES.AGENT && <Building2 className="w-3 h-3 mr-1" />}
                      {user.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <StatusBadge status={user.status} />
                  </TableCell>
                  <TableCell>{user.district?.name || "-"}</TableCell>
                  <TableCell>
                    {user.createdAt.toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {user.lastLoginAt.toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="ghost" 
                          className="h-8 w-8 p-0 hover:bg-neutral-100 transition-colors duration-200"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent 
                        align="end" 
                        className="w-48 shadow-lg border-neutral-200/70 bg-white rounded-lg p-1.5"
                        style={{ zIndex: 1000 }}
                      >
                        {statusTransitions[user.status]?.map((newStatus) => (
                          <DropdownMenuItem
                            key={newStatus}
                            onClick={() => handleStatusChange(user.$id, newStatus)}
                            className={cn(
                              "flex items-center px-3 py-2 text-sm rounded-md cursor-pointer transition-colors duration-200",
                              {
                                "text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50":
                                  newStatus === STATUS.ACTIVE,
                                "text-red-600 hover:text-red-700 hover:bg-red-50":
                                  newStatus === STATUS.BLOCKED,
                              }
                            )}
                          >
                            {newStatus === STATUS.ACTIVE && (
                              <>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                {user.status === STATUS.BLOCKED ? "Débloquer" : "Activer"}
                              </>
                            )}
                            {newStatus === STATUS.BLOCKED && (
                              <>
                                <Ban className="mr-2 h-4 w-4" />
                                Bloquer
                              </>
                            )}
                          </DropdownMenuItem>
                        ))}

                        {/* Actions supplémentaires avec séparateur */}
                        {(user.role === ROLES.AGENT || user.role === ROLES.CHEF) && (
                          <>
                            <DropdownMenuSeparator className="my-1.5 border-neutral-200/70" />
                            {user.role === ROLES.AGENT && (
                              <DropdownMenuItem className="flex items-center px-3 py-2 text-sm rounded-md cursor-pointer hover:bg-neutral-100 transition-colors duration-200">
                                <UserCog className="mr-2 h-4 w-4" />
                                Assigner un chef
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem className="flex items-center px-3 py-2 text-sm rounded-md cursor-pointer hover:bg-neutral-100 transition-colors duration-200">
                              <Building2 className="mr-2 h-4 w-4" />
                              Assigner un quartier
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>

        {/* Pagination */}
        {pagination && (
          <div className="flex items-center justify-between border-t border-neutral-200/60 px-6 py-4">
            <div className="flex-1 text-sm text-neutral-500">
              {pagination.total > 0 ? (
                <p>
                  Affichage de{" "}
                  <span className="font-medium">
                    {pagination.page * pagination.limit + 1}
                  </span>{" "}
                  à{" "}
                  <span className="font-medium">
                    {Math.min(
                      (pagination.page + 1) * pagination.limit,
                      pagination.total
                    )}
                  </span>{" "}
                  sur <span className="font-medium">{pagination.total}</span> utilisateurs
                </p>
              ) : null}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page <= 0 || isLoading}
              >
                Précédent
              </Button>
              <div className="flex items-center gap-1 text-sm font-medium">
                <span className="px-3 py-1 rounded-md bg-neutral-100">
                  {pagination.page + 1}
                </span>
                <span className="text-neutral-400">
                  sur {Math.ceil(pagination.total / pagination.limit)}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={
                  pagination.page >= Math.ceil(pagination.total / pagination.limit) - 1 ||
                  isLoading
                }
              >
                Suivant
              </Button>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
}