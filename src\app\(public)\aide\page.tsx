"use client";

import Image from "next/image";
import { motion } from "framer-motion";
import { useState } from "react";

// Animation variants
const containerVariants = {
  initial: { opacity: 0 },
  animate: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

// FAQ Data
const faqCategories = [
  {
    id: "general",
    icon: "🏠",
    title: "Informations Générales",
    questions: [
      {
        q: "Qu'est-ce que le certificat de résidence numérique ?",
        a: "Le certificat de résidence numérique est un document officiel délivré par l'administration guinéenne qui atteste de votre lieu de résidence. Cette version numérique simplifie les démarches administratives tout en garantissant la sécurité et l'authenticité du document.",
      },
      {
        q: "Combien de temps faut-il pour obtenir un certificat ?",
        a: "Le délai standard de traitement est de 48 heures ouvrables. Une fois votre demande validée, vous recevrez une notification par email et SMS.",
      },
    ],
  },
  {
    id: "process",
    icon: "📝",
    title: "Processus de Demande",
    questions: [
      {
        q: "Quels documents dois-je fournir ?",
        a: "Vous devez fournir une pièce d'identité valide (carte nationale d'identité, passeport), un justificatif de domicile de moins de 3 mois, et une photo d'identité récente.",
      },
      {
        q: "Comment suivre l'état de ma demande ?",
        a: "Connectez-vous à votre espace personnel pour suivre en temps réel l'état d'avancement de votre demande. Vous recevrez également des notifications par email à chaque étape importante.",
      },
    ],
  },
  {
    id: "technical",
    icon: "💻",
    title: "Support Technique",
    questions: [
      {
        q: "J'ai oublié mon mot de passe, que faire ?",
        a: "Cliquez sur 'Mot de passe oublié' sur la page de connexion. Vous recevrez un lien de réinitialisation sur votre email enregistré.",
      },
      {
        q: "Comment assurer la sécurité de mon compte ?",
        a: "Utilisez un mot de passe fort, activez l'authentification à deux facteurs, et ne partagez jamais vos identifiants. Nous utilisons le chiffrement SSL pour protéger vos données.",
      },
    ],
  },
];

export default function FAQPage() {
  const [activeCategory, setActiveCategory] = useState("general");
  const [openQuestion, setOpenQuestion] = useState<string | null>(null);

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-[#004D40] via-[#00695C] to-[#00796B]">
      {/* Motif de fond */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-10" />
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-black/5 to-black/20" />
      </div>

      {/* Cercles décoratifs */}
      <motion.div
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.2, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -left-32 -top-32 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-3xl opacity-30"
      />
      <motion.div
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.2, 0.3, 0.2],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -right-32 top-1/4 w-96 h-96 bg-red-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20"
      />

      {/* Contenu principal */}
      <div className="relative z-10 container mx-auto px-4 py-16">
        <motion.div
          variants={containerVariants}
          initial="initial"
          animate="animate"
          className="max-w-4xl mx-auto"
        >
          {/* En-tête */}
          <motion.div
            variants={itemVariants}
            className="text-center mb-16"
          >
            <div className="flex justify-center mb-6">
              <div className="relative w-20 h-20 bg-white/10 rounded-2xl p-3 backdrop-blur-sm">
                <Image
                  src="/logo.png"
                  alt="Logo NCR"
                  width={64}
                  height={64}
                  className="object-contain"
                  priority
                />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-white mb-4">
              Centre d&apos;aide NCR
            </h1>
            <p className="text-xl text-white/80">
              Comment pouvons-nous vous aider ?
            </p>
          </motion.div>

          {/* Barre de recherche */}
          <motion.div
            variants={itemVariants}
            className="mb-12"
          >
            <div className="relative max-w-2xl mx-auto">
              <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 via-red-500/20 to-green-400/20 rounded-xl blur-xl" />
              <div className="relative bg-white/10 backdrop-blur-sm rounded-xl border border-white/10 p-2">
                <div className="flex items-center gap-3 px-4 py-2">
                  <svg
                    className="w-5 h-5 text-white/60"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                  <input
                    type="text"
                    placeholder="Rechercher une réponse..."
                    className="w-full bg-transparent text-white placeholder-white/60 focus:outline-none"
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* Catégories */}
          <motion.div
            variants={itemVariants}
            className="grid md:grid-cols-3 gap-4 mb-12"
          >
            {faqCategories.map((category) => (
              <motion.button
                key={category.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setActiveCategory(category.id)}
                className={`p-4 rounded-xl backdrop-blur-sm border transition-all duration-300 ${
                  activeCategory === category.id
                    ? "bg-white/20 border-white/20"
                    : "bg-white/10 border-white/10 hover:bg-white/15"
                }`}
              >
                <span className="text-2xl mb-2 block">{category.icon}</span>
                <h3 className="text-lg font-semibold text-white">
                  {category.title}
                </h3>
              </motion.button>
            ))}
          </motion.div>

          {/* Questions et Réponses */}
          <motion.div
            variants={itemVariants}
            className="space-y-4"
          >
            {faqCategories
              .find((cat) => cat.id === activeCategory)
              ?.questions.map((faq, index) => (
                <motion.div
                  key={index}
                  initial={false}
                  animate={{ backgroundColor: openQuestion === faq.q ? "rgba(255, 255, 255, 0.1)" : "rgba(255, 255, 255, 0.05)" }}
                  className="rounded-xl backdrop-blur-sm border border-white/10 overflow-hidden"
                >
                  <button
                    onClick={() => setOpenQuestion(openQuestion === faq.q ? null : faq.q)}
                    className="w-full px-6 py-4 flex items-center justify-between text-left"
                  >
                    <span className="text-white font-medium">{faq.q}</span>
                    <motion.span
                      animate={{ rotate: openQuestion === faq.q ? 180 : 0 }}
                      className="text-white/60"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </motion.span>
                  </button>
                  <motion.div
                    initial={false}
                    animate={{
                      height: openQuestion === faq.q ? "auto" : 0,
                      opacity: openQuestion === faq.q ? 1 : 0,
                    }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 pb-4 text-white/80">
                      {faq.a}
                    </div>
                  </motion.div>
                </motion.div>
              ))}
          </motion.div>

          {/* Contact Support */}
          <motion.div
            variants={itemVariants}
            className="mt-12 text-center"
          >
            <p className="text-white/60">
              Vous ne trouvez pas ce que vous cherchez ?{" "}
              <a
                href="/contact"
                className="text-yellow-400 hover:text-yellow-300 transition-colors duration-200"
              >
                Contactez notre support
              </a>
            </p>
          </motion.div>
        </motion.div>
      </div>

      {/* Bordures décoratives */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-yellow-400/20 via-red-500/20 to-green-400/20" />
        <div className="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-green-400/20 via-red-500/20 to-yellow-400/20" />
        <div className="absolute top-0 left-0 h-full w-2 bg-gradient-to-b from-yellow-400/20 via-red-500/20 to-green-400/20" />
        <div className="absolute top-0 right-0 h-full w-2 bg-gradient-to-b from-green-400/20 via-red-500/20 to-yellow-400/20" />
      </div>
    </div>
  );
}
