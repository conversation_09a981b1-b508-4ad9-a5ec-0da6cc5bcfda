import { Clock, Mail, MapPin, Phone, Shield } from "lucide-react";
import Link from "next/link";

const navigation = {
  official: [
    { name: "Portail NCR", href: "/" },
    { name: "À propos du système", href: "/a-propos" },
    { name: "Services", href: "/services" },
    { name: "Aide et support", href: "/aide" },
  ],
  contact: [
    {
      icon: MapPin,
      text: "Ministère de l'Administration du Territoire, Conakry",
    },
    { icon: Phone, text: "+224 XX XX XX XX" },
    { icon: Mail, text: "<EMAIL>" },
    { icon: Clock, text: "Service 24h/24, 7j/7" },
  ],
  legal: [
    { name: "Politique de Confidentialité", href: "/confidentialite" },
    { name: "Conditions d'Utilisation", href: "/conditions" },
    { name: "Cookies", href: "/cookies" },
  ],
};

export function VerificationFooterSSR() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="relative mt-auto pt-16 overflow-hidden">
      {/* Background with glassmorphism effect */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/95 via-white/90 to-white/85 backdrop-blur-xl border-t border-neutral-200/50" />

      {/* Decorative circles with Guinea colors */}
      <div className="absolute -left-24 -bottom-24 w-96 h-96 bg-green-600 rounded-full mix-blend-multiply filter blur-3xl opacity-10" />
      <div className="absolute -right-24 -bottom-24 w-96 h-96 bg-yellow-400 rounded-full mix-blend-multiply filter blur-3xl opacity-10" />
      <div className="absolute -top-24 left-1/2 w-96 h-96 bg-red-400 rounded-full mix-blend-multiply filter blur-3xl opacity-10" />

      <div className="relative container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 pb-12">
          {/* Logo and Description */}
          <div className="space-y-6">
            <Link href="/" className="flex items-center gap-4 group">
              <div className="relative w-12 h-12 transform transition-transform group-hover:scale-105">
                <div className="absolute inset-0 bg-white rounded-xl opacity-20 blur-md group-hover:opacity-30 transition-opacity" />
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center relative z-10">
                  <Shield className="w-6 h-6 text-white" />
                </div>
              </div>
              <div className="space-y-1">
                <h3 className="text-xl font-bold bg-gradient-to-r from-green-600 via-yellow-600 to-red-600 bg-clip-text text-transparent">
                  Vérification NCR
                </h3>
                <p className="text-sm text-neutral-700 font-medium">
                  République de Guinée
                </p>
              </div>
            </Link>
            <p className="text-neutral-700 text-sm leading-relaxed font-medium">
              Système officiel de vérification des certificats de résidence.
              Garantit l'authenticité des documents émis par les autorités
              locales de la République de Guinée.
            </p>

            {/* Guinea tricolor band */}
            <div className="space-y-2">
              <div className="flex h-2 rounded-full overflow-hidden shadow-sm">
                <div className="flex-1 bg-red-600"></div>
                <div className="flex-1 bg-yellow-500"></div>
                <div className="flex-1 bg-green-600"></div>
              </div>
              <p className="text-xs text-center text-neutral-700 font-semibold">
                Travail - Justice - Solidarité
              </p>
            </div>
          </div>

          {/* Verification Links */}
          <div className="space-y-6">
            <h3 className="text-sm font-semibold text-neutral-900">
              Vérification
            </h3>
            <ul className="space-y-3">
              {navigation.official.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-neutral-700 hover:text-green-600 text-sm transition-colors duration-200 hover:underline decoration-green-600/30 underline-offset-4 font-medium"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact */}
          <div className="space-y-6">
            <h3 className="text-sm font-semibold text-neutral-900">Contact</h3>
            <ul className="space-y-4">
              {navigation.contact.map((item, index) => (
                <li key={index} className="flex items-start gap-3 group">
                  <div className="p-2 rounded-lg bg-green-600/10 group-hover:bg-green-600/15 transition-colors duration-200 mt-0.5 shadow-sm">
                    <item.icon className="w-4 h-4 text-green-600" />
                  </div>
                  <span className="text-sm text-neutral-700 leading-relaxed font-medium">
                    {item.text}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Security and Legal */}
          <div className="space-y-6">
            <h3 className="text-sm font-semibold text-neutral-900">
              Sécurité & Légal
            </h3>

            {/* Security badges */}
            <div className="space-y-3">
              <div className="flex flex-wrap gap-2">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-700 border border-green-200">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  SSL/TLS
                </span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-700 border border-blue-200">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                  Signature Numérique
                </span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-700 border border-yellow-200">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                  Horodatage
                </span>
              </div>

              <p className="text-xs text-neutral-700 leading-relaxed font-medium">
                Système sécurisé utilisant des technologies de cryptographie
                avancées pour garantir l'authenticité des certificats.
              </p>
            </div>
          </div>
        </div>

        {/* Separator and Copyright */}
        <div className="border-t border-neutral-300/70 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-center md:text-left">
              <p className="text-sm text-neutral-800 font-medium">
                © {currentYear} République de Guinée. Tous droits réservés.
              </p>
              <p className="text-xs text-neutral-700 mt-1 font-medium">
                Système de vérification officiel des certificats de résidence
              </p>
            </div>
            <div className="flex items-center gap-6">
              {navigation.legal.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-sm text-neutral-700 hover:text-green-600 transition-colors duration-200
                    hover:underline decoration-green-600/30 underline-offset-4 font-medium"
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
