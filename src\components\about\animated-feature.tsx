"use client";

import { motion } from "framer-motion";

interface AnimatedFeatureProps {
  icon: string;
  title: string;
  description: string;
  delay: number;
}

export function AnimatedFeature({ icon, title, description, delay }: AnimatedFeatureProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay }}
      className="flex items-start space-x-4"
    >
      <div className="text-3xl">{icon}</div>
      <div>
        <h3 className="font-semibold text-[#004D40]">
          {title}
        </h3>
        <p className="text-gray-600">{description}</p>
      </div>
    </motion.div>
  );
}
