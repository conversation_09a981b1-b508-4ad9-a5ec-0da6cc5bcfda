"use client";

import { verifyPayment } from "@/actions/payment";
import { PaymentStatus } from "@/components/payment/PaymentStatus";
import { PaymentStatus as PaymentStatusEnum } from "@/config/payment";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

const containerVariants = {
  initial: { opacity: 0 },
  animate: {
    opacity: 1,
    transition: { staggerChildren: 0.1 },
  },
};

const itemVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

const getPaymentStatusInfo = (status: PaymentStatusEnum | undefined) => {
  switch (status) {
    case PaymentStatusEnum.SUCCESS:
      return {
        title: "Paiement réussi",
        description: "Votre transaction a été effectuée avec succès.",
        icon: "✅",
      };
    case PaymentStatusEnum.FAILED:
      return {
        title: "Échec du paiement",
        description:
          "La transaction n'a pas pu être complétée. Veuillez réessayer.",
        icon: "❌",
      };
    case PaymentStatusEnum.EXPIRED:
      return {
        title: "Paiement expiré",
        description:
          "La transaction a expiré. Veuillez réessayer pour continuer.",
        icon: "⌛",
      };
    case PaymentStatusEnum.PENDING:
      return {
        title: "Paiement en attente",
        description: "Votre transaction est en cours de traitement.",
        icon: "⏳",
      };
    default:
      return {
        title: "Traitement en cours",
        description:
          "Veuillez patienter pendant que nous traitons votre paiement.",
        icon: "🔄",
      };
  }
};

export default function PaymentSuccessPage() {
  const searchParams = useSearchParams();
  const transactionId = searchParams.get("transactionId") || "";
  const orderId = searchParams.get("orderId") || "";
  const [status, setStatus] = useState<PaymentStatusEnum>();
  const [callbackUrl, setCallbackUrl] = useState<string>("/dashboard");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkPaymentStatus = async () => {
      if (!transactionId) return;
      try {
        const result = await verifyPayment(transactionId);

        if (result.success) {
          setStatus(result.status);
          setCallbackUrl(result.returnUrl);
        }
      } catch (error) {
        console.error("Erreur lors de la vérification du paiement:", error);
        setStatus(PaymentStatusEnum.FAILED);
      } finally {
        setIsLoading(false);
      }
    };

    checkPaymentStatus();
  }, [transactionId]);

  const statusInfo = getPaymentStatusInfo(
    isLoading ? PaymentStatusEnum.PENDING : status
  );

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-[#004D40] via-[#00695C] to-[#00796B] flex items-center justify-center">
      {/* Motif de fond */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-10" />
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-black/5 to-black/20" />
      </div>

      {/* Cercles décoratifs animés */}
      <motion.div
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.2, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -left-32 -top-32 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-3xl opacity-30"
      />
      <motion.div
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.2, 0.3, 0.2],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -right-32 top-1/4 w-96 h-96 bg-red-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20"
      />

      {/* Contenu principal */}
      <div className="relative z-10 container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="initial"
          animate="animate"
          className="max-w-2xl mx-auto"
        >
          {/* Logo */}
          <motion.div
            variants={itemVariants}
            className="mb-8 flex justify-center"
          >
            <div className="relative w-20 h-20 bg-white/10 rounded-2xl p-3 backdrop-blur-sm">
              <Image
                src="/logo.png"
                alt="Logo NCR"
                width={64}
                height={64}
                className="object-contain"
                priority
              />
            </div>
          </motion.div>

          {/* En-tête du statut */}
          <motion.div variants={itemVariants} className="text-center mb-8">
            <span className="text-4xl mb-4 block">{statusInfo.icon}</span>
            <h1 className="text-2xl font-semibold text-white mb-2">
              {statusInfo.title}
            </h1>
            <p className="text-white/80">{statusInfo.description}</p>
          </motion.div>

          {/* Détails de la transaction */}
          <motion.div variants={itemVariants} className="mb-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <PaymentStatus
                transactionId={transactionId}
                status={status || PaymentStatusEnum.PENDING}
                redirectUrl={callbackUrl}
              />
              {(transactionId || orderId) && (
                <div className="mt-4 pt-4 border-t border-white/10">
                  {transactionId && (
                    <p className="text-white/70 text-sm">
                      Référence de transaction:
                      <span className="ml-2 text-white font-mono">
                        {transactionId}
                      </span>
                    </p>
                  )}
                  {orderId && (
                    <p className="text-white/70 text-sm mt-2">
                      Numéro de commande:
                      <span className="ml-2 text-white font-mono">
                        {orderId}
                      </span>
                    </p>
                  )}
                </div>
              )}
            </div>
          </motion.div>

          {/* Actions */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col items-center gap-4"
          >
            <Link
              href="/"
              className="px-6 py-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg text-white transition-colors inline-flex items-center gap-2"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              Retour à l&apos;accueil
            </Link>
            <Link
              href="/contact"
              className="text-white/70 hover:text-white text-sm flex items-center gap-1 transition-colors"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
              </svg>
              Besoin d&apos;aide ?
            </Link>
          </motion.div>
        </motion.div>
      </div>

      {/* Bordures décoratives */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-yellow-400/20 via-red-500/20 to-green-400/20" />
        <div className="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-green-400/20 via-red-500/20 to-yellow-400/20" />
        <div className="absolute top-0 left-0 h-full w-2 bg-gradient-to-b from-yellow-400/20 via-red-500/20 to-green-400/20" />
        <div className="absolute top-0 right-0 h-full w-2 bg-gradient-to-b from-green-400/20 via-red-500/20 to-yellow-400/20" />
      </div>
    </div>
  );
}
