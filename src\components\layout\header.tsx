"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UserAvatar } from "@/components/ui/user-avatar";
import { useAuth } from "@/hooks/use-auth";
import { AnimatePresence, motion } from "framer-motion";
import { LogOut, Menu, Settings, User, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

const navigation = [
  { name: "Accueil", href: "/" },
  { name: "Services", href: "/services" },
  { name: "À propos", href: "/a-propos" },
  {
    name: "Déclaration de naissance",
    href: "/dashboard/declaration-naissance",
  },
  { name: "Contact", href: "/contact" },
];

export function Header() {
  const pathname = usePathname();
  const { user, isLoading, signOut, isAuthenticated } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-lg border-b border-gray-100 supports-[backdrop-filter]:bg-white/60">
      <nav className="container mx-auto px-4 sm:px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo et nom */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="flex items-center space-x-4"
          >
            <Link href="/" className="flex items-center gap-4 group">
              <div className="relative w-12 h-12 transform transition-transform group-hover:scale-105">
                <div className="absolute inset-0 bg-gradient-to-br from-[#004D40] to-[#00796B] rounded-xl blur opacity-20" />
                <div className="relative">
                  <Image
                    src="/logo.png"
                    alt="Logo NCR"
                    width={48}
                    height={48}
                    className="object-contain"
                    priority
                  />
                </div>
              </div>
              <div>
                <span className="text-2xl font-bold bg-gradient-to-r from-[#004D40] via-yellow-600 to-red-600 bg-clip-text text-transparent">
                  NCR
                </span>
              </div>
            </Link>
          </motion.div>

          {/* Navigation principale - Desktop */}
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="hidden md:flex items-center space-x-1"
          >
            {navigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`relative px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    isActive
                      ? "text-[#004D40]"
                      : "text-gray-600 hover:text-[#004D40]"
                  }`}
                >
                  {item.name}
                  {isActive && (
                    <motion.div
                      layoutId="navbar-indicator"
                      className="absolute inset-0 bg-[#004D40]/5 rounded-full"
                      transition={{
                        type: "spring",
                        bounce: 0.25,
                        duration: 0.5,
                      }}
                    />
                  )}
                </Link>
              );
            })}
          </motion.div>

          {/* Actions utilisateur et bouton menu mobile */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="flex items-center space-x-4"
          >
            {/* Bouton menu mobile */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Toggle menu"
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6 text-gray-600" />
              ) : (
                <Menu className="w-6 h-6 text-gray-600" />
              )}
            </button>

            {/* Actions utilisateur */}
            <div className="hidden sm:flex items-center space-x-4">
              {isLoading ? (
                <div className="h-10 w-10 animate-pulse rounded-full bg-accent-primary/10" />
              ) : isAuthenticated ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="relative h-10 w-10 rounded-full overflow-hidden bg-accent-primary hover:bg-accent-secondary ring-2 ring-accent-primary/10 hover:ring-accent-primary/20 transition-all duration-300 shadow-sm hover:shadow-md"
                    >
                      <UserAvatar
                        name={user?.name}
                        src={user?.prefs?.avatarUrl}
                        size="md"
                        solid
                      />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <div className="flex items-center justify-start gap-2 p-2">
                      <div className="flex flex-col space-y-1 leading-none">
                        {user?.name && (
                          <p className="font-medium">{user.name}</p>
                        )}
                        {user?.email && (
                          <p className="w-[200px] truncate text-sm text-neutral-600">
                            {user.email}
                          </p>
                        )}
                      </div>
                    </div>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/profile" className="cursor-pointer">
                        <User className="mr-2 h-4 w-4" />
                        <span>Profile</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/settings" className="cursor-pointer">
                        <Settings className="mr-2 h-4 w-4" />
                        <span>Paramètres</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="cursor-pointer text-red-600 focus:text-red-600"
                      onSelect={(event) => {
                        event.preventDefault();
                        signOut();
                      }}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Déconnexion</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <>
                  <Link
                    href="/inscription"
                    className="px-4 py-2 rounded-full text-[#004D40] font-medium hover:bg-[#004D40]/5 transition-colors"
                  >
                    Inscription
                  </Link>
                  <Link
                    href="/connexion"
                    className="px-6 py-2.5 rounded-full bg-gradient-to-r from-[#004D40] to-[#00796B] text-white font-medium shadow-lg shadow-green-600/20 hover:shadow-xl hover:shadow-green-600/30 transform hover:-translate-y-0.5 transition-all duration-300"
                  >
                    Connexion
                  </Link>
                </>
              )}
            </div>
          </motion.div>
        </div>

        {/* Menu mobile */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="md:hidden mt-4"
            >
              <div className="py-2 space-y-1">
                {navigation.map((item) => {
                  const isActive = pathname === item.href;
                  return (
                    <motion.div
                      key={item.name}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Link
                        href={item.href}
                        className={`block px-4 py-3 rounded-xl text-base font-medium transition-all ${
                          isActive
                            ? "bg-[#004D40]/5 text-[#004D40]"
                            : "text-gray-600 hover:bg-gray-50 hover:text-[#004D40]"
                        }`}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {item.name}
                      </Link>
                    </motion.div>
                  );
                })}
              </div>

              {/* Actions utilisateur mobile */}
              {!isAuthenticated && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  className="mt-4 pt-4 border-t border-gray-100 grid gap-2"
                >
                  <Link
                    href="/inscription"
                    className="block text-center px-4 py-3 rounded-xl text-[#004D40] font-medium bg-[#004D40]/5 hover:bg-[#004D40]/10 transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Inscription
                  </Link>
                  <Link
                    href="/connexion"
                    className="block text-center px-4 py-3 rounded-xl bg-gradient-to-r from-[#004D40] to-[#00796B] text-white font-medium shadow-lg shadow-green-600/20"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Connexion
                  </Link>
                </motion.div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </nav>
    </header>
  );
}
