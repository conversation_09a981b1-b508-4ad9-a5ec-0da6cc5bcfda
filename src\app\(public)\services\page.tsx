"use client";

import { civilServices } from "@/data/civil-services";
import { motion } from "framer-motion";

// Types pour une meilleure maintenabilité
interface ServiceCategory {
  id: string;
  title: string;
  description: string;
  icon: string;
  services: Array<{
    id: string;
    title: string;
    description: string;
    icon: string;
    features: string[];
    timeline?: string;
    price?: string;
    comingSoon?: boolean;
  }>;
}

const serviceCategories: ServiceCategory[] = [
  {
    id: "civil-services",
    title: "Services d'État Civil",
    description: "Gérez vos documents administratifs en toute simplicité",
    icon: "📋",
    services: civilServices,
  },
  {
    id: "support-services",
    title: "Services Complémentaires",
    description: "Des outils additionnels pour faciliter vos démarches",
    icon: "🛠️",
    services: [
      {
        id: "assistance",
        title: "Assistance 24/7",
        description: "Support multilingue et aide personnalisée",
        icon: "💬",
        features: [
          "Chat en direct",
          "Support téléphonique",
          "Base de connaissances",
          "Vidéos tutorielles",
        ],
        timeline: "Immédiat",
      },
      {
        id: "tracking",
        title: "Suivi de demande",
        description: "Suivez l'état de votre demande en temps réel",
        icon: "🔍",
        features: [
          "Notifications en temps réel",
          "Timeline détaillée",
          "Chat avec l'agent",
          "Historique complet",
        ],
      },
      {
        id: "verification",
        title: "Vérification de Documents",
        description: "Authentification et validation de vos certificats",
        icon: "✅",
        features: [
          "Scan QR Code",
          "Vérification blockchain",
          "Historique des validations",
          "Rapport détaillé",
        ],
        timeline: "< 5min",
      },
      {
        id: "analytics",
        title: "Statistiques & Rapports",
        description: "Suivez et analysez vos démarches",
        icon: "📊",
        features: [
          "Tableaux de bord",
          "Export de données",
          "Alertes personnalisées",
          "Rapports périodiques",
        ],
        comingSoon: true,
      },
      {
        id: "api-integrations",
        title: "API & Intégrations",
        description: "Connectez vos systèmes existants à notre plateforme",
        icon: "🔌",
        features: [
          "API RESTful",
          "Intégrations avec des systèmes existants",
          "Automatisation des démarches",
        ],
        comingSoon: true,
      },
    ],
  },
];

// Animations réutilisables
const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 },
};

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 xl:py-24">
        {/* En-tête */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12 md:mb-16 xl:mb-20"
        >
          <h1 className="text-3xl md:text-4xl xl:text-5xl font-bold bg-gradient-to-r from-[#004D40] via-[#00796B] to-[#009688] bg-clip-text text-transparent mb-4 md:mb-6">
            Nos Services
          </h1>
          <p className="text-neutral-600 max-w-2xl mx-auto text-sm md:text-base xl:text-lg">
            Une suite complète de services numériques pour moderniser vos
            démarches administratives
          </p>
        </motion.div>

        {/* Catégories de services */}
        {serviceCategories.map((category) => (
          <motion.div
            key={category.id}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mb-16 last:mb-0"
          >
            {/* En-tête de catégorie */}
            <div className="flex items-center gap-4 mb-8">
              <div className="w-12 h-12 md:w-14 md:h-14 flex items-center justify-center rounded-xl bg-gradient-to-br from-accent-primary/10 to-accent-secondary/10">
                <span className="text-2xl md:text-3xl">{category.icon}</span>
              </div>
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-neutral-900">
                  {category.title}
                </h2>
                <p className="text-neutral-600 text-sm md:text-base">
                  {category.description}
                </p>
              </div>
            </div>

            {/* Grille de services */}
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="show"
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 xl:gap-8"
            >
              {category.services.map((service) => (
                <motion.div
                  key={service.id}
                  variants={itemVariants}
                  className="relative group"
                >
                  {/* Effet de brillance */}
                  <div className="absolute inset-0 bg-gradient-to-r from-accent-primary/5 to-accent-secondary/5 rounded-xl md:rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500" />

                  {/* Carte */}
                  <div className="relative p-5 md:p-6 xl:p-8 rounded-xl md:rounded-2xl bg-white/80 backdrop-blur-sm border border-neutral-200/60 shadow-md hover:shadow-xl transition-all duration-300 group-hover:-translate-y-1">
                    <div className="flex items-start gap-4 mb-4 md:mb-6">
                      <div className="shrink-0 w-12 h-12 md:w-14 md:h-14 flex items-center justify-center rounded-xl bg-gradient-to-br from-accent-primary/10 to-accent-secondary/10">
                        <span className="text-2xl md:text-3xl">
                          {service.icon}
                        </span>
                      </div>
                      <div>
                        <div className="flex items-center gap-2 flex-wrap">
                          <h3 className="text-lg md:text-xl font-semibold text-neutral-900">
                            {service.title}
                          </h3>
                          {service.comingSoon && (
                            <span className="px-2 py-0.5 text-xs font-medium bg-accent-primary/10 text-accent-primary rounded-full">
                              Bientôt
                            </span>
                          )}
                          {service.timeline && (
                            <span className="px-2 py-0.5 text-xs font-medium bg-accent-secondary/10 text-accent-secondary rounded-full">
                              {service.timeline}
                            </span>
                          )}
                        </div>
                        <p className="text-sm md:text-base text-neutral-600 mt-1">
                          {service.description}
                        </p>
                      </div>
                    </div>

                    {/* Fonctionnalités */}
                    <div className="space-y-2">
                      {service.features.map((feature, idx) => (
                        <motion.div
                          key={idx}
                          initial={{ opacity: 0.8 }}
                          whileHover={{ opacity: 1 }}
                          className="flex items-center gap-2 p-2 rounded-lg hover:bg-accent-primary/5 transition-all duration-300"
                        >
                          <div className="w-1.5 h-1.5 rounded-full bg-accent-primary/60" />
                          <span className="text-sm md:text-base text-neutral-700">
                            {feature}
                          </span>
                        </motion.div>
                      ))}
                    </div>

                    {/* Bordure décorative */}
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-accent-primary/20 to-accent-secondary/20 rounded-b-xl md:rounded-b-2xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out" />
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
