export const theme = {
  colors: {
    primary: {
      red: {
        500: '#FF6B6B',  // Rouge principal
      },
      yellow: {
        500: '#FCC419',  // Jaune principal
      },
      green: {
        500: '#51CF66',  // Vert principal
      }
    },
    neutral: {
      50: 'hsl(var(--neutral-50))',
      100: 'hsl(var(--neutral-100))',
      200: 'hsl(var(--neutral-200))',
      300: 'hsl(var(--neutral-300))',
      400: 'hsl(var(--neutral-400))',
      500: 'hsl(var(--neutral-500))',
      600: 'hsl(var(--neutral-600))',
      700: 'hsl(var(--neutral-700))',
      800: 'hsl(var(--neutral-800))',
      900: 'hsl(var(--neutral-900))',
    },
    semantic: {
      success: 'hsl(var(--success))',
      warning: 'hsl(var(--warning))',
      error: 'hsl(var(--error))',
      info: 'hsl(var(--info))',
    }
  },

  typography: {
    fonts: {
      sans: 'var(--font-inter)',
      mono: 'var(--font-jetbrains-mono)',
    }
  },

  effects: {
    gradients: {
      primary: 'linear-gradient(135deg, var(--primary-red) 0%, var(--primary-yellow) 50%, var(--primary-green) 100%)',
    },
    animations: {
      fadeIn: 'fadeIn 0.5s ease-in-out',
      slideUp: 'slideUp 0.5s ease-in-out',
      pulse: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      pulseSlow: 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
    }
  }
} as const;
