import { createAdminClient } from "./appwrite";
import { BUCKETS } from "./constant";

export const DOCUMENTS_BUCKET_ID = BUCKETS.DOCUMENTS;
export const SIGNATURES_BUCKET_ID = BUCKETS.SIGNATURES;

// Type pour la configuration des buckets
type BucketConfig = {
  name: string;
  // permissions: string[];
  // fileSecurity: boolean;
  // enabled: boolean;
  // maximumFileSize: number;
  // allowedFileExtensions: string[];
  // allowedMimeTypes: string[];
};

const STORAGE_STRUCTURE: Record<string, BucketConfig> = {
  [BUCKETS.DOCUMENTS]: {
    name: "Citizen Documents",
    // permissions: ["role:all"],
    // fileSecurity: true,
    // enabled: true,
    // maximumFileSize: 10 * 1024 * 1024, // 10MB
    // allowedFileExtensions: ["jpg", "jpeg", "png", "pdf"],
    // allowedMimeTypes: ["image/jpeg", "image/jpg", "image/png", "application/pdf"],
  },
};

export async function initializeStorage(force: boolean = false) {
  console.log("🚀 Starting storage initialization...");
  const { storage } = await createAdminClient();

  try {
    for (const [bucketId, config] of Object.entries(STORAGE_STRUCTURE)) {
      try {
        await storage.createBucket(
          bucketId,
          config.name
          // [...config.permissions],
          // config.fileSecurity,
          // config.enabled,
          // config.maximumFileSize,
          // [...config.allowedMimeTypes]
        );
        console.log(`✓ Bucket ${config.name} created successfully`);
      } catch (error: any) {
        if (error.code !== 409 || force) {
          console.error(`× Error creating bucket ${config.name}:`, error);
          throw error;
        }
        console.log(`⚠ Bucket ${config.name} already exists`);
      }
    }

    console.log("\n✅ Storage initialization completed successfully");
  } catch (error) {
    console.error("\n❌ Error during storage initialization:", error);
    throw error;
  }
}
