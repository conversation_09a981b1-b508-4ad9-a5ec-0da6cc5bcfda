"use client";

import { motion } from "framer-motion";

interface SectionTitleProps {
  title: string;
  subtitle?: string;
  centered?: boolean;
  delay?: number;
}

export function SectionTitle({
  title,
  subtitle,
  centered = false,
  delay = 0,
}: SectionTitleProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      className={`space-y-4 mb-12 ${centered ? "text-center" : ""}`}
    >
      <h2 className="text-3xl font-bold bg-gradient-to-r from-[#004D40] via-[#00796B] to-[#009688] bg-clip-text text-transparent">
        {title}
      </h2>
      {subtitle && <p className="text-neutral-600 max-w-2xl">{subtitle}</p>}
    </motion.div>
  );
}
