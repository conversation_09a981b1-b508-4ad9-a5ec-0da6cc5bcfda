import { siteConfig } from "@/config/seo";
import { Metadata } from "next";

interface GenerateMetadataProps {
  title?: string;
  description?: string;
  image?: string;
  noIndex?: boolean;
  keywords?: string[];
  path?: string;
  additionalMetadata?: Partial<Metadata>;
}

export function generateMetadata({
  title,
  description,
  image,
  noIndex = false,
  keywords = [],
  path = "",
  additionalMetadata = {},
}: GenerateMetadataProps): Metadata {
  const metaTitle = title ? `${title} | ${siteConfig.name}` : siteConfig.name;
  const metaDescription = description || siteConfig.description;
  const metaImage = image || siteConfig.ogImage;
  const url = `${siteConfig.url}${path}`;

  const baseMetadata: Metadata = {
    title: {
      default: metaTitle,
      template: `%s | ${siteConfig.name}`,
    },
    description: metaDescription,
    keywords: [...siteConfig.keywords, ...keywords],
    authors: [{ name: siteConfig.creator, url: siteConfig.url }],
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url,
      siteName: siteConfig.name,
      images: [
        {
          url: metaImage,
          width: 1200,
          height: 630,
          alt: metaTitle,
        },
      ],
      locale: "fr_GN",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: metaTitle,
      description: metaDescription,
      images: [metaImage],
      creator: siteConfig.links.twitter,
    },
    robots: {
      index: !noIndex,
      follow: !noIndex,
      googleBot: {
        index: !noIndex,
        follow: !noIndex,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    alternates: {
      canonical: url,
    },
    metadataBase: new URL(siteConfig.url),
  };

  // Fusionner les métadonnées de base avec les métadonnées additionnelles
  return {
    ...baseMetadata,
    ...additionalMetadata,
  };
}
