"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Baby } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { declareBirth } from "@/actions/birth/declare";
import { But<PERSON> } from "@/components/ui/button";
import { FileUpload } from "@/components/ui/file-upload";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { FormSection } from "@/components/ui/form-section";
import { Input } from "@/components/ui/input";
import { Loader } from "@/components/ui/loader";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import {
  DeclarationNaissanceFormData,
  DeclarationNaissanceSchema,
} from "@/schemas/birth";

export function DeclarationNaissanceForm() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<DeclarationNaissanceFormData>({
    resolver: zodResolver(DeclarationNaissanceSchema),
    mode: "onChange",
    defaultValues: {
      nomEnfant: "",
      prenomEnfant: "",
      dateNaissance: "",
      lieuNaissance: "",
      sexe: undefined,
      nomPere: "",
      prenomPere: "",
      professionPere: "",
      nationalitePere: "",
      nomMere: "",
      prenomMere: "",
      professionMere: "",
      nationaliteMere: "",
      nomDeclarant: "",
      prenomDeclarant: "",
      qualiteDeclarant: "",
      telephoneDeclarant: "",
      certificatNaissance: undefined,
      consentement: false,
    },
  });

  const onSubmit = async (data: DeclarationNaissanceFormData) => {
    try {
      console.log("Début de la soumission", data);
      setIsLoading(true);

      // Vérifier que le fichier est bien présent
      if (!data.certificatNaissance) {
        toast({
          title: "Erreur",
          description: "Le certificat de naissance est obligatoire",
          variant: "destructive",
        });
        return;
      }

      const result = await declareBirth(data);

      if (!result.success) {
        toast({
          title: "Erreur",
          description:
            result.error || "Une erreur est survenue lors de l'enregistrement.",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Déclaration enregistrée",
        description:
          "Votre déclaration de naissance a été enregistrée avec succès.",
        variant: "success",
      });

      form.reset();
    } catch (error) {
      console.error("Erreur lors de la soumission:", error);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'enregistrement.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-8 p-6"
        noValidate
      >
        <FormSection
          title="Informations sur l'enfant"
          description="Renseignez les informations de l'enfant"
          className="bg-gradient-to-br from-emerald-50/50 to-transparent rounded-xl p-6"
        >
          <FormField
            control={form.control}
            name="nomEnfant"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nom</FormLabel>
                <FormControl>
                  <Input placeholder="Nom de l'enfant" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="prenomEnfant"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Prénom</FormLabel>
                <FormControl>
                  <Input placeholder="Prénom de l'enfant" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="dateNaissance"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date de naissance</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lieuNaissance"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Lieu de naissance</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Lieu de naissance de l'enfant"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="sexe"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Sexe</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionnez le sexe" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="M">Masculin</SelectItem>
                    <SelectItem value="F">Féminin</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </FormSection>

        <FormSection
          title="Informations sur les parents"
          description="Renseignez les informations des parents"
          className="bg-gradient-to-br from-teal-50/50 to-transparent rounded-xl p-6"
        >
          {/* Section père */}
          <div className="grid gap-4 p-4 bg-white/50 rounded-lg border border-teal-100/50">
            <h4 className="text-sm font-semibold text-teal-800">
              Informations du père
            </h4>
            <div className="grid gap-4 sm:grid-cols-2">
              <FormField
                control={form.control}
                name="nomPere"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nom</FormLabel>
                    <FormControl>
                      <Input placeholder="Nom du père" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="prenomPere"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Prénom</FormLabel>
                    <FormControl>
                      <Input placeholder="Prénom du père" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="professionPere"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Profession</FormLabel>
                    <FormControl>
                      <Input placeholder="Profession du père" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="nationalitePere"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nationalité</FormLabel>
                    <FormControl>
                      <Input placeholder="Nationalité du père" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Section mère */}
          <div className="grid gap-4 p-4 bg-white/50 rounded-lg border border-teal-100/50">
            <h4 className="text-sm font-semibold text-teal-800">
              Informations de la mère
            </h4>
            <div className="grid gap-4 sm:grid-cols-2">
              <FormField
                control={form.control}
                name="nomMere"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nom</FormLabel>
                    <FormControl>
                      <Input placeholder="Nom de la mère" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="prenomMere"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Prénom</FormLabel>
                    <FormControl>
                      <Input placeholder="Prénom de la mère" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="professionMere"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Profession</FormLabel>
                    <FormControl>
                      <Input placeholder="Profession de la mère" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="nationaliteMere"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nationalité</FormLabel>
                    <FormControl>
                      <Input placeholder="Nationalité de la mère" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </FormSection>

        <FormSection
          title="Informations sur le déclarant"
          description="Renseignez vos informations en tant que déclarant"
          className="bg-gradient-to-br from-cyan-50/50 to-transparent rounded-xl p-6"
        >
          <div className="grid gap-4 sm:grid-cols-2">
            <FormField
              control={form.control}
              name="nomDeclarant"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom</FormLabel>
                  <FormControl>
                    <Input placeholder="Votre nom" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="prenomDeclarant"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Prénom</FormLabel>
                  <FormControl>
                    <Input placeholder="Votre prénom" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="qualiteDeclarant"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Qualité</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Votre qualité" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="pere">Père</SelectItem>
                      <SelectItem value="mere">Mère</SelectItem>
                      <SelectItem value="autre">Autre</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="telephoneDeclarant"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Téléphone</FormLabel>
                  <FormControl>
                    <Input
                      type="tel"
                      placeholder="Votre numéro de téléphone"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </FormSection>

        <FormSection
          title="Documents justificatifs"
          description="Joignez les documents requis"
          className="bg-gradient-to-br from-blue-50/50 to-transparent rounded-xl p-6"
        >
          <FormField
            control={form.control}
            name="certificatNaissance"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Certificat de naissance</FormLabel>
                <FormControl>
                  <FileUpload
                    endpoint="certificatNaissance"
                    value={field.value}
                    onChange={field.onChange}
                    options={{
                      maxSize: 5 * 1024 * 1024,
                      acceptedTypes: [
                        "image/jpeg",
                        "image/jpg",
                        "image/png",
                        "application/pdf",
                      ],
                      label: "Ajoutez le certificat de naissance",
                      description: "Format JPG, PNG ou PDF - Max 5MB",
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </FormSection>

        <FormSection title="Consentement">
          <FormField
            control={form.control}
            name="consentement"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <div className="flex items-center space-x-2">
                  <FormControl>
                    <input
                      type="checkbox"
                      checked={field.value}
                      onChange={field.onChange}
                      className="h-4 w-4 rounded border-neutral-300 text-emerald-600 focus:ring-emerald-600"
                    />
                  </FormControl>
                  <FormLabel className="text-sm font-normal">
                    Je certifie sur l'honneur l'exactitude des informations
                    fournies
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </FormSection>

        <div className="flex justify-end mt-8">
          <Button
            type="submit"
            disabled={isLoading || !form.formState.isValid}
            className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
            onClick={(e) => {
              console.log("État du formulaire:", {
                isValid: form.formState.isValid,
                errors: form.formState.errors,
                isDirty: form.formState.isDirty,
                values: form.getValues(),
              });
            }}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Loader size="xs" variant="ghost" className="text-white" />
                <span>Enregistrement en cours...</span>
              </div>
            ) : (
              <>
                <Baby className="mr-2 h-4 w-4" />
                Enregistrer la déclaration
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
