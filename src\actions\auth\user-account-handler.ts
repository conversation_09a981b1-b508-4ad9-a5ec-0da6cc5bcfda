"use server";

import { createAdminClient } from "@/lib/server/appwrite";
import { DATABASE_ID, CITIZENS_COLLECTION_ID } from "@/lib/server/database";
import { ID, Query } from "node-appwrite";

// Type pour le résultat de la vérification d'utilisateur
type UserExistenceCheck = {
  exists: boolean;
  error?: string;
};

/**
 * Vérifie si un utilisateur existe déjà par un champ donné
 * @param field Champ à vérifier (email ou telephone)
 * @param value Valeur à rechercher
 * @returns Résultat de la vérification
 */
export async function checkUserExists(
  field: "email" | "telephone",
  value: string
): Promise<UserExistenceCheck> {
  try {
    const { databases } = await createAdminClient();

    // Requête pour vérifier l'existence de l'utilisateur
    const { total } = await databases.listDocuments(
      DATABASE_ID,
      CITIZENS_COLLECTION_ID,
      [Query.equal(field, value)]
    );

    if (total > 0) {
      return {
        exists: true,
        error:
          field === "email"
            ? "Un compte avec cet email existe déjà."
            : "Un compte avec ce numéro de téléphone existe déjà.",
      };
    }

    return { exists: false };
  } catch (error) {
    console.error(
      `Erreur lors de la vérification de l'utilisateur par ${field}:`,
      error
    );
    return {
      exists: false,
      error: "Une erreur est survenue lors de la vérification.",
    };
  }
}

/**
 * Crée un nouveau compte utilisateur
 * @param data Données de l'utilisateur
 * @returns Résultat de la création de compte
 */
export async function createUserAccount(data: {
  email: string;
  motDePasse: string;
  nom: string;
  prenom: string;
}) {
  try {
    const { account } = await createAdminClient();

    // 1. Création du compte Appwrite
    const user = await account.create(
      ID.unique(),
      data.email,
      data.motDePasse,
      `${data.prenom} ${data.nom}`
    );

    return {
      success: true,
      userId: user.$id,
    };
  } catch (error) {
    console.error("Erreur lors de la création du compte:", error);
    return {
      success: false,
      error: "Impossible de créer le compte utilisateur.",
    };
  }
}
