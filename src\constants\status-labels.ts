import { CERTIFICATE_STATUS, STATUS } from "@/actions/auth/constants";

type BadgeVariant =
  | "default"
  | "success"
  | "warning"
  | "error"
  | "secondary"
  | "outline"
  | "info"
  | "primary";

export const CERTIFICATE_STATUS_LABELS: Record<CERTIFICATE_STATUS, string> = {
  [CERTIFICATE_STATUS.DRAFT]: "Brouillon",
  [CERTIFICATE_STATUS.SUBMITTED]: "<PERSON>umis",
  [CERTIFICATE_STATUS.PENDING]: "En attente",
  [CERTIFICATE_STATUS.VERIFIED]: "Vérifi<PERSON>",
  [CERTIFICATE_STATUS.APPROVED]: "Approuvé",
  [CERTIFICATE_STATUS.READY]: "Prêt pour signature",
  [CERTIFICATE_STATUS.SIGNED]: "Signé",
  [CERTIFICATE_STATUS.DELIVERED]: "Déliv<PERSON>",
  [CERTIFICATE_STATUS.REJECTED]: "Rejeté",
  [CERTIFICATE_STATUS.EXPIRED]: "Expiré",
} as const;

export const CERTIFICATE_STATUS_COLORS: Record<
  CERTIFICATE_STATUS,
  BadgeVariant
> = {
  [CERTIFICATE_STATUS.DRAFT]: "secondary",
  [CERTIFICATE_STATUS.SUBMITTED]: "info",
  [CERTIFICATE_STATUS.PENDING]: "warning",
  [CERTIFICATE_STATUS.VERIFIED]: "primary",
  [CERTIFICATE_STATUS.APPROVED]: "success",
  [CERTIFICATE_STATUS.READY]: "info",
  [CERTIFICATE_STATUS.SIGNED]: "success",
  [CERTIFICATE_STATUS.DELIVERED]: "success",
  [CERTIFICATE_STATUS.REJECTED]: "error",
  [CERTIFICATE_STATUS.EXPIRED]: "secondary",
} as const;

export const USER_STATUS_LABELS: Record<STATUS, string> = {
  [STATUS.ACTIVE]: "Actif",
  [STATUS.PENDING]: "En attente",
  [STATUS.INACTIVE]: "Inactif",
  [STATUS.BLOCKED]: "Bloqué",
} as const;

export const USER_STATUS_COLORS: Record<STATUS, BadgeVariant> = {
  [STATUS.ACTIVE]: "success",
  [STATUS.PENDING]: "warning",
  [STATUS.INACTIVE]: "secondary",
  [STATUS.BLOCKED]: "error",
} as const;
