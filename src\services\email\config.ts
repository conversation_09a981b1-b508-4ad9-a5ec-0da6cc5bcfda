import { createTransport } from "nodemailer";

export const emailConfig = {
  from: process.env.SMTP_FROM || "<EMAIL>",
  smtp: {
    host: process.env.SMTP_HOST || "smtp.gmail.com",
    port: parseInt(process.env.SMTP_PORT || "587"),
    secure: process.env.SMTP_SECURE === "true",
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  },
  templates: {
    contact: {
      subject: "Nouvelle demande de contact - {subject}",
      replyTo: "{email}",
      adminEmail: process.env.ADMIN_EMAIL || "<EMAIL>",
    },
  },
};

export const transporter = createTransport(emailConfig.smtp);
