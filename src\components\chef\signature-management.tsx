"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Edit, Save, X, FileSignature, Clock, Hash } from "lucide-react";
import { SignaturePadComponent } from "@/components/certificates/signature-pad";

interface StoredSignature {
  $id: string;
  signatureData: string;
  signatureType: "digital" | "handwritten";
  createdAt: string;
  updatedAt: string;
  lastUsedAt?: string;
  usageCount: number;
}

export function SignatureManagement() {
  const [signature, setSignature] = useState<StoredSignature | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadSignature();
  }, []);

  const loadSignature = async () => {
    try {
      setIsLoading(true);
      const { getChefSignatureAction } = await import("@/actions/chef/signature-management");
      const result = await getChefSignatureAction();

      if (result.success) {
        setSignature(result.signature);
      } else {
        console.error("Erreur lors du chargement de la signature:", result.error);
      }
    } catch (error) {
      console.error("Erreur lors du chargement de la signature:", error);
      toast({
        title: "Erreur",
        description: "Impossible de charger votre signature",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveSignature = async (signatureData: string) => {
    try {
      setIsSaving(true);
      const { saveChefSignatureAction } = await import("@/actions/chef/signature-management");
      
      const result = await saveChefSignatureAction({
        signatureData,
        signatureType: "digital",
      });

      if (result.success) {
        toast({
          title: "Signature sauvegardée",
          description: "Votre signature a été sauvegardée avec succès",
          variant: "success",
        });
        setIsEditing(false);
        await loadSignature(); // Recharger les données
      } else {
        throw new Error(result.error || "Erreur lors de la sauvegarde");
      }
    } catch (error: any) {
      console.error("Erreur lors de la sauvegarde:", error);
      toast({
        title: "Erreur",
        description: error?.message || "Impossible de sauvegarder la signature",
        variant: "error",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Chargement de votre signature...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileSignature className="h-5 w-5" />
            Gestion de votre signature
          </CardTitle>
          <CardDescription>
            Gérez votre signature numérique pour signer les certificats de résidence
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {signature ? (
            <div className="space-y-4">
              {/* Informations sur la signature */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">
                    {signature.signatureType === "digital" ? "Numérique" : "Manuscrite"}
                  </Badge>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Hash className="h-4 w-4" />
                  <span>Utilisée {signature.usageCount} fois</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  <span>
                    {signature.lastUsedAt 
                      ? `Dernière utilisation: ${formatDate(signature.lastUsedAt)}`
                      : "Jamais utilisée"
                    }
                  </span>
                </div>
              </div>

              <Separator />

              {/* Aperçu de la signature */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Aperçu de votre signature</h4>
                <div className="border rounded-lg p-4 bg-gray-50">
                  <img 
                    src={signature.signatureData} 
                    alt="Votre signature" 
                    className="max-h-24 mx-auto"
                    style={{ maxWidth: "300px" }}
                  />
                </div>
              </div>

              <Separator />

              {/* Actions */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(true)}
                  disabled={isEditing}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Modifier la signature
                </Button>
                <div className="text-xs text-muted-foreground">
                  Créée le {formatDate(signature.createdAt)}
                  {signature.updatedAt !== signature.createdAt && (
                    <span> • Modifiée le {formatDate(signature.updatedAt)}</span>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <FileSignature className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Aucune signature enregistrée</h3>
              <p className="text-muted-foreground mb-4">
                Créez votre signature numérique pour signer les certificats plus rapidement
              </p>
              <Button onClick={() => setIsEditing(true)}>
                <FileSignature className="h-4 w-4 mr-2" />
                Créer ma signature
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Éditeur de signature */}
      {isEditing && (
        <Card>
          <CardHeader>
            <CardTitle>
              {signature ? "Modifier votre signature" : "Créer votre signature"}
            </CardTitle>
            <CardDescription>
              Dessinez votre signature dans l'espace ci-dessous
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <SignaturePadComponent
                id="signature-management"
                onSave={handleSaveSignature}
                title=""
                description=""
                saveButtonText={isSaving ? "Sauvegarde..." : "Sauvegarder la signature"}
                disabled={isSaving}
              />
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                  disabled={isSaving}
                >
                  <X className="h-4 w-4 mr-2" />
                  Annuler
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
