"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  FileText,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Ban,
  Loader2,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useCertificates } from "@/hooks/use-certificates";
import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { cn } from "@/lib/utils/cn";
import { CertificateFilters } from "./certificate-filters";
import { StatusBadge } from "@/components/certificates/status-badge";

// Mise à jour des transitions de statut autorisées selon le workflow complet
const statusTransitions: Record<CERTIFICATE_STATUS, CERTIFICATE_STATUS[]> = {
  [CERTIFICATE_STATUS.DRAFT]: [CERTIFICATE_STATUS.SUBMITTED],
  [CERTIFICATE_STATUS.SUBMITTED]: [
    CERTIFICATE_STATUS.PENDING,
    CERTIFICATE_STATUS.REJECTED,
  ],
  [CERTIFICATE_STATUS.PENDING]: [
    CERTIFICATE_STATUS.VERIFIED,
    CERTIFICATE_STATUS.REJECTED,
  ],
  [CERTIFICATE_STATUS.VERIFIED]: [
    CERTIFICATE_STATUS.APPROVED,
    CERTIFICATE_STATUS.REJECTED,
  ],
  [CERTIFICATE_STATUS.APPROVED]: [
    CERTIFICATE_STATUS.READY,
    CERTIFICATE_STATUS.REJECTED,
  ],
  [CERTIFICATE_STATUS.READY]: [
    CERTIFICATE_STATUS.SIGNED,
    CERTIFICATE_STATUS.REJECTED,
  ],
  [CERTIFICATE_STATUS.SIGNED]: [CERTIFICATE_STATUS.DELIVERED],
  [CERTIFICATE_STATUS.DELIVERED]: [CERTIFICATE_STATUS.EXPIRED],
  [CERTIFICATE_STATUS.REJECTED]: [CERTIFICATE_STATUS.DRAFT],
  [CERTIFICATE_STATUS.EXPIRED]: [], // État final
};

// Libellés des actions selon le statut cible
const actionLabels: Partial<
  Record<CERTIFICATE_STATUS, { icon: any; label: string; style: string }>
> = {
  [CERTIFICATE_STATUS.VERIFIED]: {
    icon: CheckCircle,
    label: "Vérifier",
    style: "text-blue-600 hover:text-blue-700 hover:bg-blue-50",
  },
  [CERTIFICATE_STATUS.APPROVED]: {
    icon: CheckCircle,
    label: "Approuver",
    style: "text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50",
  },
  [CERTIFICATE_STATUS.SIGNED]: {
    icon: CheckCircle,
    label: "Signer",
    style: "text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50",
  },
  [CERTIFICATE_STATUS.REJECTED]: {
    icon: XCircle,
    label: "Rejeter",
    style: "text-red-600 hover:text-red-700 hover:bg-red-50",
  },
  [CERTIFICATE_STATUS.DELIVERED]: {
    icon: FileText,
    label: "Délivrer",
    style: "text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50",
  },
};

type CertificateTableFilters = {
  status: CERTIFICATE_STATUS | undefined;
  search: string;
  page: number;
  limit: number;
};

export function CertificatesTable() {
  const [filters, setFilters] = useState<CertificateTableFilters>({
    status: undefined,
    search: "",
    page: 0,
    limit: 10,
  });

  const { certificates, pagination, isLoading, updateStatus } =
    useCertificates(filters);

  const handleStatusChange = async (
    certificateId: string,
    newStatus: CERTIFICATE_STATUS
  ) => {
    try {
      await updateStatus.mutate({ certificateId, status: newStatus });
    } catch (error) {
      console.error("Erreur lors du changement de statut:", error);
    }
  };

  const handlePageChange = (newPage: number) => {
    setFilters((prev) => ({ ...prev, page: newPage }));
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <CertificateFilters
          filters={filters}
          onFiltersChange={(newFilters) =>
            setFilters((prev) => ({ ...prev, ...newFilters }))
          }
        />
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="rounded-lg border border-neutral-200/60 overflow-hidden bg-white"
      >
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Référence</TableHead>
              <TableHead>Demandeur</TableHead>
              <TableHead>Quartier</TableHead>
              <TableHead>Statut</TableHead>
              <TableHead>Date de demande</TableHead>
              <TableHead>Dernière mise à jour</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  <div className="flex items-center justify-center">
                    <Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
                  </div>
                </TableCell>
              </TableRow>
            ) : !certificates?.length ? (
              <TableRow>
                <TableCell
                  colSpan={7}
                  className="h-24 text-center text-neutral-500"
                >
                  Aucun certificat trouvé
                </TableCell>
              </TableRow>
            ) : (
              certificates.map((certificate) => (
                <TableRow key={certificate.$id}>
                  <TableCell className="font-medium">
                    {certificate.reference}
                  </TableCell>
                  <TableCell>{certificate.userName}</TableCell>
                  <TableCell>{certificate.quartier}</TableCell>
                  <TableCell>
                    <StatusBadge
                      status={certificate.status}
                      timestamp={certificate.updatedAt}
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(certificate.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {new Date(certificate.updatedAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="h-8 w-8 p-0 hover:bg-neutral-100 transition-colors duration-200"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        align="end"
                        className="w-48 shadow-lg border-neutral-200/70 bg-white rounded-lg p-1.5"
                      >
                        {statusTransitions[certificate.status]?.map(
                          (newStatus) => {
                            const action = actionLabels[newStatus];
                            if (!action) return null;

                            const { icon: Icon, label, style } = action;

                            return (
                              <DropdownMenuItem
                                key={newStatus}
                                onClick={() =>
                                  handleStatusChange(certificate.$id, newStatus)
                                }
                                className={cn(
                                  "flex items-center px-3 py-2 text-sm rounded-md cursor-pointer transition-colors duration-200",
                                  style
                                )}
                              >
                                <Icon className="mr-2 h-4 w-4" />
                                {label}
                              </DropdownMenuItem>
                            );
                          }
                        )}

                        <DropdownMenuSeparator className="my-1.5 border-neutral-200/70" />
                        <DropdownMenuItem className="flex items-center px-3 py-2 text-sm rounded-md cursor-pointer hover:bg-neutral-100 transition-colors duration-200">
                          <FileText className="mr-2 h-4 w-4" />
                          Voir les détails
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>

        {/* Pagination */}
        {pagination && (
          <div className="flex items-center justify-between border-t border-neutral-200/60 px-6 py-4">
            <div className="flex-1 text-sm text-neutral-500">
              {pagination.total > 0 ? (
                <p>
                  Affichage de{" "}
                  <span className="font-medium">
                    {pagination.page * pagination.limit + 1}
                  </span>{" "}
                  à{" "}
                  <span className="font-medium">
                    {Math.min(
                      (pagination.page + 1) * pagination.limit,
                      pagination.total
                    )}
                  </span>{" "}
                  sur <span className="font-medium">{pagination.total}</span>{" "}
                  certificats
                </p>
              ) : null}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page <= 0 || isLoading}
              >
                Précédent
              </Button>
              <div className="flex items-center gap-1 text-sm font-medium">
                <span className="px-3 py-1 rounded-md bg-neutral-100">
                  {pagination.page + 1}
                </span>
                <span className="text-neutral-400">
                  sur {Math.ceil(pagination.total / pagination.limit)}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={
                  pagination.page >=
                    Math.ceil(pagination.total / pagination.limit) - 1 ||
                  isLoading
                }
              >
                Suivant
              </Button>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
}
