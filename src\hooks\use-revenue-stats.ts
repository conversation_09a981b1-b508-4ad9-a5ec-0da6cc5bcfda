"use client";

import { useEffect, useState } from "react";

interface RevenueStats {
  totalRevenue: number;
  totalDistributions: number;
  platformShare: number;
  communeShare: number;
  quartierShare: number;
  partnerShare: number;
  payrollShare: number;
  averageAmount: number;
  distributionsByDay: Array<{
    date: string;
    amount: number;
    count: number;
  }>;
  topQuartiers: Array<{
    quartier: string;
    amount: number;
    count: number;
  }>;
}

interface UseRevenueStatsOptions {
  startDate?: string;
  endDate?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useRevenueStats(options: UseRevenueStatsOptions = {}) {
  const [stats, setStats] = useState<RevenueStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const {
    startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 jours par défaut
    endDate = new Date().toISOString(),
    autoRefresh = false,
    refreshInterval = 60000, // 1 minute
  } = options;

  const fetchStats = async () => {
    try {
      setError(null);
      const { getRevenueStatisticsAction } = await import(
        "@/actions/admin/revenue"
      );
      const result = await getRevenueStatisticsAction({
        startDate,
        endDate,
      });

      if (result.success && result.statistics) {
        // Adapter les données retournées par getRevenueSummary au format RevenueStats
        const summary = result.statistics;
        const adaptedStats: RevenueStats = {
          totalRevenue: summary.totalRevenue,
          totalDistributions: summary.totalDistributions,
          platformShare: summary.platformTotal,
          communeShare: summary.communeTotal,
          quartierShare: summary.quartierTotal,
          partnerShare: summary.partnerTotal,
          payrollShare: summary.payrollTotal,
          averageAmount:
            summary.totalDistributions > 0
              ? summary.totalRevenue / summary.totalDistributions
              : 0,
          distributionsByDay: [], // TODO: Implémenter si nécessaire
          topQuartiers: [], // TODO: Implémenter si nécessaire
        };
        setStats(adaptedStats);
      } else {
        throw new Error(
          result.error || "Erreur lors de la récupération des statistiques"
        );
      }
    } catch (err: any) {
      console.error(
        "Erreur lors de la récupération des statistiques de revenus:",
        err
      );
      setError(
        err.message || "Erreur lors de la récupération des statistiques"
      );
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, [startDate, endDate]);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchStats, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const refresh = () => {
    setIsLoading(true);
    fetchStats();
  };

  return {
    stats,
    isLoading,
    error,
    refresh,
  };
}

/**
 * Hook pour les statistiques de revenus en temps réel (pour les admins)
 */
export function useRealtimeRevenueStats() {
  return useRevenueStats({
    autoRefresh: true,
    refreshInterval: 30000, // 30 secondes
  });
}

/**
 * Hook pour les statistiques de revenus mensuelles
 */
export function useMonthlyRevenueStats(year?: number, month?: number) {
  const currentDate = new Date();
  const targetYear = year || currentDate.getFullYear();
  const targetMonth = month || currentDate.getMonth();

  const startDate = new Date(targetYear, targetMonth, 1).toISOString();
  const endDate = new Date(
    targetYear,
    targetMonth + 1,
    0,
    23,
    59,
    59
  ).toISOString();

  return useRevenueStats({
    startDate,
    endDate,
  });
}

/**
 * Hook pour les statistiques de revenus annuelles
 */
export function useYearlyRevenueStats(year?: number) {
  const targetYear = year || new Date().getFullYear();

  const startDate = new Date(targetYear, 0, 1).toISOString();
  const endDate = new Date(targetYear, 11, 31, 23, 59, 59).toISOString();

  return useRevenueStats({
    startDate,
    endDate,
  });
}

/**
 * Hook pour comparer les revenus entre deux périodes
 */
export function useRevenueComparison(
  currentPeriod: { startDate: string; endDate: string },
  previousPeriod: { startDate: string; endDate: string }
) {
  const [currentStats, setCurrentStats] = useState<RevenueStats | null>(null);
  const [previousStats, setPreviousStats] = useState<RevenueStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchComparison = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const { getRevenueStatisticsAction } = await import(
          "@/actions/admin/revenue"
        );
        const [currentResult, previousResult] = await Promise.all([
          getRevenueStatisticsAction(currentPeriod),
          getRevenueStatisticsAction(previousPeriod),
        ]);

        if (currentResult.success && previousResult.success) {
          // Adapter les données pour les deux périodes
          const adaptCurrentStats = (summary: any): RevenueStats => ({
            totalRevenue: summary.totalRevenue,
            totalDistributions: summary.totalDistributions,
            platformShare: summary.platformTotal,
            communeShare: summary.communeTotal,
            quartierShare: summary.quartierTotal,
            partnerShare: summary.partnerTotal,
            payrollShare: summary.payrollTotal,
            averageAmount:
              summary.totalDistributions > 0
                ? summary.totalRevenue / summary.totalDistributions
                : 0,
            distributionsByDay: [],
            topQuartiers: [],
          });

          setCurrentStats(adaptCurrentStats(currentResult.statistics));
          setPreviousStats(adaptCurrentStats(previousResult.statistics));
        } else {
          throw new Error(
            "Erreur lors de la récupération des statistiques de comparaison"
          );
        }
      } catch (err: any) {
        console.error("Erreur lors de la comparaison des revenus:", err);
        setError(err.message || "Erreur lors de la comparaison");
      } finally {
        setIsLoading(false);
      }
    };

    fetchComparison();
  }, [
    currentPeriod.startDate,
    currentPeriod.endDate,
    previousPeriod.startDate,
    previousPeriod.endDate,
  ]);

  // Calculer les variations
  const getVariation = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  };

  const comparison =
    currentStats && previousStats
      ? {
          revenueVariation: getVariation(
            currentStats.totalRevenue,
            previousStats.totalRevenue
          ),
          distributionsVariation: getVariation(
            currentStats.totalDistributions,
            previousStats.totalDistributions
          ),
          averageAmountVariation: getVariation(
            currentStats.averageAmount,
            previousStats.averageAmount
          ),
        }
      : null;

  return {
    currentStats,
    previousStats,
    comparison,
    isLoading,
    error,
  };
}

/**
 * Hook pour les statistiques de revenus par quartier
 */
export function useQuartierRevenueStats(quartierId?: string) {
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchQuartierStats = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Pour l'instant, utiliser les statistiques globales
        // Dans une implémentation future, on pourrait filtrer par quartier
        const { getRevenueStatisticsAction } = await import(
          "@/actions/admin/revenue"
        );
        const result = await getRevenueStatisticsAction({
          startDate: new Date(
            Date.now() - 30 * 24 * 60 * 60 * 1000
          ).toISOString(),
          endDate: new Date().toISOString(),
        });

        if (result.success) {
          // Filtrer les données pour le quartier spécifique si nécessaire
          setStats(result.statistics);
        } else {
          throw new Error(
            "Erreur lors de la récupération des statistiques du quartier"
          );
        }
      } catch (err: any) {
        console.error(
          "Erreur lors de la récupération des statistiques du quartier:",
          err
        );
        setError(
          err.message || "Erreur lors de la récupération des statistiques"
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchQuartierStats();
  }, [quartierId]);

  return {
    stats,
    isLoading,
    error,
  };
}
