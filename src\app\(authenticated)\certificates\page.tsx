"use client";

import { JsonLd } from "@/components/seo/json-ld";
import { useToast } from "@/components/ui/use-toast";
import { useCertificateActions } from "@/hooks/use-certificate-actions";
import { TOAST_MESSAGES } from "@/services/toast-messages";
import { motion } from "framer-motion";
import {
  ArrowRight,
  Calendar,
  Download,
  Eye,
  Filter,
  MapPin,
  Plus,
  Search,
  UserCircle2,
} from "lucide-react";
import { useState } from "react";

const glassEffect = `
  backdrop-blur-xl bg-white/40
  border border-white/20
  shadow-[0_8px_32px_0_rgba(31,38,135,0.07)]
  hover:shadow-[0_8px_32px_0_rgba(31,38,135,0.1)]
  transition-all duration-500
`;

const glassCardEffect = `
  backdrop-blur-lg bg-white/30
  border border-white/20
  shadow-[0_8px_32px_0_rgba(31,38,135,0.05)]
  hover:shadow-[0_8px_32px_0_rgba(31,38,135,0.1)]
  hover:bg-white/40
  transition-all duration-500
`;

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15,
    },
  },
};

export default function CertificatesPage() {
  const { download } = useCertificateActions();
  const { toast } = useToast();
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = async (certificateId: string, reference: string) => {
    setIsDownloading(true);
    try {
      const response = await download(certificateId);

      if (response.success && response.documentUrl) {
        const link = document.createElement("a");
        link.target = "_blank";
        link.href = response.documentUrl;
        link.download = `certificat-${reference}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast(TOAST_MESSAGES.certificate.download.success);
      }
    } catch (error) {
      toast({
        ...TOAST_MESSAGES.certificate.download.error,
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const certificates = [
    { $id: "1", reference: "CERT-001" },
    { $id: "2", reference: "CERT-002" },
    // ...etc
  ];

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5 overflow-hidden">
      {/* Effets de fond sophistiqués */}
      <div className="fixed inset-0 -z-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(0,77,64,0.1),transparent_50%)]" />
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-[0.02] animate-pulse" />
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.05, 0.1, 0.05],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            repeatType: "reverse",
          }}
          className="absolute inset-0 bg-gradient-to-tr from-accent-primary/10 via-transparent to-accent-secondary/10"
        />
        <div className="absolute inset-0 backdrop-blur-[100px]" />
      </div>

      <div className="container mx-auto px-6 py-12 relative z-10">
        {/* En-tête avec effet glass morphism */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`${glassEffect} rounded-2xl p-8 mb-12 relative overflow-hidden`}
        >
          <div className="absolute -top-12 -right-12 w-48 h-48 bg-accent-primary/10 rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-12 -left-12 w-48 h-48 bg-accent-secondary/10 rounded-full blur-3xl animate-pulse" />

          <div className="relative z-10">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-4xl md:text-5xl font-sans font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r
                from-accent-primary via-accent-secondary to-accent-primary"
            >
              Mes Certificats
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-xl text-neutral-600 max-w-2xl"
            >
              Gérez vos certificats en toute simplicité avec notre plateforme
              moderne et sécurisée
            </motion.p>
          </div>
        </motion.div>

        {/* Barre d'actions */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className={`${glassEffect} rounded-2xl p-6 mb-12 relative overflow-hidden`}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 rounded-2xl" />

          <div className="relative z-10 flex flex-col md:flex-row gap-6">
            <motion.div variants={itemVariants} className="flex-1">
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-accent-primary/5 to-accent-secondary/5 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity" />
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Rechercher un certificat..."
                    className="w-full pl-12 pr-4 py-3 rounded-xl bg-white/50 border border-neutral-200/50
                      focus:outline-none focus:ring-2 focus:ring-accent-primary/20 focus:border-accent-primary/30
                      transition-all duration-300"
                  />
                </div>
              </div>
            </motion.div>

            <div className="flex gap-4">
              <motion.button
                variants={itemVariants}
                className={`${glassCardEffect} flex items-center gap-2 px-4 py-3 rounded-xl
                  hover:scale-[1.02] hover:-translate-y-0.5 active:scale-[0.98]`}
              >
                <Filter className="w-5 h-5 text-accent-primary/80" />
                <span className="text-neutral-700">Filtrer</span>
              </motion.button>

              <motion.button
                variants={itemVariants}
                className="flex items-center gap-2 px-6 py-3 rounded-xl bg-gradient-to-r
                  from-accent-primary to-accent-secondary text-white
                  hover:shadow-lg hover:shadow-accent-primary/20 hover:scale-[1.02] hover:-translate-y-0.5
                  active:scale-[0.98] transition-all duration-300"
              >
                <Plus className="w-5 h-5" />
                <span>Nouveau Certificat</span>
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Grille de certificats */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {certificates.map((certificate) => (
            <motion.div
              key={certificate.$id}
              variants={itemVariants}
              onHoverStart={() => setHoveredCard(certificate.$id)}
              onHoverEnd={() => setHoveredCard(null)}
              className={`${glassCardEffect} group relative rounded-2xl p-6 hover:-translate-y-1`}
            >
              {/* Effet de brillance au survol */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <div className="absolute inset-0 bg-gradient-to-r from-accent-primary/5 via-accent-secondary/5 to-accent-primary/5 rounded-2xl" />
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-2xl" />
              </div>

              {/* Badge de statut */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="absolute top-4 right-4"
              >
                <span
                  className="px-3 py-1 text-sm rounded-full bg-accent-primary/10 text-accent-primary font-medium
                  shadow-sm shadow-accent-primary/10"
                >
                  Actif
                </span>
              </motion.div>

              {/* En-tête du certificat */}
              <div className="relative mb-6">
                <h3
                  className="text-xl font-sans font-semibold mb-2 bg-gradient-to-r from-neutral-900 to-neutral-600
                  bg-clip-text text-transparent"
                >
                  Certificat #{certificate.$id}
                </h3>
                <div className="flex items-center gap-2 text-neutral-500">
                  <Calendar className="w-4 h-4" />
                  <span className="text-sm">
                    {new Date().toLocaleDateString()}
                  </span>
                </div>
              </div>

              {/* Informations du certificat */}
              <div className="relative space-y-4 mb-6">
                <div className="flex items-center gap-3 py-2 border-b border-neutral-200/50">
                  <UserCircle2 className="w-5 h-5 text-accent-primary/60" />
                  <span className="text-neutral-600">John Doe</span>
                </div>
                <div className="flex items-center gap-3 py-2 border-b border-neutral-200/50">
                  <MapPin className="w-5 h-5 text-accent-primary/60" />
                  <span className="text-neutral-600">Conakry</span>
                </div>
                <div className="flex items-center gap-3 py-2 border-b border-neutral-200/50">
                  <ArrowRight className="w-5 h-5 text-accent-primary/60" />
                  <span className="text-neutral-600">Résidence</span>
                </div>
              </div>

              {/* Actions du certificat */}
              <div className="relative flex gap-3">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="flex-1 flex items-center justify-center gap-2 py-2.5 rounded-xl
                    bg-accent-primary/10 text-accent-primary hover:bg-accent-primary/20 transition-colors"
                >
                  <Eye className="w-4 h-4" />
                  <span>Voir</span>
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() =>
                    handleDownload(certificate.$id, certificate.reference)
                  }
                  disabled={isDownloading}
                  className={`flex-1 flex items-center justify-center gap-2 py-2.5 rounded-xl
                    bg-neutral-100 text-neutral-700 hover:bg-neutral-200 transition-colors
                    ${isDownloading ? "opacity-50 cursor-not-allowed" : ""}
                    backdrop-blur-sm shadow-lg hover:shadow-xl`}
                >
                  <Download
                    className={`w-4 h-4 ${isDownloading ? "animate-spin" : ""}`}
                  />
                  <span>
                    {isDownloading ? "Téléchargement..." : "Télécharger"}
                  </span>
                </motion.button>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>

      <JsonLd
        type="WebPage"
        title="Certificats de Résidence"
        description="Consultez et gérez vos certificats de résidence en ligne"
        path="/certificates"
      />
    </div>
  );
}
