"use client";

import { motion } from "framer-motion";
import { FileCheck2, UserCheck2, Clock, AlertCircle } from "lucide-react";

const stats = [
  {
    title: "Demandes en attente",
    value: "12",
    subtitle: "+2 depuis hier",
    icon: Clock,
    trend: { value: 8, label: "depuis hier" },
    color: "from-amber-500 to-orange-600",
    shadowColor: "shadow-amber-500/20",
    delay: 0,
  },
  {
    title: "Demandes traitées",
    value: "48",
    subtitle: "+8 cette semaine",
    icon: FileCheck2,
    trend: { value: 12, label: "cette semaine" },
    color: "from-emerald-500 to-teal-600",
    shadowColor: "shadow-emerald-500/20",
    delay: 0.1,
  },
  {
    title: "Agents actifs",
    value: "7",
    subtitle: "+1 ce mois",
    icon: UserCheck2,
    trend: { value: 5, label: "ce mois" },
    color: "from-blue-500 to-indigo-600",
    shadowColor: "shadow-blue-500/20",
    delay: 0.2,
  },
  {
    title: "Demandes urgentes",
    value: "3",
    subtitle: "Nécessitent une attention immédiate",
    icon: AlertCircle,
    trend: { value: -2, label: "depuis hier" },
    color: "from-rose-500 to-red-600",
    shadowColor: "shadow-rose-500/20",
    delay: 0.3,
  },
];

export function ChefDashboardStats() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent">
          Statistiques du quartier
        </h2>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: stat.delay }}
            className="relative group"
          >
            <div
              className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${stat.color} opacity-[0.08] blur-sm
              group-hover:opacity-[0.12] group-hover:blur-md transition-all duration-300`}
            />

            <div
              className="relative h-full p-6 rounded-2xl bg-white/60 backdrop-blur-sm border border-neutral-200/60
              hover:bg-white/70 transition-colors duration-300"
            >
              <div className="flex items-center justify-between mb-4">
                <div
                  className={`p-3 rounded-xl bg-gradient-to-br ${stat.color} ${stat.shadowColor}
                  transform group-hover:scale-110 transition-transform duration-300`}
                >
                  <stat.icon className="w-5 h-5 text-white" />
                </div>
                {stat.trend && (
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: stat.delay + 0.2 }}
                    className={`flex items-center gap-1 px-2 py-1 rounded-full
                      ${
                        stat.trend.value > 0
                          ? "text-emerald-700 bg-emerald-50 border border-emerald-200/50"
                          : "text-rose-700 bg-rose-50 border border-rose-200/50"
                      }`}
                  >
                    <span className="text-xs font-medium">
                      {stat.trend.value > 0 ? "↑" : "↓"}{" "}
                      {Math.abs(stat.trend.value)}%
                    </span>
                  </motion.div>
                )}
              </div>

              <div className="space-y-1">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: stat.delay + 0.3 }}
                  className={`text-2xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`}
                >
                  {stat.value}
                </motion.div>
                <motion.h3
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: stat.delay + 0.4 }}
                  className="text-sm font-medium text-neutral-900"
                >
                  {stat.title}
                </motion.h3>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: stat.delay + 0.5 }}
                  className="text-xs text-neutral-500"
                >
                  {stat.subtitle}
                </motion.p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
