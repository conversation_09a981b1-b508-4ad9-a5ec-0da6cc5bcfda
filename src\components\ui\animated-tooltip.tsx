"use client";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

export const AnimatedTooltip = ({
  content,
  children,
}: {
  content: string;
  children: React.ReactNode;
}) => {
  const [isTooltipVisible, setTooltipVisible] = useState(false);

  return (
    <div className="relative inline-block">
      <div
        onMouseEnter={() => setTooltipVisible(true)}
        onMouseLeave={() => setTooltipVisible(false)}
      >
        {children}
      </div>
      <AnimatePresence>
        {isTooltipVisible && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute left-1/2 bottom-full mb-2 -translate-x-1/2"
          >
            <div className="rounded bg-neutral-800 px-4 py-2 text-sm text-white shadow-lg">
              {content}
            </div>
            <div className="absolute left-1/2 top-full -mt-1 h-2 w-2 -translate-x-1/2 rotate-45 bg-neutral-800" />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
