import { describe, it, expect } from "vitest";
import {
  generateCitizenId,
  isValidCitizenId,
  parseCitizenId,
} from "../generate-citizen-id";

describe("generateCitizenId", () => {
  const birthDate = new Date("1990-05-15");
  const registrationDate = new Date("2024-01-10");
  const databaseId = "test-db-id-123";

  it("should generate a valid citizen ID with correct format", () => {
    const citizenId = generateCitizenId(birthDate, registrationDate, databaseId);
    
    expect(citizenId).toMatch(/^\d{8}-\d{8}-[A-F0-9]{8}$/);
    expect(citizenId).toContain("19900515"); // Birth date
    expect(citizenId).toContain("20240110"); // Registration date
  });

  it("should generate consistent IDs for same inputs", () => {
    const id1 = generateCitizenId(birthDate, registrationDate, databaseId);
    const id2 = generateCitizenId(birthDate, registrationDate, databaseId);
    
    expect(id1).toBe(id2);
  });

  it("should generate different IDs for different inputs", () => {
    const id1 = generateCitizenId(birthDate, registrationDate, databaseId);
    const id2 = generateCitizenId(birthDate, registrationDate, "different-db-id");
    
    expect(id1).not.toBe(id2);
  });

  it("should throw error for missing parameters", () => {
    expect(() => generateCitizenId(null as any, registrationDate, databaseId))
      .toThrow("Tous les paramètres sont requis pour générer l'ID citoyen");
    
    expect(() => generateCitizenId(birthDate, null as any, databaseId))
      .toThrow("Tous les paramètres sont requis pour générer l'ID citoyen");
    
    expect(() => generateCitizenId(birthDate, registrationDate, ""))
      .toThrow("Tous les paramètres sont requis pour générer l'ID citoyen");
  });

  it("should throw error when birth date is after registration date", () => {
    const futureBirthDate = new Date("2025-01-01");
    
    expect(() => generateCitizenId(futureBirthDate, registrationDate, databaseId))
      .toThrow("La date de naissance ne peut pas être postérieure à la date d'inscription");
  });
});

describe("isValidCitizenId", () => {
  it("should validate correct citizen ID format", () => {
    expect(isValidCitizenId("19900515-20240110-A1B2C3D4")).toBe(true);
    expect(isValidCitizenId("20000101-20241231-12345678")).toBe(true);
  });

  it("should reject invalid citizen ID formats", () => {
    expect(isValidCitizenId("")).toBe(false);
    expect(isValidCitizenId("invalid-format")).toBe(false);
    expect(isValidCitizenId("1990051-20240110-A1B2C3D4")).toBe(false); // Wrong date length
    expect(isValidCitizenId("19900515-20240110-A1B2C3")).toBe(false); // Wrong hash length
    expect(isValidCitizenId("19900515-20240110-a1b2c3d4")).toBe(false); // Lowercase hash
  });
});

describe("parseCitizenId", () => {
  const validId = "19900515-20240110-A1B2C3D4";

  it("should parse valid citizen ID correctly", () => {
    const result = parseCitizenId(validId);
    
    expect(result).not.toBeNull();
    expect(result!.birthDate).toEqual(new Date(1990, 4, 15)); // Month is 0-indexed
    expect(result!.registrationDate).toEqual(new Date(2024, 0, 10));
    expect(result!.hash).toBe("A1B2C3D4");
  });

  it("should return null for invalid citizen ID", () => {
    expect(parseCitizenId("invalid-id")).toBeNull();
    expect(parseCitizenId("")).toBeNull();
    expect(parseCitizenId("19900515-20240110-invalid")).toBeNull();
  });

  it("should handle edge cases in date parsing", () => {
    const result = parseCitizenId("20000229-20240229-12345678"); // Leap year dates
    
    expect(result).not.toBeNull();
    expect(result!.birthDate.getFullYear()).toBe(2000);
    expect(result!.birthDate.getMonth()).toBe(1); // February (0-indexed)
    expect(result!.birthDate.getDate()).toBe(29);
  });
});

describe("Integration tests", () => {
  it("should create and parse citizen ID correctly", () => {
    const birthDate = new Date("1985-12-25");
    const registrationDate = new Date("2024-03-15");
    const databaseId = "integration-test-id";

    // Generate ID
    const citizenId = generateCitizenId(birthDate, registrationDate, databaseId);
    
    // Validate format
    expect(isValidCitizenId(citizenId)).toBe(true);
    
    // Parse and verify
    const parsed = parseCitizenId(citizenId);
    expect(parsed).not.toBeNull();
    expect(parsed!.birthDate).toEqual(birthDate);
    expect(parsed!.registrationDate).toEqual(registrationDate);
  });
});
