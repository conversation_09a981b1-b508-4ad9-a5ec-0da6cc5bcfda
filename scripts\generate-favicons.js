const sharp = require('sharp');
const fs = require('fs').promises;
const path = require('path');

const FAVICON_SIZES = [
    { size: 16, name: 'favicon-16x16.png' },
    { size: 32, name: 'favicon-32x32.png' },
    { size: 180, name: 'apple-touch-icon.png' },
    { size: 192, name: 'android-chrome-192x192.png' },
    { size: 512, name: 'android-chrome-512x512.png' }
];

async function generateFavicons() {
    const inputFile = path.join(__dirname, '../public/logo.png');
    const publicDir = path.join(__dirname, '../public');

    try {
        // Vérifier si le fichier source existe
        await fs.access(inputFile);
        console.log('✓ Fichier source trouvé:', inputFile);

        // Générer les différentes tailles de favicon
        for (const { size, name } of FAVICON_SIZES) {
            const outputFile = path.join(publicDir, name);
            await sharp(inputFile)
                .resize(size, size, {
                    fit: 'contain',
                    background: { r: 0, g: 0, b: 0, alpha: 0 }
                })
                .png()
                .toFile(outputFile);
            console.log(`✓ Généré: ${name} (${size}x${size}px)`);
        }

        // Utiliser favicon-32x32.png comme favicon.ico principal
        await fs.copyFile(
            path.join(publicDir, 'favicon-32x32.png'),
            path.join(publicDir, 'favicon.ico')
        );
        console.log('✓ Généré: favicon.ico (copie de favicon-32x32.png)');

        console.log('\n✨ Génération des favicons terminée avec succès!');
    } catch (error) {
        console.error('❌ Erreur lors de la génération des favicons:', error);
        process.exit(1);
    }
}

generateFavicons();
