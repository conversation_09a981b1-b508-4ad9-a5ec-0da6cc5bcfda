"use client";

import Link from "next/link";
import { CheckCircle2, FileText, Clock } from "lucide-react";
import { motion } from "framer-motion";

export default function ConfirmationPage() {
  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-[#004D40] via-[#00695C] to-[#00796B]">
      {/* Mo<PERSON>f de fond */}
      <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-10" />
      <div className="absolute inset-0 bg-gradient-radial from-transparent via-black/5 to-black/20" />

      {/* Cercles décoratifs animés */}
      <motion.div
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.2, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -left-32 -top-32 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-3xl opacity-30"
      />
      <motion.div
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.2, 0.3, 0.2],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -right-32 top-1/4 w-96 h-96 bg-red-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20"
      />

      {/* Contenu principal */}
      <div className="relative flex items-center justify-center min-h-screen p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="relative bg-white/10 backdrop-blur-xl shadow-2xl rounded-2xl max-w-2xl w-full p-12 space-y-8 border border-white/20"
        >
          <div className="flex flex-col items-center space-y-8">
            {/* Logo et icône de succès */}
            <div className="relative flex flex-col items-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", duration: 0.8 }}
                className="relative mb-6"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 via-red-500 to-green-400 rounded-full blur-xl opacity-50 animate-pulse" />
                <CheckCircle2
                  className="relative text-white w-24 h-24"
                  strokeWidth={1.5}
                />
              </motion.div>
            </div>

            {/* Titre et sous-titre */}
            <div className="text-center space-y-3">
              <motion.h1
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-4xl font-bold bg-gradient-to-r from-yellow-400 via-red-500 to-green-400 bg-clip-text text-transparent"
              >
                Inscription Réussie
              </motion.h1>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-white/90 text-lg"
              >
                Votre demande d&apos;inscription a été soumise avec succès.
              </motion.p>
            </div>

            {/* Cartes d'information */}
            <div className="w-full space-y-4">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className="group bg-white/10 backdrop-blur-sm hover:bg-white/[0.15] rounded-xl p-6 transition-all duration-300 border border-white/10"
              >
                <div className="flex items-center space-x-4">
                  <div className="bg-white/10 p-3 rounded-lg group-hover:scale-110 transition-transform">
                    <FileText className="text-yellow-400 w-8 h-8" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-white">
                      Documents Soumis
                    </h3>
                    <p className="text-white/80 text-sm">
                      Vos documents sont en cours de vérification
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
                className="group bg-white/10 backdrop-blur-sm hover:bg-white/[0.15] rounded-xl p-6 transition-all duration-300 border border-white/10"
              >
                <div className="flex items-center space-x-4">
                  <div className="bg-white/10 p-3 rounded-lg group-hover:scale-110 transition-transform">
                    <Clock className="text-green-400 w-8 h-8" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-white">
                      Délai de Traitement
                    </h3>
                    <p className="text-white/80 text-sm">
                      Estimation : 30 minutes à 1 jour ouvrable
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Boutons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 w-full"
            >
              <Link
                href="/dashboard"
                className="flex-1 bg-gradient-to-r from-yellow-400 via-red-500 to-green-400 text-white py-3 px-6 rounded-lg text-center
                  hover:from-yellow-500 hover:via-red-600 hover:to-green-500 transition-all duration-300 shadow-lg hover:shadow-xl
                  hover:-translate-y-0.5 active:translate-y-0"
              >
                Aller au Tableau de Bord
              </Link>

              <Link
                href="/aide"
                className="flex-1 bg-white/10 backdrop-blur-sm text-white py-3 px-6 rounded-lg text-center
                  hover:bg-white/20 transition-all duration-300 shadow-md hover:shadow-lg
                  hover:-translate-y-0.5 active:translate-y-0 border border-white/20"
              >
                Besoin d&apos;Aide
              </Link>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
