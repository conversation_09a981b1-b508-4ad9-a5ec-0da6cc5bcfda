import { STATUS } from "@/actions/auth/constants";
import * as z from "zod";

export enum AttachedDocumentStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
}

// Schémas de base réutilisables
const basePersonalInfo = {
  nom: z.string().min(2, "Le nom doit contenir au moins 2 caractères").max(255),
  prenom: z
    .string()
    .min(2, "Le prénom doit contenir au moins 2 caractères")
    .max(255),
  dateNaissance: z.string().min(1, "La date de naissance est requise").max(255),
  lieuNaissance: z.string().min(2, "Le lieu de naissance est requis").max(255),
  nomPere: z.string().min(2, "Le nom du père est requis").max(255),
  nomMere: z.string().min(2, "Le nom de la mère est requis").max(255),
  nationalite: z.string().min(2, "La nationalité est requise").max(255),
  profession: z.string().min(2, "La profession est requise").max(255),
  telephone: z
    .string()
    .min(9, "Le numéro de téléphone doit contenir au moins 9 chiffres")
    .max(255),
  email: z.string().email("L'adresse email n'est pas valide").max(255),
  carteElecteur: z.string().max(255),
} as const;

const baseAddress = {
  region: z.string().min(1, "La région est requise").max(255),
  prefecture: z.string().min(1, "La préfecture est requise").max(255),
  commune: z.string().min(1, "La commune est requise").max(255),
  sousPrefecture: z.string().min(1, "La sous-préfecture est requise").max(255),
  quartier: z.string().min(1, "Le quartier est requis").max(255),
  numeroBatiment: z.string().max(255).optional(),
  proprietaireBatiment: z.string().max(255).optional(),
  adressePrecise: z.string().min(5, "L'adresse précise est requise").max(255),
  dateInstallation: z
    .string()
    .min(1, "La date d'installation est requise")
    .max(255),
} as const;

const baseDocuments = {
  pieceIdentite: z
    .any()
    .refine((val) => val != null, "La pièce d'identité est obligatoire")
    .refine(
      (val) => val?.size <= 5 * 1024 * 1024,
      "La pièce d'identité ne doit pas dépasser 5MB"
    ),
  extraitNaissance: z
    .any()
    .refine(
      (val) => val != null,
      "L'extrait d'acte de naissance est obligatoire"
    )
    .refine(
      (val) => val?.size <= 5 * 1024 * 1024,
      "L'extrait d'acte de naissance ne doit pas dépasser 5MB"
    ),
} as const;

const baseAuthentication = {
  motDePasse: z
    .string()
    .min(8, "Le mot de passe doit contenir au moins 8 caractères")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Le mot de passe doit contenir au moins une majuscule, une minuscule et un chiffre"
    ),
  confirmationMotDePasse: z.string(),
} as const;

// Schéma pour la base de données
export const CitizenSchema = z.object({
  ...basePersonalInfo,
  userId: z.string().max(36),
  status: z.nativeEnum(STATUS).default(STATUS.PENDING),
});

// Schéma pour l'adresse
export const AddressSchema = z.object({
  ...baseAddress,
  citizenId: z.string().max(36),
});

export const DocumentSchema = z.object({
  citizenId: z.string().max(36),
  type: z.string().max(255),
  fileId: z.string().max(255),
  status: z
    .nativeEnum(AttachedDocumentStatus)
    .default(AttachedDocumentStatus.PENDING),
});

// Schéma pour l'inscription
export const RegisterSchema = z
  .object({
    ...basePersonalInfo,
    ...baseAuthentication,
    ...baseAddress,
    ...baseDocuments,
    consentement: z.boolean().refine((val) => val === true, {
      message: "Vous devez accepter les conditions",
    }),
  })
  .refine((data) => data.motDePasse === data.confirmationMotDePasse, {
    message: "Les mots de passe ne correspondent pas",
    path: ["confirmationMotDePasse"],
  });

// Types dérivés des schémas
export type Citizen = z.infer<typeof CitizenSchema>;
export type Address = z.infer<typeof AddressSchema>;
export type Documents = z.infer<typeof DocumentSchema>;
export type RegisterFormData = z.infer<typeof RegisterSchema>;

// Types d'erreurs personnalisés
export type ValidationError = {
  path: string[];
  message: string;
};
