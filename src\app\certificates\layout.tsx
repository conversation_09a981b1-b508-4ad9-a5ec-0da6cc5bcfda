"use client";

import { motion } from "framer-motion";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils/cn";

interface CertificatesLayoutProps {
  children: React.ReactNode;
}

export default function CertificatesLayout({
  children,
}: CertificatesLayoutProps) {
  const pathname = usePathname();

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={cn(
        "relative min-h-screen",
        pathname === "/certificates/new"
          ? "bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5"
          : "bg-white"
      )}
    >
      {children}
    </motion.div>
  );
}
