# 🔄 Migration des Métadonnées pour Appwrite

## Vue d'ensemble

Cette migration adapte le système de vérification des certificats pour être compatible avec les contraintes d'Appwrite, qui ne supporte que les types primitifs (string, number, boolean, array) et non les objets JavaScript complexes.

## 🎯 Objectifs de la migration

### Problème initial

- Appwrite ne stocke pas d'objets JavaScript complexes dans les champs
- Le champ `metadata` était un objet avec plusieurs propriétés
- Incompatibilité avec les types supportés par Appwrite

### Solution implémentée

- **Collection séparée** `certificate_verifications_metadata` pour les métadonnées
- **Relation** entre `certificate_verifications` et `certificate_verifications_metadata`
- **Types primitifs** uniquement (string pour compatibilité maximale)
- **Pattern inspiré** de `src/actions/payment/index.ts`

## 🏗️ Nouvelle architecture

### Avant (problématique)

```typescript
interface CertificateVerification {
  // ... autres champs
  metadata: {
    // ❌ Objet complexe non supporté
    issuerType: string;
    issuerId: string;
    issuerName: string;
    region: string;
    commune: string;
    quartier: string;
  };
}
```

### Après (compatible Appwrite)

```typescript
// Collection 1: certificate_verifications
interface CertificateVerification {
  // ... autres champs
  metadataId: string; // ✅ Référence vers certificate_verifications_metadata
}

// Collection 2: certificate_verifications_metadata
interface CertificateMetadata {
  $id: string;
  verificationId: string; // ✅ Référence vers certificate_verifications
  issuerType: string; // ✅ Type primitif
  issuerId: string; // ✅ Type primitif
  issuerName: string; // ✅ Type primitif
  region: string; // ✅ Type primitif
  commune: string; // ✅ Type primitif
  quartier: string; // ✅ Type primitif
  // ... autres champs
}
```

## 📊 Structure des collections

### Collection `certificate_verifications`

```typescript
{
  $id: string;
  hash: string;                    // Hash unique de vérification
  certificateId: string;           // ID du certificat original
  citizenId: string;              // ID du citoyen
  issuedAt: string;               // Date d'émission (ISO string)
  expiresAt: string;              // Date d'expiration (ISO string)
  isValid: string;                // "true" ou "false"
  isRevoked: string;              // "true" ou "false"
  verificationCount: string;       // Nombre de vérifications (string)
  lastVerifiedAt?: string;        // Dernière vérification (ISO string)
  metadataId: string;             // Référence vers certificate_verifications_metadata
  createdAt: string;              // Date de création (ISO string)
  updatedAt: string;              // Date de mise à jour (ISO string)
}
```

### Collection `certificate_verifications_metadata`

```typescript
{
  $id: string;
  verificationId: string;         // Référence vers certificate_verifications
  issuerType: string;             // 'chef' | 'agent' | 'admin'
  issuerId: string;               // ID de l'émetteur
  issuerName: string;             // Nom de l'émetteur
  region: string;                 // Région
  commune: string;                // Commune
  quartier: string;               // Quartier
  revocationReason?: string;      // Raison de révocation (optionnel)
  revokedAt?: string;            // Date de révocation (optionnel)
  revokedBy?: string;            // Qui a révoqué (optionnel)
  createdAt: string;             // Date de création (ISO string)
  updatedAt: string;             // Date de mise à jour (ISO string)
}
```

## 🔧 Adaptations techniques

### 1. Gestion des types boolean

```typescript
// Conversion boolean → string pour Appwrite
private static booleanToString(value: boolean): string {
  return value.toString();
}

// Conversion string → boolean pour l'application
private static stringToBoolean(value: string): boolean {
  return value === "true";
}
```

### 2. Gestion des types number

```typescript
// Conversion number → string pour Appwrite
private static numberToString(value: number): string {
  return value.toString();
}

// Conversion string → number pour l'application
private static stringToNumber(value: string): number {
  return parseInt(value, 10) || 0;
}
```

### 3. Création transactionnelle

```typescript
static async createVerification(data) {
  // 1. Créer l'enregistrement de vérification
  const verification = await databases.createDocument(/*...*/);

  // 2. Créer les métadonnées
  const metadata = await this.createMetadata(verification.$id, data.metadata);

  // 3. Mettre à jour la référence
  const updatedVerification = await databases.updateDocument(
    verification.$id,
    { metadataId: metadata.$id }
  );

  // 4. Retourner l'objet combiné
  return { ...updatedVerification, metadata };
}
```

## 📋 Index et performances

### Index sur `certificate_verifications`

- `hash_unique` (Unique) - Recherche par hash
- `certificate_id` (Key) - Recherche par certificat
- `citizen_id` (Key) - Recherche par citoyen
- `metadata_id` (Key) - Jointure avec métadonnées
- `expires_at` (Key) - Filtrage par expiration
- `status` (Key) - Filtrage par statut

### Index sur `certificate_verifications_metadata`

- `verification_id` (Unique) - Relation 1:1
- `issuer` (Key) - Recherche par émetteur
- `location` (Key) - Recherche géographique
- `issuer_name_fulltext` (Fulltext) - Recherche textuelle

## 🚀 Migration et déploiement

### 1. Création des collections

```bash
# Exécuter le script de création
npx tsx scripts/create-verification-collections.ts
```

### 2. Variables d'environnement requises

```env
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://your-appwrite-endpoint
NEXT_PUBLIC_APPWRITE_PROJECT=your-project-id
APPWRITE_API_KEY=your-api-key
APPWRITE_DATABASE_ID=your-database-id
```

### 3. Vérification post-migration

```typescript
// Test de création
const verification = await CertificateVerificationService.createVerification({
  hash: "test-hash",
  certificateId: "cert-123",
  citizenId: "citizen-456",
  issuedAt: new Date(),
  expiresAt: new Date(Date.now() + 3 * 30 * 24 * 60 * 60 * 1000),
  metadata: {
    issuerType: "chef",
    issuerId: "chef-789",
    issuerName: "Alpha Touré",
    region: "Conakry",
    commune: "Matam",
    quartier: "Matam",
  },
});

console.log("✅ Vérification créée:", verification);
```

## 🔍 Compatibilité et rétrocompatibilité

### Interface publique préservée

```typescript
// ✅ L'interface publique reste identique
const result = await verifyCertificate(hash);
// Retourne toujours le même format avec metadata inclus
```

### Gestion des erreurs améliorée

- **Transactions** : Création atomique des enregistrements liés
- **Rollback** : Gestion des erreurs de création
- **Validation** : Vérification de l'intégrité des relations

## 📊 Avantages de la nouvelle architecture

### Performance

- ✅ **Index optimisés** pour les requêtes fréquentes
- ✅ **Requêtes ciblées** sur les métadonnées
- ✅ **Jointures efficaces** avec les relations

### Maintenabilité

- ✅ **Séparation des responsabilités** claire
- ✅ **Extensibilité** des métadonnées sans impact
- ✅ **Compatibilité** totale avec Appwrite

### Sécurité

- ✅ **Validation** des types au niveau service
- ✅ **Intégrité** des relations garantie
- ✅ **Audit trail** complet

## 🧪 Tests mis à jour

### Nouveaux mocks

```typescript
// Mock pour la nouvelle structure
vi.mocked(CertificateVerificationService.createVerification).mockResolvedValue({
  // Champs de verification avec types string
  isValid: "true",
  isRevoked: "false",
  verificationCount: "0",
  metadataId: "metadata-id",
  // Métadonnées incluses
  metadata: {
    $id: "metadata-id",
    verificationId: "verification-id",
    issuerType: "chef",
    // ... autres champs
  },
});
```

## ✅ Checklist de migration

- [x] **Collections créées** dans Appwrite
- [x] **Service refactorisé** avec types compatibles
- [x] **Server Actions** mis à jour
- [x] **Tests** adaptés à la nouvelle structure
- [x] **Générateur PDF** compatible
- [x] **Documentation** complète
- [x] **Script de déploiement** fourni

## 🎉 Conclusion

La migration vers une architecture compatible Appwrite est **complète et opérationnelle**. Elle préserve toutes les fonctionnalités existantes tout en respectant les contraintes techniques d'Appwrite, offrant une base solide pour l'évolution future du système.
