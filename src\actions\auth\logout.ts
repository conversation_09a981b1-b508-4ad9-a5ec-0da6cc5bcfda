"use server";

import { cookies } from "next/headers";
import { createSessionClient } from "@/lib/server/appwrite";
import { SESSION_COOKIE } from "./constants";

export async function logout() {
  try {
    const cookieStore = await cookies();
    const sessionId = cookieStore.get(SESSION_COOKIE)?.value;

    if (sessionId) {
      try {
        // Création du client avec la session de l'utilisateur
        const { account } = await createSessionClient(sessionId);

        // Suppression de la session Appwrite
        await account.deleteSession("current");
      } catch (error) {
        console.error(
          "Erreur lors de la suppression de la session Appwrite:",
          error
        );
      }
    }

    // Suppression du cookie de session
    cookieStore.delete(SESSION_COOKIE);

    return {
      success: true,
    };
  } catch (error) {
    console.error("Erreur de déconnexion:", error);
    return {
      success: false,
      error: "Une erreur est survenue lors de la déconnexion",
    };
  }
}
