import { ROLES, STATUS } from "@/actions/auth/constants";

export interface UserDetails {
  $id: string;
  name: string;
  email: string;
  avatarUrl?: string;
  role: ROLES;
  status: STATUS;
  district?: {
    id: string;
    name: string;
  };
  createdAt: Date;
  lastLoginAt: Date;
  phoneNumber?: string;
  documents?: string[];
}

export interface UsersFilters {
  search?: string;
  role?: ROLES;
  status?: STATUS;
  district?: string;
  page?: number;
  limit?: number;
}

export interface UsersPagination {
  total: number;
  page: number;
  limit: number;
}

export interface UsersResponse {
  users: UserDetails[];
  pagination: UsersPagination;
}