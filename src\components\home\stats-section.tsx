import { motion } from "framer-motion";

export function StatsSection() {
  const stats = [
    { value: "500K+", label: "Documents Délivrés" },
    { value: "95%", label: "Satisfaction Utilisateurs" },
    { value: "48h", label: "<PERSON><PERSON><PERSON>" },
    { value: "24/7", label: "Disponibilité" },
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-accent-primary to-accent-secondary">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-white mb-2">
                {stat.value}
              </div>
              <div className="text-green-100">{stat.label}</div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
