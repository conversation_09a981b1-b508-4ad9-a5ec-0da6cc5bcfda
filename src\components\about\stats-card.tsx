"use client";

import { motion } from "framer-motion";

interface StatsCardProps {
  value: string;
  label: string;
  delay?: number;
}

export function StatsCard({ value, label, delay = 0 }: StatsCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      className="bg-white p-6 rounded-2xl shadow-sm border border-neutral-100/50 hover:border-neutral-200 transition-colors"
    >
      <div className="text-3xl font-bold bg-gradient-to-r from-[#004D40] via-[#00796B] to-[#009688] bg-clip-text text-transparent">
        {value}
      </div>
      <div className="text-sm text-neutral-600 mt-2">{label}</div>
    </motion.div>
  );
}
