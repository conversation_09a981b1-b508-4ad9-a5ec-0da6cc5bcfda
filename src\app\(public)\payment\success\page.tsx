"use client";

import { PaymentStatus } from "@/components/payment/PaymentStatus";
import { useSearchParams } from "next/navigation";

export default function PaymentSuccessPage() {
  const searchParams = useSearchParams();
  const transactionId = searchParams.get("transactionId") || "";
  const status = searchParams.get("status") || "COMPLETED";
  const redirectUrl = searchParams.get("redirectUrl") || "/dashboard";

  return (
    <PaymentStatus
      transactionId={transactionId}
      status={status as any}
      redirectUrl={redirectUrl}
    />
  );
}
