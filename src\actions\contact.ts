"use server";

import { ContactFormData, contactFormSchema } from "@/schemas/contact";
import { EmailService } from "@/services/email/service";

export async function sendContactEmail(data: ContactFormData) {
  try {
    // Validation des données
    const validatedData = contactFormSchema.parse(data);

    // Initialisation du service d'email
    const emailService = new EmailService();

    // Vérification de la connexion SMTP
    const isConnected = await emailService.verifyConnection();
    if (!isConnected) {
      throw new Error("Le service d'email n'est pas disponible");
    }

    // Envoi des emails
    await emailService.sendContactConfirmation(validatedData);

    return {
      success: true,
      message: "Votre message a été envoyé avec succès",
    };
  } catch (error) {
    console.error("Erreur lors du traitement du formulaire de contact:", error);
    return {
      success: false,
      message: "Une erreur est survenue lors de l'envoi du message",
    };
  }
}
