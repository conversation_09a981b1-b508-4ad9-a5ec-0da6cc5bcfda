/**
 * Génère une référence unique pour un certificat
 * Format: PREFIX-YYYYMMDD-XXXXX
 * où XXXXX est un nombre aléatoire à 5 chiffres
 *
 * @param prefix Préfixe de la référence (sans tiret final)
 * @returns Référence formatée avec tirets appropriés
 */
export function generateReference(prefix: string = "NCR"): string {
  // Nettoyage du préfixe pour éviter les tirets en double
  const cleanPrefix = prefix.replace(/-+$/, "").toUpperCase();

  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  // Génère un nombre aléatoire à 5 chiffres
  const random = Math.floor(Math.random() * 100000)
    .toString()
    .padStart(5, "0");

  return `${cleanPrefix}-${year}${month}${day}-${random}`;
}

/**
 * Valide le format d'une référence simple
 * Format: PREFIX-YYYYMMDD-XXXXX
 */
export function isValidReference(reference: string): boolean {
  const pattern = /^[A-Z0-9]{2,10}-\d{8}-\d{5}$/;
  return pattern.test(reference);
}

/**
 * Extrait la date d'une référence
 */
export function getDateFromReference(reference: string): Date | null {
  if (!isValidReference(reference)) return null;

  const dateStr = reference.split("-")[1];
  const year = parseInt(dateStr.substring(0, 4));
  const month = parseInt(dateStr.substring(4, 6)) - 1;
  const day = parseInt(dateStr.substring(6, 8));

  return new Date(year, month, day);
}

/**
 * Génère une référence de certificat avec région et commune
 * Format: NCR-REG-COMMUNE-YYYYMMDD-XXXXX
 *
 * @param region Nom de la région
 * @param commune Nom de la commune
 * @returns Référence de certificat formatée correctement
 */
export function generateCertificateReference(
  region: string,
  commune: string
): string {
  // Nettoyage et formatage des codes
  const regionCode = region
    .substring(0, 3)
    .toUpperCase()
    .replace(/[^A-Z0-9]/g, ""); // Supprime les caractères spéciaux

  const communeCode = commune
    .substring(0, 6) // Limite à 6 caractères pour éviter des références trop longues
    .toUpperCase()
    .replace(/[^A-Z0-9]/g, "");

  // Génération de la partie date-numéro
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  const random = Math.floor(Math.random() * 100000)
    .toString()
    .padStart(5, "0");

  // Construction de la référence finale sans tirets en double
  return `NCR-${regionCode}-${communeCode}-${year}${month}${day}-${random}`;
}

/**
 * Valide le format d'une référence de certificat
 * @param reference La référence à valider
 * @returns true si le format est valide
 */
export function isValidCertificateReference(reference: string): boolean {
  const pattern = /^NCR-[A-Z0-9]{1,3}-[A-Z0-9]{1,6}-\d{8}-\d{5}$/;
  return pattern.test(reference);
}

/**
 * Extrait les informations d'une référence de certificat
 * @param reference La référence à analyser
 * @returns Objet contenant les informations extraites ou null si invalide
 */
export function parseCertificateReference(reference: string): {
  regionCode: string;
  communeCode: string;
  date: Date;
  randomNumber: string;
} | null {
  if (!isValidCertificateReference(reference)) {
    return null;
  }

  const parts = reference.split("-");
  const regionCode = parts[1];
  const communeCode = parts[2];
  const dateStr = parts[3];
  const randomNumber = parts[4];

  try {
    const year = parseInt(dateStr.substring(0, 4));
    const month = parseInt(dateStr.substring(4, 6)) - 1;
    const day = parseInt(dateStr.substring(6, 8));
    const date = new Date(year, month, day);

    return { regionCode, communeCode, date, randomNumber };
  } catch {
    return null;
  }
}
