"use client";

import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import {
  MapPin,
  Mail,
  Phone,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Youtube,
} from "lucide-react";

const navigation = {
  principal: [
    { name: "Accueil", href: "/" },
    { name: "Services", href: "/services" },
    { name: "À propos", href: "/a-propos" },
    { name: "Contact", href: "/contact" },
    { name: "<PERSON><PERSON>", href: "/aide" },
  ],
  legal: [
    { name: "Conditions", href: "/conditions" },
    { name: "Confidentialité", href: "/confidentialite" },
    { name: "<PERSON><PERSON>", href: "/cookies" },
  ],
  contact: [
    { icon: MapPin, text: "Conakry, République de Guinée" },
    { icon: Mail, text: "<EMAIL>" },
    { icon: Phone, text: "+224 620 15 71 84" },
  ],
  social: [
    { name: "Facebook", icon: Facebook, href: "#", color: "hover:bg-blue-500" },
    { name: "Twitter", icon: Twitter, href: "#", color: "hover:bg-sky-500" },
    {
      name: "Instagram",
      icon: Instagram,
      href: "#",
      color: "hover:bg-pink-500",
    },
    { name: "LinkedIn", icon: Linkedin, href: "#", color: "hover:bg-blue-700" },
    { name: "YouTube", icon: Youtube, href: "#", color: "hover:bg-red-500" },
  ],
};

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export function Footer() {
  return (
    <footer className="relative mt-auto pt-16 overflow-hidden">
      {/* Effet de glassmorphism amélioré */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/90 via-white/80 to-white/70 backdrop-blur-xl border-t border-neutral-200/50" />

      {/* Cercles décoratifs */}
      <div className="absolute -left-24 -bottom-24 w-96 h-96 bg-[#004D40] rounded-full mix-blend-multiply filter blur-3xl opacity-5" />
      <div className="absolute -right-24 -bottom-24 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-3xl opacity-5" />

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="relative container mx-auto px-6"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 pb-12">
          {/* Logo et Description */}
          <motion.div variants={itemVariants} className="space-y-6">
            <Link href="/" className="flex items-center gap-4 group">
              <div className="relative w-12 h-12 transform transition-transform group-hover:scale-105">
                <div className="absolute inset-0 bg-white rounded-xl opacity-20 blur-md group-hover:opacity-30 transition-opacity" />
                <Image
                  src="/logo.png"
                  alt="Logo NCR"
                  fill
                  className="object-contain relative z-10"
                  priority
                />
              </div>
              <div className="space-y-1">
                <h3 className="text-xl font-bold bg-gradient-to-r from-[#004D40] via-yellow-600 to-red-600 bg-clip-text text-transparent">
                  NCR
                </h3>
                <p className="text-sm text-neutral-600">République de Guinée</p>
              </div>
            </Link>
            <p className="text-neutral-600 text-sm leading-relaxed">
              Plateforme officielle de gestion des certificats de résidence, au
              service des citoyens de la République de Guinée.
            </p>
          </motion.div>

          {/* Liens Rapides */}
          <motion.div variants={itemVariants} className="space-y-6">
            <h3 className="text-sm font-semibold text-neutral-900">
              Liens Rapides
            </h3>
            <ul className="space-y-3">
              {navigation.principal.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-neutral-600 hover:text-[#004D40] text-sm transition-colors duration-200 hover:underline decoration-[#004D40]/30 underline-offset-4"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact */}
          <motion.div variants={itemVariants} className="space-y-6">
            <h3 className="text-sm font-semibold text-neutral-900">Contact</h3>
            <ul className="space-y-4">
              {navigation.contact.map((item, index) => (
                <li key={index} className="flex items-center gap-3 group">
                  <div className="p-2 rounded-lg bg-[#004D40]/5 group-hover:bg-[#004D40]/10 transition-colors duration-200">
                    <item.icon className="w-4 h-4 text-[#004D40]" />
                  </div>
                  <span className="text-sm text-neutral-600">{item.text}</span>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Réseaux Sociaux */}
          <motion.div variants={itemVariants} className="space-y-6">
            <h3 className="text-sm font-semibold text-neutral-900">
              Suivez-nous
            </h3>
            <div className="flex flex-wrap gap-3">
              {navigation.social.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`p-2.5 rounded-lg bg-neutral-100/50 hover:text-white transition-all duration-300
                    ${item.color} hover:scale-105 hover:shadow-lg`}
                >
                  <item.icon className="w-5 h-5" />
                  <span className="sr-only">{item.name}</span>
                </Link>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Séparateur */}
        <motion.div
          variants={itemVariants}
          className="border-t border-neutral-200/70 py-8"
        >
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-sm text-neutral-600">
              © {new Date().getFullYear()} NCR - République de Guinée. Tous
              droits réservés.
            </p>
            <div className="flex items-center gap-6">
              {navigation.legal.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-sm text-neutral-600 hover:text-[#004D40] transition-colors duration-200
                    hover:underline decoration-[#004D40]/30 underline-offset-4"
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </footer>
  );
}
