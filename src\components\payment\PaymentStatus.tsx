"use client";

import { verifyPayment } from "@/actions/payment";
import { PaymentStatus as PaymentStatusEnum } from "@/config/payment";
import { AlertCircle, CheckCircle2, Loader2, XCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Button } from "../ui/button";

interface PaymentStatusProps {
  transactionId: string;
  status: PaymentStatusEnum;
  redirectUrl: string;
}

export function PaymentStatus({
  transactionId,
  status: initialStatus,
  redirectUrl,
}: PaymentStatusProps) {
  const [status, setStatus] = useState(initialStatus);
  const [message, setMessage] = useState(
    "Vérification du statut de paiement en cours..."
  );
  const [isChecking, setIsChecking] = useState(false);
  const [redirectCountdown, setRedirectCountdown] = useState<number | null>(
    null
  );
  const router = useRouter();

  // Fonction de redirection
  const handleRedirect = () => router.push(redirectUrl);

  useEffect(() => {
    if (status === PaymentStatusEnum.SUCCESS && !redirectCountdown) {
      // Démarrer le compte à rebours de 5 secondes
      setRedirectCountdown(5);
      setMessage("Redirection automatique dans 5 secondes...");
    }
  }, [status, redirectCountdown]);

  useEffect(() => {
    if (redirectCountdown !== null) {
      if (redirectCountdown === 0) {
        handleRedirect();
      } else {
        const timer = setTimeout(() => {
          setRedirectCountdown(redirectCountdown - 1);
          setMessage(
            `Redirection automatique dans ${redirectCountdown} secondes...`
          );
        }, 1000);
        return () => clearTimeout(timer);
      }
    }
  }, [redirectCountdown, redirectUrl]);

  useEffect(() => {
    if (status === PaymentStatusEnum.PENDING) {
      const checkStatus = async () => {
        setIsChecking(true);
        try {
          const result = await verifyPayment(transactionId);
          if (result.success) {
            setStatus(result.status);
            if (result.status === PaymentStatusEnum.SUCCESS) {
              setMessage("Paiement confirmé ! Redirection en cours...");
            } else {
              setMessage("Vérification du statut de paiement terminée.");
            }
          }
        } catch (error) {
          console.error("Erreur lors de la vérification:", error);
        } finally {
          setIsChecking(false);
        }
      };

      const interval = setInterval(checkStatus, 5000);
      return () => clearInterval(interval);
    }
  }, [status, transactionId]);

  const statusConfigs: Record<
    PaymentStatusEnum,
    {
      icon: JSX.Element | undefined;
      title: string;
      className: string;
    }
  > = {
    [PaymentStatusEnum.SUCCESS]: {
      icon: <CheckCircle2 className="w-16 h-16 text-green-500" />,
      title: "Paiement réussi",
      className: "bg-green-50 border-green-200",
    },
    [PaymentStatusEnum.FAILED]: {
      icon: <XCircle className="w-16 h-16 text-red-500" />,
      title: "Paiement échoué",
      className: "bg-red-50 border-red-200",
    },
    [PaymentStatusEnum.PENDING]: {
      icon: <Loader2 className="w-16 h-16 text-blue-500 animate-spin" />,
      title: "Traitement en cours",
      className: "bg-blue-50 border-blue-200",
    },
    [PaymentStatusEnum.EXPIRED]: {
      icon: <AlertCircle className="w-16 h-16 text-orange-500" />,
      title: "Paiement annulé",
      className: "bg-orange-50 border-orange-200",
    },
    [PaymentStatusEnum.INITIATED]: {
      icon: undefined,
      title: "",
      className: "",
    },
  };

  const config =
    statusConfigs[status] || statusConfigs[PaymentStatusEnum.FAILED];

  return (
    <div className={`rounded-xl border p-8 shadow-lg ${config.className}`}>
      <div className="flex flex-col items-center space-y-4">
        {config.icon}
        <h2 className="text-2xl font-semibold">{config.title}</h2>
        {message && <p className="text-gray-600 text-center">{message}</p>}
        {!isChecking && status === PaymentStatusEnum.SUCCESS && (
          <Button
            onClick={handleRedirect}
            className="mt-4 bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-2.5 shadow-lg shadow-green-600/30 transition-all duration-200 hover:shadow-green-600/40 hover:scale-[1.02] active:scale-[0.98]"
            size="lg"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Retourner au tableau de bord maintenant
          </Button>
        )}
      </div>
    </div>
  );
}
