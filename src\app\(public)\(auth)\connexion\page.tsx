"use client";

import { LoginForm } from "@/components/auth/login-form";
import { motion } from "framer-motion";
import Image from "next/image";

export default function LoginPage() {
  return (
    <div className="relative min-h-screen flex flex-col lg:flex-row overflow-hidden">
      {/* Section gauche */}
      <div className="hidden lg:flex lg:w-1/2 bg-[#004D40] relative">
        {/* Motifs de fond animés */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-10 animate-slide-slow" />
          <div className="absolute inset-0 bg-gradient-to-br from-[#004D40] via-[#00251A] to-[#004D40] opacity-90" />
        </div>

        {/* Cercles décoratifs animés */}
        <div className="absolute -left-32 -top-32 w-64 h-64 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob" />
        <div className="absolute -right-32 -top-32 w-64 h-64 bg-green-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000" />
        <div className="absolute -bottom-32 left-32 w-64 h-64 bg-green-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000" />

        <div className="relative z-10 flex flex-col justify-between w-full p-8 lg:p-12">
          {/* Logo et titre avec effet de survol */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="group flex items-center gap-4 transition-transform duration-300 hover:transform hover:translate-x-2"
          >
            <div className="relative w-16 h-16 transform transition-transform group-hover:scale-105">
              <div className="absolute inset-0 bg-white rounded-xl opacity-20 blur-md group-hover:opacity-30 transition-opacity" />
              <Image
                src="/logo.png"
                alt="Logo NCR"
                width={64}
                height={64}
                className="object-contain relative z-10"
                priority
              />
            </div>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <h1 className="text-2xl font-bold text-white">NCR</h1>
              <p className="text-sm text-green-100">Certificats de Résidence</p>
            </motion.div>
          </motion.div>

          {/* Message principal avec animation de fondu */}
          <div className="space-y-6 lg:space-y-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.3 }}
              className="space-y-4"
            >
              <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">
                <motion.span
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  La Guinée
                </motion.span>{" "}
                <motion.span
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  className="bg-gradient-to-r from-yellow-400 via-red-500 to-green-400 bg-clip-text text-transparent"
                >
                  Numérique
                </motion.span>
              </h2>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
                className="text-lg lg:text-xl text-green-100 leading-relaxed max-w-lg"
              >
                Modernisation des services administratifs pour une Guinée plus
                efficace et accessible
              </motion.p>
            </motion.div>

            {/* Carte de citation avec effet de verre */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-yellow-500 via-red-500 to-green-500 rounded-2xl blur opacity-20 group-hover:opacity-30 transition-opacity" />
              <blockquote className="relative bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-6 shadow-xl border border-white/10">
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.9 }}
                  className="text-lg italic text-white leading-relaxed"
                >
                  &ldquo;La plateforme NCR modernise la gestion des certificats
                  de résidence en Guinée, offrant un service plus rapide,
                  sécurisé et accessible à tous.&rdquo;
                </motion.p>
                <motion.footer
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 1 }}
                  className="mt-4 flex items-center gap-3"
                >
                  <div className="h-px flex-1 bg-gradient-to-r from-transparent via-green-500 to-transparent opacity-30" />
                  <span className="text-sm text-green-100 font-medium">
                    Ministère de l&apos;Administration du Territoire
                  </span>
                </motion.footer>
              </blockquote>
            </motion.div>

            {/* Indicateurs statistiques */}
            <div className="grid grid-cols-3 gap-4 lg:gap-6">
              {[
                { number: "24/7", label: "Disponibilité" },
                { number: "100%", label: "Sécurisé" },
                { number: "48h", label: "Délai max." },
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.1 + index * 0.1 }}
                  className="text-center group"
                >
                  <div className="text-xl lg:text-2xl font-bold text-white mb-1 transform transition-transform group-hover:scale-110">
                    {stat.number}
                  </div>
                  <div className="text-sm text-green-100 opacity-80">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Effet de vague animée */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/20 to-transparent" />
      </div>

      {/* Section droite - Formulaire */}
      <div className="flex-1 relative bg-gradient-to-br from-gray-50 via-white to-green-50/30">
        {/* Motif de fond subtil */}
        <div className="absolute inset-0 bg-[url('/patterns/grid.svg')] opacity-[0.02]" />

        {/* Cercles décoratifs avec animation */}
        <div className="absolute -top-48 -right-48 w-96 h-96 bg-[#004D40] rounded-full blur-3xl opacity-[0.07] animate-pulse-slow" />
        <div className="absolute -bottom-48 -left-48 w-96 h-96 bg-yellow-600 rounded-full blur-3xl opacity-[0.07] animate-pulse-slow animation-delay-2000" />

        <div className="relative flex items-center justify-center min-h-screen w-full px-4 py-8 lg:px-8">
          <div className="w-full max-w-md mx-auto">
            {/* Conteneur du formulaire */}
            <div className="relative group transition-all duration-300 hover:transform hover:scale-[1.01]">
              <div className="absolute inset-0 bg-gradient-to-r from-red-600 via-yellow-600 to-[#004D40] rounded-2xl opacity-[0.15] group-hover:opacity-[0.18] transition-opacity" />
              <div className="relative bg-white/90 rounded-2xl p-6 lg:p-8 shadow-lg ring-1 ring-black/[0.05] space-y-6 lg:space-y-8">
                {/* Accent borders avec animation */}
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-red-600 via-yellow-600 to-[#004D40] animate-gradient" />
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#004D40] via-yellow-600 to-red-600 animate-gradient" />

                {/* Logo version mobile avec animation */}
                <div className="flex flex-col items-center lg:hidden">
                  {/* <div className="relative w-20 h-20">
                    <div className="absolute inset-0 bg-gradient-to-br from-[#004D40] to-[#00796B] rounded-2xl" />
                    <div className="relative p-2 bg-white rounded-2xl shadow-sm">
                      <Image
                        src="/logo.png"
                        alt="Logo NCR"
                        width={80}
                        height={80}
                        className="object-contain"
                        priority
                      />
                    </div>
                  </div> */}
                  <h2 className="text-center mt-4 text-2xl font-bold bg-gradient-to-r from-[#004D40] via-yellow-600 to-red-600 bg-clip-text text-transparent">
                    Bienvenue sur NCR
                  </h2>
                </div>

                {/* En-tête du formulaire avec animation */}
                <div className="text-center lg:text-left space-y-2">
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-[#004D40] via-yellow-600 to-red-600 bg-clip-text text-transparent animate-gradient">
                    Connexion
                  </h2>
                  <p className="text-gray-600">
                    Accédez à votre espace personnel NCR
                  </p>
                </div>

                {/* Formulaire */}
                <LoginForm />

                {/* Note de sécurité avec animation */}
                <div className="mt-6 text-center">
                  <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-[#004D40]/5 via-yellow-600/5 to-red-600/5 hover:from-[#004D40]/10 hover:via-yellow-600/10 hover:to-red-600/10 transition-colors">
                    <svg
                      className="w-4 h-4 text-[#004D40]"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                    <span className="text-sm text-gray-600">
                      Connexion sécurisée SSL
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
