import { SESSION_COOKIE } from "@/actions/auth/constants";
import { getValidSubpath } from "@/lib/utils/navigation";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getCurrentUser } from "./actions/auth/session";

// Configuration des routes protégées
const PROTECTED_ROUTES = {
  prefix: "/(authenticated)",
  additionalPaths: ["/dashboard", "/certificates", "/admin", "profile"],
  publicPaths: [
    "/api/auth",
    "/maintenance",
    "/api/webhooks",
    "/payment/back",
    "/payment/cancel",
  ],
};

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Fonction utilitaire pour vérifier si une route est protégée
  const isProtectedRoute = (path: string): boolean => {
    const isAuthenticatedPath = path.startsWith(PROTECTED_ROUTES.prefix);
    const isAdditionalProtectedPath = PROTECTED_ROUTES.additionalPaths.some(
      (protectedPath) => path.startsWith(protectedPath)
    );
    const isNotPublicPath = !PROTECTED_ROUTES.publicPaths.some((publicPath) =>
      path.startsWith(publicPath)
    );

    return (
      (isAuthenticatedPath || isAdditionalProtectedPath) && isNotPublicPath
    );
  };

  // Vérifier si la route actuelle nécessite une authentification
  if (isProtectedRoute(pathname)) {
    const user = await getCurrentUser();

    if (!user) {
      request.cookies.delete(SESSION_COOKIE);
      // Encoder l'URL de redirection
      const redirectUrl = getValidSubpath(pathname);
      if (redirectUrl) {
        const searchParams = new URLSearchParams({
          callbackUrl: redirectUrl,
        });

        // Rediriger vers la page de connexion avec l'URL de callback
        return NextResponse.redirect(
          new URL(`/connexion?${searchParams.toString()}`, request.url)
        );
      }
    }
  }

  const response = NextResponse.next();

  // // // En-têtes de sécurité
  // // const securityHeaders = {
  // //   "X-Frame-Options": "DENY",
  // //   "X-Content-Type-Options": "nosniff",
  // //   "Referrer-Policy": "strict-origin-when-cross-origin",
  // //   "Permissions-Policy":
  // //     "accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()",
  // //   "X-XSS-Protection": "1; mode=block",
  // //   "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
  // // };

  // // Object.entries(securityHeaders).forEach(([key, value]) => {
  // //   response.headers.set(key, value);
  // // });

  // const sessionCookie = (await cookies()).get(SESSION_COOKIE);

  // if (sessionCookie) {
  //   response.headers.set("Cookie", sessionCookie.value);
  // }

  return response;
}

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico|public|maintenance).*)",
  ],
};
