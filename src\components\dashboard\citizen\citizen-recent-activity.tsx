"use client";

import { motion, AnimatePresence } from "framer-motion";
import {
  useCitizenActivities,
  ActivityType,
} from "@/hooks/use-citizen-activities";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import Link from "next/link";
import {
  FileText,
  Send,
  CheckCircle,
  XCircle,
  FileCheck,
  FileSignature,
  FileOutput,
  Download,
  Ban,
  Clock,
  UserCircle,
  ChevronRight,
} from "lucide-react";
import { Loader } from "@/components/ui/loader";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils/cn";

interface ActivityConfig {
  icon: any;
  color: string;
  bg: string;
  label: string;
  description: (activity: any) => string;
}

const activityConfig: Record<ActivityType, ActivityConfig> = {
  certificate_created: {
    icon: FileText,
    color: "text-neutral-600",
    bg: "bg-neutral-100",
    label: "Création",
    description: () => "Nouveau certificat créé",
  },
  certificate_submitted: {
    icon: Send,
    color: "text-blue-600",
    bg: "bg-blue-50",
    label: "Soumission",
    description: () => "Certificat soumis pour traitement",
  },
  certificate_updated: {
    icon: Clock,
    color: "text-amber-600",
    bg: "bg-amber-50",
    label: "Mise à jour",
    description: () => "Certificat mis à jour",
  },
  certificate_verified: {
    icon: FileCheck,
    color: "text-teal-600",
    bg: "bg-teal-50",
    label: "Vérification",
    description: (activity) =>
      activity.actor
        ? `Vérifié par ${activity.actor.name}`
        : "Certificat vérifié",
  },
  certificate_approved: {
    icon: CheckCircle,
    color: "text-emerald-600",
    bg: "bg-emerald-50",
    label: "Approbation",
    description: (activity) =>
      activity.actor
        ? `Approuvé par ${activity.actor.name}`
        : "Certificat approuvé",
  },
  certificate_rejected: {
    icon: XCircle,
    color: "text-red-600",
    bg: "bg-red-50",
    label: "Rejet",
    description: (activity) =>
      activity.metadata?.reason
        ? `Rejeté : ${activity.metadata.reason}`
        : "Certificat rejeté",
  },
  certificate_signed: {
    icon: FileSignature,
    color: "text-indigo-600",
    bg: "bg-indigo-50",
    label: "Signature",
    description: (activity) =>
      activity.actor ? `Signé par ${activity.actor.name}` : "Certificat signé",
  },
  certificate_delivered: {
    icon: FileOutput,
    color: "text-green-600",
    bg: "bg-green-50",
    label: "Délivrance",
    description: () => "Certificat délivré",
  },
  certificate_expired: {
    icon: Ban,
    color: "text-neutral-600",
    bg: "bg-neutral-100",
    label: "Expiration",
    description: () => "Certificat expiré",
  },
  certificate_downloaded: {
    icon: Download,
    color: "text-violet-600",
    bg: "bg-violet-50",
    label: "Téléchargement",
    description: () => "Certificat téléchargé",
  },
};

export function CitizenRecentActivity({ limit = 5 }: { limit?: number }) {
  const { activities, isLoading } = useCitizenActivities(limit);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <Loader
          variant="primary"
          size="lg"
          text="Chargement des activités..."
        />
      </div>
    );
  }

  if (!activities || activities.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="flex flex-col items-center justify-center min-h-[200px] text-neutral-500"
      >
        <Clock className="w-12 h-12 mb-4 text-neutral-400" />
        <p className="text-lg font-medium">Aucune activité récente</p>
        <p className="text-sm">Vos activités apparaîtront ici</p>
      </motion.div>
    );
  }

  return (
    <div className="space-y-6">
      <motion.h2
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-lg font-semibold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent"
      >
        Activité récente
      </motion.h2>

      <div className="space-y-4">
        <AnimatePresence mode="popLayout">
          {activities.map((activity, index) => {
            const config = activityConfig[activity.type];
            const Icon = config.icon;

            return (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ delay: index * 0.1 }}
              >
                <Link
                  href={`/dashboard/certificates/${activity.certificateId}`}
                >
                  <div
                    className="group relative overflow-hidden rounded-xl border border-neutral-200/60 bg-white p-4
                    hover:shadow-lg hover:border-neutral-300/80 transition-all duration-300"
                  >
                    {/* Effet de brillance au survol */}
                    <div
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent
                      translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000"
                    />

                    <div className="relative flex items-center gap-4">
                      <div
                        className={cn(
                          "w-10 h-10 rounded-lg flex items-center justify-center",
                          "group-hover:scale-110 transition-transform duration-300",
                          config.bg
                        )}
                      >
                        <Icon className={cn("w-5 h-5", config.color)} />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span
                            className={cn("text-sm font-medium", config.color)}
                          >
                            {config.label}
                          </span>
                          <span className="text-sm text-neutral-500">•</span>
                          <span className="text-sm text-neutral-600 truncate">
                            {activity.certificateReference}
                          </span>
                        </div>
                        <p className="text-sm text-neutral-600 truncate">
                          {config.description(activity)}
                        </p>
                        {activity.actor && (
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex items-center gap-1 text-xs text-neutral-500">
                              <UserCircle className="w-3 h-3" />
                              <span>{activity.actor.name}</span>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className="text-sm text-neutral-500">
                            {formatDistanceToNow(activity.timestamp, {
                              addSuffix: true,
                              locale: fr,
                            })}
                          </p>
                        </div>
                        <ChevronRight
                          className="w-5 h-5 text-neutral-400 group-hover:text-neutral-900
                          group-hover:translate-x-1 transition-all duration-300"
                        />
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            );
          })}
        </AnimatePresence>

        {activities.length > limit && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center mt-6"
          >
            <Button asChild variant="outline" className="gap-2">
              <Link href="/activities">
                <Clock className="w-4 h-4" />
                Voir toutes les activités
              </Link>
            </Button>
          </motion.div>
        )}
      </div>
    </div>
  );
}
