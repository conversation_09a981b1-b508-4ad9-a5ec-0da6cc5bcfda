"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { UserAvatar } from "@/components/ui/user-avatar";
import {
  Clock,
  FileText,
  Bell,
  ChevronRight,
  Activity,
  Calendar,
  ArrowRight,
} from "lucide-react";
import type { User } from "@/types";

interface UserViewProps {
  user: User;
}

export function UserView({ user }: UserViewProps) {
  return (
    <div className="relative min-h-screen bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5">
      {/* Fond décoratif - Optimisé pour mobile */}
      <div className="absolute inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(0,77,64,0.08),transparent_50%)]" />
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-[0.02] md:opacity-[0.03]"
        />
      </div>

      {/* Hero Section - Responsive */}
      <section className="relative pt-20 md:pt-24 lg:pt-32 pb-12 md:pb-16">
        <div className="container mx-auto px-4 sm:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-5xl mx-auto"
          >
            {/* En-tête avec avatar - Layout optimisé */}
            <div className="flex flex-col md:flex-row items-center md:items-start gap-4 md:gap-6 mb-8 md:mb-12">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", duration: 0.6 }}
                className="relative"
              >
                <UserAvatar
                  name={user.name}
                  src={user.prefs?.avatarUrl}
                  size="lg"
                  showStatus
                  status="online"
                  className="w-20 h-20 md:w-24 md:h-24 ring-4 ring-white shadow-xl"
                />
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.3 }}
                  className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white"
                />
              </motion.div>
              <div className="text-center md:text-left space-y-2 md:space-y-4">
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold">
                    <span>Bienvenue, </span>
                    <span className="bg-gradient-to-r from-accent-primary via-accent-secondary to-accent-tertiary bg-clip-text text-transparent">
                      {user.name}
                    </span>
                  </h1>
                  <p className="text-base md:text-lg text-neutral-600 mt-2">
                    Votre espace personnel NCR
                  </p>
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    className="mt-4"
                  >
                    <Link
                      href="/dashboard"
                      className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-accent-primary to-accent-secondary text-white rounded-lg hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5"
                    >
                      Accéder au tableau de bord
                      <ArrowRight className="w-4 h-4" />
                    </Link>
                  </motion.div>
                </motion.div>
              </div>
            </div>

            {/* Cartes d'action rapide - Grid responsive */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-8 md:mb-12">
              {[
                {
                  title: "Nouveau Certificat",
                  description: "Demander un nouveau certificat de résidence",
                  icon: FileText,
                  href: "/certificates/new",
                  color: "from-emerald-500 to-teal-600",
                },
                {
                  title: "Mes Demandes",
                  description: "Suivre l'état de mes demandes en cours",
                  icon: Activity,
                  href: "/dashboard",
                  color: "from-blue-500 to-indigo-600",
                },
                {
                  title: "Rendez-vous",
                  description: "Gérer mes rendez-vous",
                  icon: Calendar,
                  href: "/appointments",
                  color: "from-purple-500 to-pink-600",
                },
              ].map((action, index) => (
                <motion.div
                  key={action.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Link
                    href={action.href}
                    className="group block p-4 md:p-6 bg-white rounded-xl md:rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 border border-neutral-100/50 hover:border-neutral-200"
                  >
                    <div className="flex items-start justify-between">
                      <div
                        className={`p-2.5 md:p-3 rounded-xl bg-gradient-to-br ${action.color} text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}
                      >
                        <action.icon className="w-5 h-5 md:w-6 md:h-6" />
                      </div>
                      <ChevronRight className="w-5 h-5 text-neutral-400 group-hover:text-neutral-900 group-hover:translate-x-1 transition-all duration-300" />
                    </div>
                    <h3 className="mt-3 md:mt-4 text-base md:text-lg font-semibold text-neutral-900 group-hover:text-accent-primary transition-colors">
                      {action.title}
                    </h3>
                    <p className="mt-1 text-sm text-neutral-600 line-clamp-2">
                      {action.description}
                    </p>
                  </Link>
                </motion.div>
              ))}
            </div>

            {/* Statistiques et notifications - Layout responsive */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
              {/* Statistiques */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="bg-white/50 backdrop-blur-sm p-4 md:p-6 rounded-xl md:rounded-2xl border border-neutral-100/50"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-base md:text-lg font-semibold text-neutral-900">
                    Aperçu Rapide
                  </h3>
                  <Link
                    href="/dashboard"
                    className="text-sm text-accent-primary hover:text-accent-secondary flex items-center gap-1 transition-colors"
                  >
                    Voir tout
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </div>
                <div className="grid grid-cols-2 gap-3 md:gap-4">
                  {[
                    { label: "Demandes en cours", value: "2", icon: Clock },
                    { label: "Certificats actifs", value: "3", icon: FileText },
                    { label: "Notifications", value: "4", icon: Bell },
                    { label: "Rendez-vous", value: "1", icon: Calendar },
                  ].map((stat, index) => (
                    <div
                      key={stat.label}
                      className="p-3 md:p-4 bg-white rounded-lg md:rounded-xl border border-neutral-100 hover:border-neutral-200 transition-colors"
                    >
                      <div className="flex items-center gap-2 md:gap-3 mb-2">
                        <stat.icon className="w-4 h-4 md:w-5 md:h-5 text-accent-primary" />
                        <span className="text-xl md:text-2xl font-bold text-neutral-900">
                          {stat.value}
                        </span>
                      </div>
                      <p className="text-xs md:text-sm text-neutral-600">
                        {stat.label}
                      </p>
                    </div>
                  ))}
                </div>
              </motion.div>

              {/* Dernières notifications */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="bg-white/50 backdrop-blur-sm p-4 md:p-6 rounded-xl md:rounded-2xl border border-neutral-100/50"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-base md:text-lg font-semibold text-neutral-900">
                    Dernières Notifications
                  </h3>
                  <Link
                    href="/notifications"
                    className="text-sm text-accent-primary hover:text-accent-secondary flex items-center gap-1 transition-colors"
                  >
                    Tout voir
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </div>
                <div className="space-y-3 md:space-y-4">
                  {[
                    {
                      title: "Demande approuvée",
                      description: "Votre certificat est prêt pour signature",
                      time: "Il y a 2 heures",
                    },
                    {
                      title: "Nouveau message",
                      description:
                        "L'agent a laissé un commentaire sur votre dossier",
                      time: "Il y a 1 jour",
                    },
                  ].map((notification, index) => (
                    <div
                      key={index}
                      className="p-3 md:p-4 bg-white rounded-lg md:rounded-xl border border-neutral-100 hover:border-neutral-200 transition-colors cursor-pointer group"
                    >
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-medium text-neutral-900 group-hover:text-accent-primary transition-colors">
                            {notification.title}
                          </h4>
                          <p className="text-sm text-neutral-600 mt-1 line-clamp-2">
                            {notification.description}
                          </p>
                          <p className="text-xs text-neutral-400 mt-2">
                            {notification.time}
                          </p>
                        </div>
                        <ChevronRight className="w-4 h-4 text-neutral-300 group-hover:text-accent-primary group-hover:translate-x-1 transition-all duration-300" />
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
