"use server";

import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { getCurrentUser } from "@/actions/auth/session";
import { createAdminClient } from "@/lib/server/appwrite";
import {
  CERTIFICATES_COLLECTION_ID,
  DATABASE_ID,
  DOCUMENTS_COLLECTION_ID,
} from "@/lib/server/database";
import { DOCUMENTS_BUCKET_ID } from "@/lib/server/storage";
import { AttachedDocumentStatus } from "@/schemas/citizen";
import { ID } from "node-appwrite";

/**
 * Mettre à jour le motif d'un certificat rejeté
 */
export async function updateCertificateMotif(params: {
  certificateId: string;
  newMotif: string;
}) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupérer le certificat
    const certificate = await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      params.certificateId
    );

    // Vérifier la propriété
    if (certificate.citizenId !== user.$id) {
      throw new Error("Vous n'êtes pas autorisé à modifier ce certificat");
    }

    // Vérifier que le certificat peut être modifié
    if (
      ![CERTIFICATE_STATUS.REJECTED, CERTIFICATE_STATUS.SUBMITTED].includes(
        certificate.status
      )
    ) {
      throw new Error("Ce certificat ne peut plus être modifié");
    }

    // Valider le nouveau motif
    if (params.newMotif.trim().length < 10) {
      throw new Error("Le motif doit contenir au moins 10 caractères");
    }

    // Créer l'historique de modification
    const updateHistory = certificate.updateHistory
      ? JSON.parse(certificate.updateHistory)
      : [];

    updateHistory.push({
      type: "motif_update",
      previousValue: certificate.motif,
      newValue: params.newMotif.trim(),
      updatedAt: new Date().toISOString(),
      updatedBy: user.$id,
    });

    // Mettre à jour le certificat
    await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      params.certificateId,
      {
        motif: params.newMotif.trim(),
        status: CERTIFICATE_STATUS.SUBMITTED, // Remettre en soumis
        updateHistory: JSON.stringify(updateHistory),
        updatedAt: new Date().toISOString(),
        // Effacer les informations de rejet si c'était rejeté
        ...(certificate.status === CERTIFICATE_STATUS.REJECTED && {
          rejectedAt: null,
          rejectedBy: null,
          rejectionReason: null,
        }),
      }
    );

    return {
      success: true,
      message: "Motif mis à jour avec succès",
    };
  } catch (error: any) {
    console.error("Erreur lors de la mise à jour du motif:", error);
    throw error;
  }
}

/**
 * Ajouter de nouveaux documents à un certificat rejeté
 */
export async function addCertificateDocuments(params: {
  certificateId: string;
  files: File[];
}) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases, storage } = await createAdminClient();

    // Récupérer le certificat
    const certificate = await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      params.certificateId
    );

    // Vérifier la propriété
    if (certificate.citizenId !== user.$id) {
      throw new Error("Vous n'êtes pas autorisé à modifier ce certificat");
    }

    // Vérifier que le certificat peut être modifié
    if (
      ![CERTIFICATE_STATUS.REJECTED, CERTIFICATE_STATUS.SUBMITTED].includes(
        certificate.status
      )
    ) {
      throw new Error("Ce certificat ne peut plus être modifié");
    }

    if (params.files.length === 0) {
      throw new Error("Aucun fichier fourni");
    }

    // Upload des nouveaux documents
    const newDocumentIds = await Promise.all(
      params.files.map(async (file) => {
        // Upload du fichier
        const uploadedFile = await storage.createFile(
          DOCUMENTS_BUCKET_ID,
          ID.unique(),
          file
        );

        // Création du document dans la collection
        const document = await databases.createDocument(
          DATABASE_ID,
          DOCUMENTS_COLLECTION_ID,
          ID.unique(),
          {
            type: "complément de documents (mise à jour)",
            fileId: uploadedFile.$id,
            fileName: file.name,
            fileSize: file.size.toString(),
            mimeType: file.type,
            status: AttachedDocumentStatus.PENDING,
          }
        );

        return document.$id;
      })
    );

    // Récupérer les documents existants
    const existingDocuments = certificate.documents || [];
    const allDocuments = [...existingDocuments, ...newDocumentIds];

    // Créer l'historique de modification
    const updateHistory = certificate.updateHistory
      ? JSON.parse(certificate.updateHistory)
      : [];

    updateHistory.push({
      type: "documents_added",
      documentsAdded: newDocumentIds.length,
      totalDocuments: allDocuments.length,
      updatedAt: new Date().toISOString(),
      updatedBy: user.$id,
    });

    // Mettre à jour le certificat
    await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      params.certificateId,
      {
        documents: allDocuments,
        status: CERTIFICATE_STATUS.SUBMITTED, // Remettre en soumis
        updateHistory: JSON.stringify(updateHistory),
        updatedAt: new Date().toISOString(),
        // Effacer les informations de rejet si c'était rejeté
        ...(certificate.status === CERTIFICATE_STATUS.REJECTED && {
          rejectedAt: null,
          rejectedBy: null,
          rejectionReason: null,
        }),
      }
    );

    return {
      success: true,
      message: `${newDocumentIds.length} document(s) ajouté(s) avec succès`,
      documentsAdded: newDocumentIds.length,
    };
  } catch (error: any) {
    console.error("Erreur lors de l'ajout de documents:", error);
    throw error;
  }
}

/**
 * Compléter un certificat (motif + documents)
 */
export async function completeCertificateRequest(params: {
  certificateId: string;
  motif?: string;
  files?: File[];
}) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases, storage } = await createAdminClient();

    // Récupérer le certificat
    const certificate = await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      params.certificateId
    );

    // Vérifier la propriété
    if (certificate.citizenId !== user.$id) {
      throw new Error("Vous n'êtes pas autorisé à modifier ce certificat");
    }

    // Vérifier que le certificat peut être modifié
    if (
      ![CERTIFICATE_STATUS.REJECTED, CERTIFICATE_STATUS.SUBMITTED].includes(
        certificate.status
      )
    ) {
      throw new Error("Ce certificat ne peut plus être modifié");
    }

    const updateData: any = {
      status: CERTIFICATE_STATUS.SUBMITTED,
      updatedAt: new Date().toISOString(),
    };

    const updateHistory = certificate.updateHistory
      ? JSON.parse(certificate.updateHistory)
      : [];

    const changes: any = {
      type: "complete_request",
      updatedAt: new Date().toISOString(),
      updatedBy: user.$id,
    };

    // Mettre à jour le motif si fourni
    if (params.motif && params.motif.trim().length >= 10) {
      updateData.motif = params.motif.trim();
      changes.motifUpdated = true;
      changes.previousMotif = certificate.motif;
      changes.newMotif = params.motif.trim();
    }

    // Ajouter des documents si fournis
    if (params.files && params.files.length > 0) {
      const newDocumentIds = await Promise.all(
        params.files.map(async (file) => {
          const uploadedFile = await storage.createFile(
            DOCUMENTS_BUCKET_ID,
            ID.unique(),
            file
          );

          const document = await databases.createDocument(
            DATABASE_ID,
            DOCUMENTS_COLLECTION_ID,
            ID.unique(),
            {
              type: "complément de documents (finalisation)",
              fileId: uploadedFile.$id,
              fileName: file.name,
              fileSize: file.size.toString(),
              mimeType: file.type,
              status: AttachedDocumentStatus.PENDING,
            }
          );

          return document.$id;
        })
      );

      const existingDocuments = certificate.documents || [];
      updateData.documents = [...existingDocuments, ...newDocumentIds];
      changes.documentsAdded = newDocumentIds.length;
      changes.totalDocuments = updateData.documents.length;
    }

    updateHistory.push(changes);
    updateData.updateHistory = JSON.stringify(updateHistory);

    // Effacer les informations de rejet si c'était rejeté
    if (certificate.status === CERTIFICATE_STATUS.REJECTED) {
      updateData.rejectedAt = null;
      updateData.rejectedBy = null;
      updateData.rejectionReason = null;
    }

    // Mettre à jour le certificat
    await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      params.certificateId,
      updateData
    );

    return {
      success: true,
      message: "Certificat complété avec succès",
      changes,
    };
  } catch (error: any) {
    console.error("Erreur lors de la finalisation du certificat:", error);
    throw error;
  }
}

/**
 * Obtenir l'historique des modifications d'un certificat
 */
export async function getCertificateUpdateHistory(certificateId: string) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupérer le certificat
    const certificate = await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    );

    // Vérifier les permissions (propriétaire, chef, agent ou admin)
    const hasAccess =
      certificate.citizenId === user.$id ||
      certificate.chefId === user.$id ||
      certificate.agentId === user.$id ||
      user.prefs?.role === "admin";

    if (!hasAccess) {
      throw new Error("Vous n'êtes pas autorisé à voir cet historique");
    }

    const updateHistory = certificate.updateHistory
      ? JSON.parse(certificate.updateHistory)
      : [];

    return {
      success: true,
      history: updateHistory,
    };
  } catch (error: any) {
    console.error("Erreur lors de la récupération de l'historique:", error);
    throw error;
  }
}

/**
 * Vérifier si un certificat peut être modifié
 */
export async function canUpdateCertificate(certificateId: string) {
  try {
    const { user } = await getCurrentUser();
    if (!user) {
      return {
        success: false,
        canUpdate: false,
        reason: "Utilisateur non authentifié",
      };
    }

    const { databases } = await createAdminClient();

    const certificate = await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    );

    // Vérifier la propriété
    if (certificate.citizenId !== user.$id) {
      return {
        success: true,
        canUpdate: false,
        reason: "Vous n'êtes pas propriétaire de ce certificat",
      };
    }

    // Vérifier le statut
    const canUpdate = [
      CERTIFICATE_STATUS.REJECTED,
      CERTIFICATE_STATUS.SUBMITTED,
    ].includes(certificate.status);

    return {
      success: true,
      canUpdate,
      reason: canUpdate ? null : "Ce certificat ne peut plus être modifié",
      allowedActions: canUpdate
        ? ["motif_update", "documents_add", "complete_request"]
        : [],
    };
  } catch (error: any) {
    console.error("Erreur lors de la vérification des permissions:", error);
    return {
      success: false,
      canUpdate: false,
      reason: "Erreur lors de la vérification",
    };
  }
}
