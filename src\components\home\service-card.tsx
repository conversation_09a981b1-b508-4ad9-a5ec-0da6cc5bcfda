import { motion } from "framer-motion";

interface ServiceCardProps {
  title: string;
  description: string;
  icon: string;
  features: string[];
  delay: number;
}

export function ServiceCard({
  title,
  description,
  icon,
  features,
  delay,
}: ServiceCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      className="relative group"
    >
      {/* Effet de brillance au survol */}
      <div className="absolute inset-0 bg-gradient-to-r from-accent-primary/5 to-accent-secondary/5 rounded-xl md:rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500" />

      {/* Carte principale */}
      <div className="relative p-5 md:p-6 xl:p-8 rounded-xl md:rounded-2xl bg-white/80 backdrop-blur-sm border border-neutral-200/60 shadow-md hover:shadow-xl transition-all duration-300 group-hover:-translate-y-1">
        {/* En-tête avec icône */}
        <div className="flex items-start gap-4 mb-5 md:mb-6">
          <div className="shrink-0 w-12 h-12 md:w-14 md:h-14 flex items-center justify-center rounded-xl bg-gradient-to-br from-accent-primary/10 to-accent-secondary/10">
            <span className="text-2xl md:text-3xl">{icon}</span>
          </div>
          <div>
            <h3 className="text-lg md:text-xl font-semibold text-neutral-900 mb-1 md:mb-2">
              {title}
            </h3>
            <p className="text-sm md:text-base text-neutral-600">
              {description}
            </p>
          </div>
        </div>

        {/* Liste des fonctionnalités */}
        <div className="space-y-3">
          {features.map((feature, idx) => (
            <motion.div
              key={idx}
              initial={{ opacity: 0.8 }}
              whileHover={{ opacity: 1 }}
              className="flex items-center gap-2 p-2 rounded-lg hover:bg-accent-primary/5 transition-all duration-300"
            >
              <div className="w-1.5 h-1.5 rounded-full bg-accent-primary/60" />
              <span className="text-sm md:text-base text-neutral-700">
                {feature}
              </span>
            </motion.div>
          ))}
        </div>

        {/* Bordure décorative animée */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-accent-primary/20 to-accent-secondary/20 rounded-b-xl md:rounded-b-2xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out" />
      </div>
    </motion.div>
  );
}
