"use server";

import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { getCurrentUser } from "@/actions/auth/session";
import { createAdminClient } from "@/lib/server/appwrite";
import {
  CERTIFICATES_COLLECTION_ID,
  CITIZENS_COLLECTION_ID,
  DATABASE_ID,
  QUARTIERS_COLLECTION_ID,
} from "@/lib/server/database";
import { Query } from "node-appwrite";

/**
 * Demander un changement de quartier
 * Utilise la collection CITIZENS pour stocker la demande de transfert
 */
export async function requestQuartierChange(params: {
  newQuartierName: string;
  reason: string;
}) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupérer le citoyen actuel
    const citizenQuery = await databases.listDocuments(
      DATABASE_ID,
      CITIZENS_COLLECTION_ID,
      [Query.equal("userId", user.$id), Query.limit(1)]
    );

    if (citizenQuery.documents.length === 0) {
      throw new Error("Profil citoyen non trouvé");
    }

    const citizen = citizenQuery.documents[0];

    // Vérifier que le nouveau quartier existe et récupérer ses informations
    const newQuartierQuery = await databases.listDocuments(
      DATABASE_ID,
      QUARTIERS_COLLECTION_ID,
      [Query.equal("nom", params.newQuartierName), Query.limit(1)]
    );

    if (newQuartierQuery.documents.length === 0) {
      throw new Error("Le nouveau quartier n'existe pas");
    }

    const newQuartier = newQuartierQuery.documents[0];

    // Vérifier que le nouveau quartier a un chef
    if (!newQuartier.chefId) {
      throw new Error("Le nouveau quartier n'a pas de chef assigné");
    }

    // Vérifier qu'il ne s'agit pas du même quartier
    if (citizen.quartier === params.newQuartierName) {
      throw new Error("Vous êtes déjà dans ce quartier");
    }

    // Vérifier qu'il n'y a pas déjà une demande en cours
    if (citizen.transferRequest && citizen.transferStatus === "pending") {
      throw new Error("Vous avez déjà une demande de transfert en cours");
    }

    // Mettre à jour le citoyen avec la demande de transfert
    await databases.updateDocument(
      DATABASE_ID,
      CITIZENS_COLLECTION_ID,
      citizen.$id,
      {
        transferRequest: JSON.stringify({
          newQuartierName: params.newQuartierName,
          newQuartierId: newQuartier.$id,
          newChefId: newQuartier.chefId,
          newQuartierInfo: {
            nom: newQuartier.nom,
            commune: newQuartier.commune,
            prefecture: newQuartier.prefecture,
            region: newQuartier.region,
            sousPrefecture: newQuartier.sousPrefecture,
          },
          reason: params.reason,
          requestedAt: new Date().toISOString(),
        }),
        transferStatus: "pending",
        updatedAt: new Date().toISOString(),
      }
    );

    return {
      success: true,
      message: "Demande de transfert envoyée avec succès",
    };
  } catch (error: any) {
    console.error("Erreur lors de la demande de transfert:", error);
    throw error;
  }
}

/**
 * Approuver une demande de transfert (pour les chefs)
 */
export async function approveQuartierTransfer(params: {
  citizenId: string;
  approval: "current_chef" | "new_chef";
  notes?: string;
}) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupérer le citoyen
    const citizen = await databases.getDocument(
      DATABASE_ID,
      CITIZENS_COLLECTION_ID,
      params.citizenId
    );

    if (!citizen.transferRequest) {
      throw new Error("Aucune demande de transfert trouvée");
    }

    const transferRequest = JSON.parse(citizen.transferRequest);
    const currentApprovals = citizen.transferApprovals
      ? JSON.parse(citizen.transferApprovals)
      : {};

    // Vérifier les permissions
    if (params.approval === "current_chef" && citizen.chefId !== user.$id) {
      throw new Error("Vous n'êtes pas autorisé à approuver ce transfert");
    }

    if (
      params.approval === "new_chef" &&
      transferRequest.newChefId !== user.$id
    ) {
      throw new Error("Vous n'êtes pas autorisé à approuver ce transfert");
    }

    // Ajouter l'approbation
    currentApprovals[params.approval] = {
      approvedBy: user.$id,
      approvedAt: new Date().toISOString(),
      notes: params.notes || "",
    };

    // Vérifier si les deux approbations sont obtenues
    const isCompleted =
      currentApprovals.current_chef && currentApprovals.new_chef;

    let updateData: any = {
      transferApprovals: JSON.stringify(currentApprovals),
      updatedAt: new Date().toISOString(),
    };

    if (isCompleted) {
      // Effectuer le transfert
      updateData = {
        ...updateData,
        quartier: transferRequest.newQuartierName,
        quartierId: transferRequest.newQuartierId,
        transferStatus: "completed",
        transferCompletedAt: new Date().toISOString(),
      };

      // Invalider les certificats existants
      await invalidateCertificatesOnTransfer(params.citizenId);
    } else {
      updateData.transferStatus = "partially_approved";
    }

    await databases.updateDocument(
      DATABASE_ID,
      CITIZENS_COLLECTION_ID,
      params.citizenId,
      updateData
    );

    return {
      success: true,
      completed: isCompleted,
      message: isCompleted
        ? "Transfert complété avec succès"
        : "Approbation enregistrée, en attente de la seconde approbation",
    };
  } catch (error: any) {
    console.error("Erreur lors de l'approbation du transfert:", error);
    throw error;
  }
}

/**
 * Rejeter une demande de transfert
 */
export async function rejectQuartierTransfer(params: {
  citizenId: string;
  reason: string;
}) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupérer le citoyen
    const citizen = await databases.getDocument(
      DATABASE_ID,
      CITIZENS_COLLECTION_ID,
      params.citizenId
    );

    if (!citizen.transferRequest) {
      throw new Error("Aucune demande de transfert trouvée");
    }

    const transferRequest = JSON.parse(citizen.transferRequest);

    // Vérifier les permissions (chef actuel ou nouveau chef peut rejeter)
    if (citizen.chefId !== user.$id && transferRequest.newChefId !== user.$id) {
      throw new Error("Vous n'êtes pas autorisé à rejeter ce transfert");
    }

    // Mettre à jour le statut
    await databases.updateDocument(
      DATABASE_ID,
      CITIZENS_COLLECTION_ID,
      params.citizenId,
      {
        transferStatus: "rejected",
        transferRejection: JSON.stringify({
          rejectedBy: user.$id,
          rejectedAt: new Date().toISOString(),
          reason: params.reason,
        }),
        updatedAt: new Date().toISOString(),
      }
    );

    return {
      success: true,
      message: "Demande de transfert rejetée",
    };
  } catch (error: any) {
    console.error("Erreur lors du rejet du transfert:", error);
    throw error;
  }
}

/**
 * Invalider les certificats lors d'un transfert
 */
async function invalidateCertificatesOnTransfer(citizenId: string) {
  try {
    const { databases } = await createAdminClient();

    // Récupérer tous les certificats actifs du citoyen
    const certificatesQuery = await databases.listDocuments(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      [
        Query.equal("citizenId", citizenId),
        Query.notEqual("status", CERTIFICATE_STATUS.EXPIRED),
        Query.notEqual("status", CERTIFICATE_STATUS.REJECTED),
      ]
    );

    // Marquer tous les certificats comme expirés
    const updatePromises = certificatesQuery.documents.map((cert) =>
      databases.updateDocument(
        DATABASE_ID,
        CERTIFICATES_COLLECTION_ID,
        cert.$id,
        {
          status: CERTIFICATE_STATUS.EXPIRED,
          rejectionReason: "Certificat expiré suite au changement de quartier",
          rejectedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      )
    );

    await Promise.all(updatePromises);

    console.log(
      `${certificatesQuery.documents.length} certificats invalidés pour le citoyen ${citizenId}`
    );
  } catch (error) {
    console.error("Erreur lors de l'invalidation des certificats:", error);
    // Ne pas faire échouer le transfert pour cette erreur
  }
}

/**
 * Obtenir les demandes de transfert pour un chef
 */
export async function getTransferRequestsForChef() {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupérer les demandes où le chef actuel ou nouveau chef est l'utilisateur
    const [currentChefRequests, newChefRequests] = await Promise.all([
      // Demandes où l'utilisateur est le chef actuel
      databases.listDocuments(DATABASE_ID, CITIZENS_COLLECTION_ID, [
        Query.equal("chefId", user.$id),
        Query.equal("transferStatus", "pending"),
      ]),
      // Demandes où l'utilisateur est le nouveau chef
      databases.listDocuments(DATABASE_ID, CITIZENS_COLLECTION_ID, [
        Query.contains("transferRequest", user.$id),
        Query.equal("transferStatus", ["pending", "partially_approved"]),
      ]),
    ]);

    // Combiner et dédupliquer les résultats
    const allRequests = [
      ...currentChefRequests.documents,
      ...newChefRequests.documents,
    ];
    const uniqueRequests = allRequests.filter(
      (request, index, self) =>
        index === self.findIndex((r) => r.$id === request.$id)
    );

    return {
      success: true,
      requests: uniqueRequests.map((citizen) => ({
        ...citizen,
        transferRequest: citizen.transferRequest
          ? JSON.parse(citizen.transferRequest)
          : null,
        transferApprovals: citizen.transferApprovals
          ? JSON.parse(citizen.transferApprovals)
          : {},
      })),
    };
  } catch (error: any) {
    console.error("Erreur lors de la récupération des demandes:", error);
    throw error;
  }
}
