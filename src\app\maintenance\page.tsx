"use client";

import Image from "next/image";
import { motion } from "framer-motion";

const containerVariants = {
  initial: { opacity: 0 },
  animate: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

export default function MaintenancePage() {
  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-[#004D40] via-[#00695C] to-[#00796B] flex items-center justify-center">
      {/* Motif de fond */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-10" />
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-black/5 to-black/20" />
      </div>

      {/* Cercles décoratifs animés */}
      <motion.div
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.2, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -left-32 -top-32 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-3xl opacity-30"
      />
      <motion.div
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.2, 0.3, 0.2],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -right-32 top-1/4 w-96 h-96 bg-red-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20"
      />

      {/* Contenu principal */}
      <div className="relative z-10 container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="initial"
          animate="animate"
          className="max-w-2xl mx-auto text-center"
        >
          {/* Logo */}
          <motion.div
            variants={itemVariants}
            className="mb-8 flex justify-center"
          >
            <div className="relative w-24 h-24 bg-white/10 rounded-2xl p-3 backdrop-blur-sm">
              <Image
                src="/logo.png"
                alt="Logo NCR"
                width={72}
                height={72}
                className="object-contain"
                priority
              />
              <motion.div
                animate={{
                  rotate: 360,
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: "linear",
                }}
                className="absolute inset-0 rounded-2xl border-2 border-white/20"
              />
            </div>
          </motion.div>

          {/* Titre */}
          <motion.div variants={itemVariants} className="mb-6">
            <h1 className="text-4xl font-bold text-white mb-2">
              Maintenance en cours
            </h1>
            <p className="text-xl text-white/90">
              Notre plateforme fait peau neuve
            </p>
          </motion.div>

          {/* Message principal */}
          <motion.div variants={itemVariants} className="mb-12">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <p className="text-lg text-white/80 leading-relaxed mb-4">
                Nous effectuons actuellement une maintenance programmée pour
                améliorer nos services. Nous serons de retour très
                prochainement.
              </p>
              <div className="grid grid-cols-2 gap-4 mt-6">
                <div className="p-4 bg-white/5 rounded-xl">
                  <div className="text-2xl font-bold text-yellow-400 mb-1">
                    2h
                  </div>
                  <div className="text-sm text-white/70">Durée estimée</div>
                </div>
                <div className="p-4 bg-white/5 rounded-xl">
                  <div className="text-2xl font-bold text-green-400 mb-1">
                    8:00
                  </div>
                  <div className="text-sm text-white/70">Heure de reprise</div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Étapes de maintenance */}
          <motion.div
            variants={itemVariants}
            className="grid gap-4 mb-12 max-w-lg mx-auto"
          >
            {[
              {
                status: "completed",
                text: "Sauvegarde des données",
                icon: "💾",
              },
              {
                status: "in-progress",
                text: "Mise à jour du système",
                icon: "🔄",
              },
              {
                status: "pending",
                text: "Tests de sécurité",
                icon: "🔒",
              },
            ].map((step, index) => (
              <div
                key={index}
                className="flex items-center gap-4 bg-white/5 p-4 rounded-xl"
              >
                <span className="text-2xl">{step.icon}</span>
                <div className="flex-1">
                  <div className="text-white font-medium">{step.text}</div>
                  <div className="w-full h-1 bg-white/10 rounded-full mt-2">
                    <motion.div
                      initial={{ width: "0%" }}
                      animate={{
                        width:
                          step.status === "completed"
                            ? "100%"
                            : step.status === "in-progress"
                            ? "60%"
                            : "0%",
                      }}
                      className="h-full rounded-full bg-gradient-to-r from-yellow-400 to-green-400"
                    />
                  </div>
                </div>
              </div>
            ))}
          </motion.div>

          {/* Contact */}
          <motion.div variants={itemVariants} className="text-center">
            <p className="text-white/60 text-sm">
              Pour toute urgence, contactez notre{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-yellow-400 hover:text-yellow-300 transition-colors duration-200"
              >
                équipe support
              </a>
            </p>
          </motion.div>
        </motion.div>
      </div>

      {/* Bordures décoratives */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-yellow-400/20 via-red-500/20 to-green-400/20" />
        <div className="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-green-400/20 via-red-500/20 to-yellow-400/20" />
        <div className="absolute top-0 left-0 h-full w-2 bg-gradient-to-b from-yellow-400/20 via-red-500/20 to-green-400/20" />
        <div className="absolute top-0 right-0 h-full w-2 bg-gradient-to-b from-green-400/20 via-red-500/20 to-yellow-400/20" />
      </div>
    </div>
  );
}
