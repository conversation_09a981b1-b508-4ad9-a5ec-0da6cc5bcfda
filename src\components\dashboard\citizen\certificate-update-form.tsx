"use client";

import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import {
  addCertificateDocuments,
  completeCertificateRequest,
  updateCertificateMotif,
} from "@/actions/citizen/certificate-management";
import { retryFailedPayment } from "@/actions/payment";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { formatBytes } from "@/lib/utils/format";
import { AnimatePresence, motion } from "framer-motion";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  CreditCard,
  Edit,
  FileText,
  RefreshCw,
  Upload,
  XCircle,
} from "lucide-react";
import { useState } from "react";
import { useDropzone } from "react-dropzone";

import type { Certificate } from "@/actions/types";
import { PAYMENT_CONFIG } from "@/config/payment";

interface CertificateUpdateFormProps {
  certificate: Certificate;
  onUpdate?: () => void;
}

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const MAX_FILES = 3;
const ACCEPTED_FILE_TYPES = {
  "application/pdf": [".pdf"],
  "image/*": [".png", ".jpg", ".jpeg"],
};

export function CertificateUpdateForm({
  certificate,
  onUpdate,
}: CertificateUpdateFormProps) {
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isRetryingPayment, setIsRetryingPayment] = useState(false);
  const [newMotif, setNewMotif] = useState(certificate.motif);
  const [files, setFiles] = useState<File[]>([]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: ACCEPTED_FILE_TYPES,
    maxSize: MAX_FILE_SIZE,
    maxFiles: MAX_FILES,
    onDropAccepted: (acceptedFiles) => {
      setFiles((prev) => [...prev, ...acceptedFiles].slice(0, MAX_FILES));
    },
    onDropRejected: (rejectedFiles) => {
      rejectedFiles.forEach(({ file, errors }) => {
        if (errors.some((e) => e.code === "file-too-large")) {
          toast({
            title: "Fichier trop volumineux",
            description: `${file.name} dépasse la limite de ${formatBytes(
              MAX_FILE_SIZE
            )}`,
            variant: "warning",
          });
        }
      });
    },
  });

  const handleUpdateMotif = async () => {
    if (newMotif.trim().length < 10) {
      toast({
        title: "Motif trop court",
        description: "Le motif doit contenir au moins 10 caractères",
        variant: "warning",
      });
      return;
    }

    try {
      await updateCertificateMotif({
        certificateId: certificate.$id,
        newMotif: newMotif.trim(),
      });

      toast({
        title: "Motif mis à jour",
        description: "Le motif de votre demande a été mis à jour avec succès",
        variant: "success",
      });

      setIsEditing(false);
      onUpdate?.();
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error?.message || "Une erreur est survenue",
        variant: "error",
      });
    }
  };

  const handleAddDocuments = async () => {
    if (files.length === 0) {
      toast({
        title: "Aucun fichier",
        description: "Veuillez sélectionner au moins un fichier",
        variant: "warning",
      });
      return;
    }

    try {
      setIsUploading(true);

      const result = await addCertificateDocuments({
        certificateId: certificate.$id,
        files,
      });

      toast({
        title: "Documents ajoutés",
        description: result.message,
        variant: "success",
      });

      setFiles([]);
      onUpdate?.();
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error?.message || "Une erreur est survenue",
        variant: "error",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleCompleteRequest = async () => {
    try {
      setIsUploading(true);

      const result = await completeCertificateRequest({
        certificateId: certificate.$id,
        motif: newMotif !== certificate.motif ? newMotif.trim() : undefined,
        files: files.length > 0 ? files : undefined,
      });

      toast({
        title: "Demande complétée",
        description: result.message,
        variant: "success",
      });

      setFiles([]);
      setIsEditing(false);
      onUpdate?.();
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error?.message || "Une erreur est survenue",
        variant: "error",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleRetryPayment = async () => {
    try {
      setIsRetryingPayment(true);

      const result = await retryFailedPayment(certificate.$id);

      if (result.success && result.paymentUrl) {
        toast({
          title: "Redirection vers le paiement",
          description: "Vous allez être redirigé vers la page de paiement",
          variant: "success",
        });

        window.location.href = result.paymentUrl;
      }
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error?.message || "Une erreur est survenue",
        variant: "error",
      });
    } finally {
      setIsRetryingPayment(false);
    }
  };

  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
  };

  const getStatusIcon = () => {
    switch (certificate.status) {
      case CERTIFICATE_STATUS.SUBMITTED:
        return <Clock className="w-4 h-4 text-blue-600" />;
      case CERTIFICATE_STATUS.VERIFIED:
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case CERTIFICATE_STATUS.REJECTED:
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <FileText className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = () => {
    switch (certificate.status) {
      case CERTIFICATE_STATUS.SUBMITTED:
        return "bg-blue-100 text-blue-800";
      case CERTIFICATE_STATUS.VERIFIED:
        return "bg-green-100 text-green-800";
      case CERTIFICATE_STATUS.REJECTED:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const canUpdate = [
    CERTIFICATE_STATUS.REJECTED,
    CERTIFICATE_STATUS.SUBMITTED,
  ].includes(certificate.status);
  const needsPayment = !certificate.isPaid && certificate.price;

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon()}
              Certificat {certificate.reference}
            </CardTitle>
            <div className="flex items-center gap-2 mt-2">
              <Badge className={getStatusColor()}>{certificate.status}</Badge>
              {certificate.paymentStatus && (
                <Badge variant={certificate.isPaid ? "default" : "secondary"}>
                  {certificate.isPaid
                    ? "Payé"
                    : `Paiement: ${certificate.paymentStatus}`}
                </Badge>
              )}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Payment Status */}
        {needsPayment && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-orange-50 border border-orange-200 rounded-lg p-4"
          >
            <div className="flex items-center gap-3">
              <AlertCircle className="w-5 h-5 text-orange-600" />
              <div className="flex-1">
                <h4 className="font-semibold text-orange-900">
                  Paiement requis
                </h4>
                <p className="text-sm text-orange-700">
                  Montant: {((certificate.price || 0) / 100).toFixed(2)}{" "}
                  {PAYMENT_CONFIG.orangeMoney.currency}
                </p>
              </div>
              <Button
                onClick={handleRetryPayment}
                disabled={isRetryingPayment}
                className="bg-orange-600 hover:bg-orange-700 text-white"
              >
                {isRetryingPayment ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <CreditCard className="w-4 h-4" />
                )}
                Payer
              </Button>
            </div>
          </motion.div>
        )}

        {/* Rejection Reason */}
        {certificate.status === CERTIFICATE_STATUS.REJECTED &&
          certificate.rejectionReason && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-50 border border-red-200 rounded-lg p-4"
            >
              <h4 className="font-semibold text-red-900 mb-2">
                Raison du rejet
              </h4>
              <p className="text-sm text-red-700">
                {certificate.rejectionReason}
              </p>
            </motion.div>
          )}

        {canUpdate && (
          <>
            {/* Motif Section */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold">Motif de la demande</h4>
                {!isEditing && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Modifier
                  </Button>
                )}
              </div>

              <AnimatePresence mode="wait">
                {isEditing ? (
                  <motion.div
                    key="editing"
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-3"
                  >
                    <Textarea
                      value={newMotif}
                      onChange={(e) => setNewMotif(e.target.value)}
                      className="min-h-[100px]"
                      placeholder="Nouveau motif..."
                    />
                    <div className="flex gap-2">
                      <Button onClick={handleUpdateMotif} size="sm">
                        Sauvegarder
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setIsEditing(false);
                          setNewMotif(certificate.motif);
                        }}
                      >
                        Annuler
                      </Button>
                    </div>
                  </motion.div>
                ) : (
                  <motion.p
                    key="viewing"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-sm text-neutral-600 bg-neutral-50 p-3 rounded-lg"
                  >
                    {certificate.motif}
                  </motion.p>
                )}
              </AnimatePresence>
            </div>

            {/* Document Upload Section */}
            <div className="space-y-3">
              <h4 className="font-semibold">Ajouter des documents</h4>
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
                  ${
                    isDragActive
                      ? "border-accent-primary bg-accent-primary/5"
                      : "border-neutral-300 hover:border-accent-primary/50"
                  }`}
              >
                <input {...getInputProps()} />
                <Upload className="w-8 h-8 mx-auto mb-2 text-neutral-400" />
                <p className="text-sm text-neutral-600">
                  Glissez-déposez vos fichiers ici ou cliquez pour parcourir
                </p>
                <p className="text-xs text-neutral-500 mt-1">
                  PDF, PNG, JPG - Max {formatBytes(MAX_FILE_SIZE)} par fichier
                </p>
              </div>

              {files.length > 0 && (
                <div className="space-y-2">
                  {files.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 p-2 bg-neutral-50 rounded"
                    >
                      <FileText className="w-4 h-4" />
                      <span className="text-sm flex-1">{file.name}</span>
                      <span className="text-xs text-neutral-500">
                        {formatBytes(file.size)}
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        ×
                      </Button>
                    </div>
                  ))}

                  <div className="flex gap-2">
                    <Button
                      onClick={handleAddDocuments}
                      disabled={isUploading}
                      size="sm"
                    >
                      {isUploading ? "Envoi..." : "Ajouter les documents"}
                    </Button>
                    <Button
                      onClick={handleCompleteRequest}
                      disabled={isUploading}
                      variant="outline"
                      size="sm"
                    >
                      {isUploading ? "Envoi..." : "Compléter la demande"}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
