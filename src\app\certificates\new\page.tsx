"use client";

import { motion } from "framer-motion";
import { RequestCertificate } from "@/components/dashboard/citizen/request-certificate";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";

export default function NewCertificatePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5">
      {/* Fond décoratif */}
      <div className="absolute inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(0,77,64,0.08),transparent_50%)]" />
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-[0.02]" />
      </div>

      <div className="container mx-auto px-6 py-8 max-w-[800px]">
        {/* Bouton retour */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="mb-6"
        >
          <Button
            asChild
            variant="ghost"
            className="gap-2 text-neutral-600 hover:text-neutral-900"
          >
            <Link href="/dashboard">
              <ChevronLeft className="w-4 h-4" />
              Retour au tableau de bord
            </Link>
          </Button>
        </motion.div>

        {/* En-tête */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-3xl font-bold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent">
            Nouvelle demande de certificat
          </h1>
          <p className="text-neutral-600 mt-2">
            Remplissez le formulaire ci-dessous pour soumettre une nouvelle
            demande de certificat
          </p>
        </motion.div>

        {/* Formulaire */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60 p-8 relative overflow-hidden"
        >
          <div className="absolute -top-12 -right-12 w-32 h-32 bg-accent-primary/5 rounded-full blur-2xl" />
          <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-accent-secondary/5 rounded-full blur-2xl" />
          <RequestCertificate />
        </motion.div>
      </div>
    </div>
  );
}
