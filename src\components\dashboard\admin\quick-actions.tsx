"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { UserPlus, FileText, Building2, Settings } from "lucide-react";

export function AdminQuickActions() {
  const actions = [
    {
      title: "Ajouter un utilisateur",
      description: "Créer un nouveau compte administratif",
      icon: UserPlus,
      href: "/admin/users/new",
      color: "bg-blue-500",
    },
    {
      title: "Gérer les certificats",
      description: "Voir tous les certificats",
      icon: FileText,
      href: "/admin/certificates",
      color: "bg-green-500",
    },
    {
      title: "Gérer les quartiers",
      description: "Administrer les zones",
      icon: Building2,
      href: "/admin/districts",
      color: "bg-purple-500",
    },
    {
      title: "Paramètres",
      description: "Configuration du système",
      icon: Settings,
      href: "/admin/settings",
      color: "bg-gray-500",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {actions.map((action, index) => (
        <motion.div
          key={action.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <Link
            href={action.href}
            className="block p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow group"
          >
            <div className={`p-3 rounded-lg ${action.color} w-fit mb-4 group-hover:scale-110 transition-transform`}>
              <action.icon className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {action.title}
            </h3>
            <p className="text-sm text-gray-600">
              {action.description}
            </p>
          </Link>
        </motion.div>
      ))}
    </div>
  );
} 