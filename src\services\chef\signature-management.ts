import { createAdminClient } from "@/lib/server/appwrite";
import {
  CHEF_SIGNATURES_COLLECTION_ID,
  DATABASE_ID,
} from "@/lib/server/database";
import { ID, Query } from "node-appwrite";

export interface ChefSignature {
  $id: string;
  chefId: string;
  signatureFileId: string;
  signatureType: "digital" | "handwritten";
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastUsedAt?: string;
  usageCount: number;
}

export interface CreateSignatureParams {
  chefId: string;
  signatureData: string;
  signatureType: "digital" | "handwritten";
}

export interface UpdateSignatureParams {
  chefId: string;
  signatureData: string;
  signatureType: "digital" | "handwritten";
}

export class ChefSignatureService {
  private static instance: ChefSignatureService;

  public static getInstance(): ChefSignatureService {
    if (!ChefSignatureService.instance) {
      ChefSignatureService.instance = new ChefSignatureService();
    }
    return ChefSignatureService.instance;
  }

  /**
   * Récupérer la signature active d'un chef
   */
  async getChefSignature(chefId: string): Promise<ChefSignature | null> {
    try {
      const { databases } = await createAdminClient();

      const signatures = await databases.listDocuments(
        DATABASE_ID,
        CHEF_SIGNATURES_COLLECTION_ID,
        [
          Query.equal("chefId", chefId),
          Query.equal("isActive", "true"),
          Query.limit(1),
        ]
      );

      if (signatures.documents.length === 0) {
        return null;
      }

      const doc = signatures.documents[0];
      return {
        $id: doc.$id,
        chefId: doc.chefId,
        signatureFileId: doc.signatureFileId,
        signatureType: doc.signatureType as "digital" | "handwritten",
        isActive: doc.isActive === "true",
        createdAt: doc.createdAt,
        updatedAt: doc.updatedAt,
        lastUsedAt: doc.lastUsedAt,
        usageCount: parseInt(doc.usageCount || "0"),
      };
    } catch (error) {
      console.error("Erreur lors de la récupération de la signature:", error);
      throw new Error("Impossible de récupérer la signature du chef");
    }
  }

  /**
   * Créer ou mettre à jour la signature d'un chef (upsert)
   */
  async upsertChefSignature(
    params: CreateSignatureParams
  ): Promise<ChefSignature> {
    try {
      const { databases } = await createAdminClient();
      const now = new Date().toISOString();

      // Vérifier si une signature existe déjà
      const existingSignature = await this.getChefSignature(params.chefId);

      if (existingSignature) {
        // Mettre à jour la signature existante
        const updatedDoc = await databases.updateDocument(
          DATABASE_ID,
          CHEF_SIGNATURES_COLLECTION_ID,
          existingSignature.$id,
          {
            signatureData: params.signatureData,
            signatureType: params.signatureType,
            updatedAt: now,
            isActive: "true",
          }
        );

        return {
          $id: updatedDoc.$id,
          chefId: updatedDoc.chefId,
          signatureData: updatedDoc.signatureData,
          signatureType: updatedDoc.signatureType as "digital" | "handwritten",
          isActive: updatedDoc.isActive === "true",
          createdAt: updatedDoc.createdAt,
          updatedAt: updatedDoc.updatedAt,
          lastUsedAt: updatedDoc.lastUsedAt,
          usageCount: parseInt(updatedDoc.usageCount || "0"),
        };
      } else {
        // Créer une nouvelle signature
        const newDoc = await databases.createDocument(
          DATABASE_ID,
          CHEF_SIGNATURES_COLLECTION_ID,
          ID.unique(),
          {
            chefId: params.chefId,
            signatureData: params.signatureData,
            signatureType: params.signatureType,
            isActive: "true",
            createdAt: now,
            updatedAt: now,
            usageCount: "0",
          }
        );

        return {
          $id: newDoc.$id,
          chefId: newDoc.chefId,
          signatureData: newDoc.signatureData,
          signatureType: newDoc.signatureType as "digital" | "handwritten",
          isActive: newDoc.isActive === "true",
          createdAt: newDoc.createdAt,
          updatedAt: newDoc.updatedAt,
          lastUsedAt: newDoc.lastUsedAt,
          usageCount: parseInt(newDoc.usageCount || "0"),
        };
      }
    } catch (error) {
      console.error(
        "Erreur lors de la création/mise à jour de la signature:",
        error
      );
      throw new Error("Impossible de sauvegarder la signature du chef");
    }
  }

  /**
   * Utiliser la signature d'un chef (incrémenter le compteur d'usage)
   */
  async useChefSignature(chefId: string): Promise<ChefSignature | null> {
    try {
      const signature = await this.getChefSignature(chefId);

      if (!signature) {
        return null;
      }

      const { databases } = await createAdminClient();
      const now = new Date().toISOString();
      const newUsageCount = signature.usageCount + 1;

      const updatedDoc = await databases.updateDocument(
        DATABASE_ID,
        CHEF_SIGNATURES_COLLECTION_ID,
        signature.$id,
        {
          lastUsedAt: now,
          usageCount: newUsageCount.toString(),
          updatedAt: now,
        }
      );

      return {
        $id: updatedDoc.$id,
        chefId: updatedDoc.chefId,
        signatureData: updatedDoc.signatureData,
        signatureType: updatedDoc.signatureType as "digital" | "handwritten",
        isActive: updatedDoc.isActive === "true",
        createdAt: updatedDoc.createdAt,
        updatedAt: updatedDoc.updatedAt,
        lastUsedAt: updatedDoc.lastUsedAt,
        usageCount: newUsageCount,
      };
    } catch (error) {
      console.error("Erreur lors de l'utilisation de la signature:", error);
      throw new Error("Impossible d'utiliser la signature du chef");
    }
  }

  /**
   * Désactiver la signature d'un chef
   */
  async deactivateChefSignature(chefId: string): Promise<boolean> {
    try {
      const signature = await this.getChefSignature(chefId);

      if (!signature) {
        return false;
      }

      const { databases } = await createAdminClient();
      const now = new Date().toISOString();

      await databases.updateDocument(
        DATABASE_ID,
        CHEF_SIGNATURES_COLLECTION_ID,
        signature.$id,
        {
          isActive: "false",
          updatedAt: now,
        }
      );

      return true;
    } catch (error) {
      console.error("Erreur lors de la désactivation de la signature:", error);
      throw new Error("Impossible de désactiver la signature du chef");
    }
  }

  /**
   * Vérifier si un chef a une signature active
   */
  async hasActiveSignature(chefId: string): Promise<boolean> {
    try {
      const signature = await this.getChefSignature(chefId);
      return signature !== null && signature.isActive;
    } catch (error) {
      console.error("Erreur lors de la vérification de la signature:", error);
      return false;
    }
  }
}

export const chefSignatureService = ChefSignatureService.getInstance();
