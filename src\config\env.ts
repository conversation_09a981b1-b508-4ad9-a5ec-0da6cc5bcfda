const defaultEnv = {
  development: {
    url: "https://webhook.site/04997c9c-09ba-447d-a76d-c2e5b0ff26d5",
    apiUrl: "http://localhost:3000/api",
    appwriteEndpoint: "https://cloud.appwrite.io/v1",
    appwriteProjectId: "ncr",
  },
  production: {
    url: "https://ncr.ouestech.com",
    apiUrl: "https://ncr.ouestech.com/api",
    appwriteEndpoint: "https://cloud.appwrite.io/v1",
    appwriteProjectId: "ncr",
  },
};

const environment = process.env.NEXT_PUBLIC_APP_ENV || "development";
const config =
  defaultEnv[environment as keyof typeof defaultEnv] || defaultEnv.development;

export const APP_URL = config.url;
export const API_URL = config.apiUrl;
export const APPWRITE_ENDPOINT =
  process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || config.appwriteEndpoint;
export const APPWRITE_PROJECT_ID =
  process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || config.appwriteProjectId;
export const APPWRITE_API_KEY = process.env.NEXT_APPWRITE_API_KEY || "";
export const VAPID_PUBLIC_KEY = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || "";

// Fonction utilitaire pour construire des URLs
export function buildUrl(path: string): string {
  return `${APP_URL}${path.startsWith("/") ? path : `/${path}`}`;
}

// Fonction utilitaire pour construire des URLs d'API
export function buildApiUrl(path: string): string {
  return `${API_URL}${path.startsWith("/") ? path : `/${path}`}`;
}

// Validation de l'environnement
if (typeof window === "undefined") {
  // Validation côté serveur
  const requiredEnvVars = [
    "NEXT_PUBLIC_APPWRITE_ENDPOINT",
    "NEXT_PUBLIC_APPWRITE_PROJECT_ID",
    "NEXT_APPWRITE_API_KEY",
  ];

  // requiredEnvVars.forEach((envVar) => {
  //   if (!process.env[envVar]) {
  //     throw new Error(`La variable d'environnement ${envVar} est requise`);
  //   }
  // });
}
