"use client";
import React, { useEffect, useRef, useState } from "react";
import { motion, useAnimation, useScroll } from "framer-motion";

export const TracingBeam = ({
  children,
  className = "",
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  const controls = useAnimation();
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end end"],
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const showBeam = async () => {
      await controls.start({ opacity: 1 });
      setIsVisible(true);
    };
    showBeam();
  }, [controls]);

  return (
    <motion.div
      ref={ref}
      className={`relative w-full ${className}`}
      animate={controls}
    >
      <motion.div
        className="absolute left-8 top-0 h-full w-[1px] bg-gradient-to-b from-emerald-500/0 via-emerald-500/50 to-emerald-500/0"
        style={{
          scaleY: scrollYProgress,
          opacity: isVisible ? 1 : 0,
        }}
      />
      <div className="relative ml-16">{children}</div>
    </motion.div>
  );
};
