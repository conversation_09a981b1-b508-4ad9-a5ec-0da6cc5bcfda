"use client";

import { motion } from "framer-motion";
import {
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  Download,
  TimerOff,
  FileEdit,
  TrendingUp,
  TrendingDown,
} from "lucide-react";
import { useCitizenStats } from "@/hooks/use-citizen-stats";
import { Loader } from "@/components/ui/loader";

const stats = [
  {
    name: "Total",
    key: "total" as const,
    icon: FileText,
    color: "from-blue-500 to-blue-600",
    bgColor: "bg-blue-50",
    textColor: "text-blue-600",
  },
  {
    name: "En cours",
    key: "pending" as const,
    icon: Clock,
    color: "from-amber-500 to-amber-600",
    bgColor: "bg-amber-50",
    textColor: "text-amber-600",
  },
  {
    name: "Approuvés",
    key: "approved" as const,
    icon: CheckCircle,
    color: "from-emerald-500 to-emerald-600",
    bgColor: "bg-emerald-50",
    textColor: "text-emerald-600",
  },
  {
    name: "Rejetés",
    key: "rejected" as const,
    icon: XCircle,
    color: "from-red-500 to-red-600",
    bgColor: "bg-red-50",
    textColor: "text-red-600",
  },
  {
    name: "Délivrés",
    key: "delivered" as const,
    icon: Download,
    color: "from-purple-500 to-purple-600",
    bgColor: "bg-purple-50",
    textColor: "text-purple-600",
  },
  {
    name: "Expirés",
    key: "expired" as const,
    icon: TimerOff,
    color: "from-gray-500 to-gray-600",
    bgColor: "bg-gray-50",
    textColor: "text-gray-600",
  },
  {
    name: "Brouillons",
    key: "draft" as const,
    icon: FileEdit,
    color: "from-neutral-500 to-neutral-600",
    bgColor: "bg-neutral-50",
    textColor: "text-neutral-600",
  },
];

export function CitizenStats() {
  const { stats: citizenStats, isLoading, error } = useCitizenStats();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader
          variant="primary"
          size="lg"
          text="Chargement des statistiques..."
        />
      </div>
    );
  }

  if (error || !citizenStats) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-neutral-500">
        <XCircle className="w-12 h-12 mb-4" />
        <p>Impossible de charger les statistiques</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <motion.h2
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-lg font-semibold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent"
      >
        Aperçu de vos demandes
      </motion.h2>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => {
          const value = citizenStats[stat.key];
          const Icon = stat.icon;
          const trend =
            stat.key === "total" ? citizenStats.trends.monthly : null;

          return (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative overflow-hidden rounded-xl border border-neutral-200/60 bg-white p-4
                hover:shadow-lg hover:border-neutral-300/80 transition-all duration-300 group"
            >
              {/* Cercle décoratif */}
              <div
                className={`absolute -right-6 -top-6 w-16 h-16 rounded-full ${stat.bgColor} opacity-20
                group-hover:scale-150 transition-transform duration-700`}
              />

              <div className="relative space-y-3">
                <div
                  className={`w-12 h-12 rounded-xl ${stat.bgColor} flex items-center justify-center
                  group-hover:scale-110 transition-transform duration-300`}
                >
                  <Icon className={`w-6 h-6 ${stat.textColor}`} />
                </div>

                <div>
                  <div className="flex items-baseline gap-2">
                    <span
                      className={`text-2xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`}
                    >
                      {value}
                    </span>
                    <span className="text-sm text-neutral-600">demandes</span>
                    {trend && trend.percentage !== 0 && (
                      <div
                        className={`flex items-center gap-1 text-xs font-medium rounded-full px-2 py-1
                        ${
                          trend.percentage > 0
                            ? "text-emerald-700 bg-emerald-50"
                            : "text-red-700 bg-red-50"
                        }`}
                      >
                        {trend.percentage > 0 ? (
                          <TrendingUp className="w-3 h-3" />
                        ) : (
                          <TrendingDown className="w-3 h-3" />
                        )}
                        {Math.abs(trend.percentage).toFixed(1)}%
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-neutral-600">{stat.name}</p>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Tendances */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
        {Object.entries(citizenStats.trends).map(([period, data], index) => (
          <motion.div
            key={period}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 + index * 0.1 }}
            className="relative overflow-hidden rounded-xl border border-neutral-200/60 bg-white p-4
              hover:shadow-lg hover:border-neutral-300/80 transition-all duration-300 group"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 capitalize">
                  {period === "daily"
                    ? "Aujourd'hui"
                    : period === "weekly"
                    ? "Cette semaine"
                    : "Ce mois"}
                </p>
                <p className="text-2xl font-bold text-neutral-900">
                  {data.total}
                </p>
              </div>
              {data.percentage !== 0 && (
                <div
                  className={`flex items-center gap-1 text-sm font-medium rounded-full px-3 py-1
                  ${
                    data.percentage > 0
                      ? "text-emerald-700 bg-emerald-50"
                      : "text-red-700 bg-red-50"
                  }`}
                >
                  {data.percentage > 0 ? (
                    <TrendingUp className="w-4 h-4" />
                  ) : (
                    <TrendingDown className="w-4 h-4" />
                  )}
                  {Math.abs(data.percentage).toFixed(1)}%
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
