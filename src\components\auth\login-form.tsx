"use client";

import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { isValidRedirectUrl } from "@/lib/utils/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { AnimatePresence, motion } from "framer-motion";
import { AlertCircle, ArrowRight, Eye, EyeOff, Loader2 } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Schéma de validation
const loginSchema = z.object({
  email: z
    .string()
    .min(1, "L'email est requis")
    .email("Format d'email invalide"),
  password: z
    .string()
    .min(1, "Le mot de passe est requis")
    .min(8, "Le mot de passe doit contenir au moins 8 caractères"),
});

type LoginFormData = z.infer<typeof loginSchema>;

export function LoginForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login, isLoading, isAuthenticated } = useAuth();
  const { toast } = useToast();

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Récupérer l'URL de callback
  const callbackUrl = searchParams?.get("callbackUrl");

  useEffect(() => {
    if (isAuthenticated) {
      // Rediriger vers l'URL de callback si elle est valide
      const redirectUrl = isValidRedirectUrl(callbackUrl) && callbackUrl
        ? callbackUrl
        : "/dashboard";

      router.replace(redirectUrl);
    }
  }, [isAuthenticated, router, callbackUrl]);

  const onSubmit = async (data: LoginFormData) => {
    setError(null);
    const result = await login(data);
    console.log(result);

    if (!result.success) {
      // si code 404, rediriger vers inscription
      if (result.code === 404) {
        // mets un joli toast avant de rediriger
        toast({
          title: "Inscription",
          description:
            "Vous devez vous inscrire avant de pouvoir vous connecter",
          variant: "warning",
        });
        router.push("/inscription");
        return;
      }
      setError(result.error || "Une erreur est survenue");
      return;
    }

    if (result.success) {
      router.push("/dashboard");
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full max-w-md space-y-8 p-8 rounded-2xl bg-white/95 lg:bg-white/5 border border-white/10"
    >
      <div className="relative py-6">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full h-1.5 bg-gradient-to-r from-red-600 via-yellow-600 to-[#004D40] animate-gradient rounded-full shadow-lg" />
        </div>
        <div className="relative flex justify-center">
          <div className="relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-red-600 via-yellow-600 to-[#004D40] rounded-full blur opacity-25 group-hover:opacity-0 group-hover:blur-none transition-all duration-300"></div>
            <span className="relative px-6 py-2 bg-white lg:bg-black/40 text-gray-800 lg:text-white text-sm font-semibold rounded-full shadow-lg group-hover:bg-white/95 lg:group-hover:bg-black/60 border border-white/20 group-hover:border-white/40 transition-all duration-300">
              Authentification sécurisée
            </span>
          </div>
        </div>
      </div>

      <AnimatePresence mode="wait">
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    className="bg-white/5"
                    autoComplete="email"
                    disabled={isLoading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Mot de passe</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      className="bg-white/5 pr-10"
                      autoComplete="current-password"
                      disabled={isLoading}
                      {...field}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={isLoading}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex items-center justify-between">
            <Link
              href="/mot-de-passe-oublie"
              className="text-sm text-accent-foreground hover:text-accent-foreground/80 transition-colors"
            >
              Mot de passe oublié ?
            </Link>
          </div>

          <Button
            type="submit"
            className="w-full bg-accent-primary hover:bg-accent-primary/90 text-white
                shadow-lg shadow-accent-primary/20 transition-all duration-300"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Connexion en cours...
              </>
            ) : (
              <>
                Se connecter
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-white/10" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Ou
              </span>
            </div>
          </div>

          <div className="text-center text-sm">
            Pas encore de compte ?{" "}
            <Link
              href="/inscription"
              className="font-medium text-accent-foreground hover:text-accent-foreground/80 transition-colors"
            >
              S&apos;inscrire
            </Link>
          </div>
        </form>
      </Form>
    </motion.div>
  );
}
