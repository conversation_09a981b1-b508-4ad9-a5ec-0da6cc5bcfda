import { JsonLd } from "@/components/seo/json-ld";
import { Toaster } from "@/components/ui/toaster";
import { siteConfig } from "@/config/seo";
import { cn } from "@/lib/utils/cn";
import { generateMetadata } from "@/lib/utils/seo";
import { QueryProvider } from "@/providers/query-provider";
import localFont from "next/font/local";
import React from "react";
import "./globals.css";
import { Viewport } from "next";
// import { initializeAppwrite } from "@/lib/server/init";

const inter = localFont({
  src: "../../public/fonts/inter.woff2",
  variable: "--font-inter",
  display: "swap",
  preload: true,
});

const jetbrainsMono = localFont({
  src: "../../public/fonts/jetbrains-mono.woff2",
  variable: "--font-jetbrains-mono",
  display: "swap",
  preload: true,
});

// Initialize Appwrite
// initializeAppwrite().catch(console.error);

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: siteConfig.themeColor },
    { media: "(prefers-color-scheme: dark)", color: "#000000" },
  ],
};

export const metadata = generateMetadata({
  title: siteConfig.name,
  description: siteConfig.description,
  path: "/",
  noIndex: false,
  keywords: siteConfig.keywords,
  image: siteConfig.ogImage,
  additionalMetadata: {
    applicationName: siteConfig.name,
    appleWebApp: {
      capable: true,
      title: siteConfig.name,
      statusBarStyle: "default",
    },
    formatDetection: {
      telephone: false,
    },
    manifest: siteConfig.manifest,
    icons: {
      icon: "/favicon.ico",
      shortcut: "/favicon-16x16.png",
      apple: [
        { url: "/icons/icon-192x192.png", sizes: "192x192", type: "image/png" },
        { url: "/icons/icon-512x512.png", sizes: "512x512", type: "image/png" },
      ],
      other: [
        {
          rel: "mask-icon",
          url: "/icons/safari-pinned-tab.svg",
          color: siteConfig.themeColor,
        },
      ],
    },
    creator: siteConfig.creator,
    publisher: siteConfig.creator,
    authors: [{ name: siteConfig.creator, url: siteConfig.url }],
    viewport: {
      width: "device-width",
      initialScale: 1,
      maximumScale: 1,
      userScalable: false,
      viewportFit: "cover",
    },
    verification: {
      google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
      yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION,
    },
    alternates: {
      canonical: siteConfig.url,
      languages: {
        fr: `${siteConfig.url}`,
      },
    },
    robots: {
      index: true,
      follow: true,
      nocache: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  },
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      lang="fr"
      suppressHydrationWarning
      className={cn(inter.variable, jetbrainsMono.variable, "h-full")}
    >
      <head>
        <link rel="manifest" href={siteConfig.manifest} />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
                window.addEventListener('load', async function() {
                  try {
                    const registration = await navigator.serviceWorker.register('/sw.js', {
                      scope: '/',
                    });
                    console.log('ServiceWorker registration successful');
                  } catch (error) {
                    console.error('ServiceWorker registration failed:', error);
                  }
                });
              }
            `,
          }}
        />
        <JsonLd />
      </head>
      <body
        className={cn(
          "h-full antialiased bg-neutral-50/50 text-neutral-900 selection:bg-green-200 font-sans",
          inter.variable,
          jetbrainsMono.variable
        )}
        suppressHydrationWarning
      >
        <QueryProvider>
          {children}
          <Toaster />
        </QueryProvider>
      </body>
    </html>
  );
}
