import { useToast as useToastUI } from "@/components/ui/use-toast";

interface ToastOptions {
  title: string;
  description?: string;
  variant?: "default" | "success" | "error" | "warning" | "info";
  duration?: number;
}

export function useToast() {
  const { toast: toastUI } = useToastUI();

  const toast = ({
    title,
    description,
    variant = "default",
    duration = 5000,
  }: ToastOptions) => {
    toastUI({
      title,
      description,
      variant,
      duration,
    });
  };

  return { toast };
}
