"use server";

import { createAdminClient } from "@/lib/server/appwrite";
import { DATABASE_ID, CITIZENS_COLLECTION_ID, AGENTS_COLLECTION_ID, CHEFS_COLLECTION_ID } from "@/lib/server/database";
import { Query } from "node-appwrite";
import { STATUS } from "./constants";
import type { ExtendedUserDetails } from "@/hooks/use-user";

export async function getUserDetails(userId: string | undefined): Promise<ExtendedUserDetails | null> {
  if (!userId) return null;

  try {
    const { databases, users } = await createAdminClient();

    // Récupérer l'utilisateur Appwrite
    const user = await users.get(userId);

    // Collections à vérifier selon le rôle
    const collections = [
      CITIZENS_COLLECTION_ID,
      AGENTS_COLLECTION_ID,
      CHEFS_COLLECTION_ID,
    ];

    // Rechercher les détails dans les collections
    for (const collectionId of collections) {
      const { documents } = await databases.listDocuments(
        DATABASE_ID,
        collectionId,
        [Query.equal("userId", userId)]
      );

      if (documents.length > 0) {
        const details = documents[0];
        return {
          ...user,
          quartier: {
            id: details.userId,
            name: details.quartier,
          },
          role: details.role,
          userStatus: details.status as STATUS,
          phoneNumber: details.telephone,
        };
      }
    }

    // Si aucun détail trouvé, retourner l'utilisateur de base
    return user as ExtendedUserDetails;
  } catch (error) {
    console.error("Erreur lors de la récupération des détails utilisateur:", error);
    throw error;
  }
}