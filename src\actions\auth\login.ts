"use server";

import { createAdminClient } from "@/lib/server/appwrite";
import { cookies } from "next/headers";
import { AppwriteException, Query } from "node-appwrite";
import { z } from "zod";
import { SESSION_COOKIE } from "./constants";
import {
  APPWRITE_API_KEY,
  APPWRITE_ENDPOINT,
  APPWRITE_PROJECT_ID,
} from "@/config/env";

// Schéma de validation pour la connexion
const LoginSchema = z.object({
  email: z.string().email("Email invalide"),
  password: z
    .string()
    .min(8, "Le mot de passe doit contenir au moins 8 caractères"),
});

export type LoginFormData = z.infer<typeof LoginSchema>;

export interface LoginResponse {
  success: boolean;
  error?: string;
  type?:
    | "user_not_found"
    | "user_invalid_credentials"
    | "user_blocked"
    | "unknown";
  code?: number;
  validationErrors?: z.ZodError["errors"];
}

export async function login(formData: LoginFormData): Promise<LoginResponse> {
  try {
    // Validation des données
    const validatedData = LoginSchema.parse(formData);

    // Log pour le débogage
    console.log("Configuration Appwrite:", {
      endpoint: APPWRITE_ENDPOINT,
      projectId: APPWRITE_PROJECT_ID,
      hasApiKey: !!APPWRITE_API_KEY,
    });

    // Création du client Appwrite avec privilèges admin
    const { account, users } = await createAdminClient();

    const { users: allUsers, total } = await users.list([
      Query.equal("email", validatedData.email),
    ]);

    // Vérification de l'existence de l'utilisateur
    if (total === 0) {
      console.log("User not found");
      throw new AppwriteException(
        "Aucun utilisateur trouvé avec ces identifiants de connexion",
        404,
        "user_not_found"
      );
    }

    // vérification si utilisateur pas blocké en utilisant l'api appwrite
    const userStatus = allUsers[0];
    if (!userStatus.status) {
      console.log("User is blocked");
      throw new AppwriteException(
        "Ce compte a été temporairement bloqué. Veuillez réessayer plus tard",
        403,
        "user_blocked"
      );
    }

    if (!userStatus.emailVerification) {
      console.log("User is not verified");
      throw new AppwriteException(
        "Ce compte n'est pas encore verifié par un administrateur. Nous vous informons dès que cela sera fait",
        403,
        "user_not_verified"
      );
    }

    try {
      // Création de la session
      const session = await account.createEmailPasswordSession(
        validatedData.email,
        validatedData.password
      );

      // Création du cookie de session
      const cookieStore = await cookies();
      cookieStore.set(SESSION_COOKIE, session.secret, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        expires: new Date(session.expire),
        path: "/",
      });
    } catch (error) {
      console.log("error-> ->", error);
      if (error instanceof AppwriteException) {
        if (error.type === "user_invalid_credentials") {
          return {
            success: false,
            error: "Nom d'utilisateur ou mot de passe incorrect",
            type: error.type,
          };
        } else {
          console.log("error-> ->", error);
          return {
            success: false,
            error: error.message,
            type: "unknown",
          };
        }
      }
    }

    return {
      success: true,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Données de connexion invalides",
        validationErrors: error.errors,
      };
    }

    // Gestion spécifique des erreurs Appwrite
    if (error instanceof AppwriteException) {
      switch (error.type) {
        case "user_not_found":
          return {
            success: false,
            error: error.message,
            code: error.code,
            type: error.type ?? "user_not_found",
          };
        case "user_blocked":
          return {
            success: false,
            error: error.message,
            code: error.code,
          };
        case "user_not_verified":
          return {
            success: false,
            error: error.message,
            code: error.code,
          };
        case "user_invalid_credentials":
          return {
            success: false,
            error:
              error.message ?? "Nom d'utilisateur ou mot de passe incorrect",
            code: error.code,
          };
      }
    }

    console.error("Erreur de connexion:", error);
    return {
      success: false,
      error: "Une erreur inattendue est survenue",
      type: "unknown",
    };
  }
}
