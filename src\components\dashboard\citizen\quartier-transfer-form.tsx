"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { useUser } from "@/hooks/use-user";
import { motion } from "framer-motion";
import { MapPin, Loader2, Send, CheckCircle, Clock, XCircle } from "lucide-react";
import { requestQuartierChange } from "@/actions/citizen/profile-updates";
import { getAllQuartiers, type Quartier } from "@/actions/geo/quartiers";

interface QuartierTransferFormProps {
  citizen?: any;
  onUpdate?: () => void;
}

export function QuartierTransferForm({ citizen, onUpdate }: QuartierTransferFormProps) {
  const { user } = useUser();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingQuartiers, setIsLoadingQuartiers] = useState(true);
  const [newQuartier, setNewQuartier] = useState("");
  const [reason, setReason] = useState("");
  const [quartiers, setQuartiers] = useState<Quartier[]>([]);

  const currentQuartier = citizen?.quartier || user?.quartier?.name;
  const transferStatus = citizen?.transferStatus;
  const transferRequest = citizen?.transferRequest ? JSON.parse(citizen.transferRequest) : null;

  // Charger les quartiers au montage du composant
  useEffect(() => {
    const loadQuartiers = async () => {
      try {
        const result = await getAllQuartiers();
        if (result.success) {
          setQuartiers(result.quartiers);
        } else {
          toast({
            title: "Erreur",
            description: "Impossible de charger la liste des quartiers",
            variant: "error",
          });
        }
      } catch (error) {
        console.error("Erreur lors du chargement des quartiers:", error);
        toast({
          title: "Erreur",
          description: "Erreur lors du chargement des quartiers",
          variant: "error",
        });
      } finally {
        setIsLoadingQuartiers(false);
      }
    };

    loadQuartiers();
  }, [toast]);

  const handleSubmit = async () => {
    if (!newQuartier) {
      toast({
        title: "Quartier requis",
        description: "Veuillez sélectionner le nouveau quartier",
        variant: "warning",
      });
      return;
    }

    if (newQuartier === currentQuartier) {
      toast({
        title: "Quartier identique",
        description: "Le nouveau quartier doit être différent de votre quartier actuel",
        variant: "warning",
      });
      return;
    }

    if (reason.trim().length < 20) {
      toast({
        title: "Motif trop court",
        description: "Veuillez fournir un motif plus détaillé (minimum 20 caractères)",
        variant: "warning",
      });
      return;
    }

    try {
      setIsLoading(true);

      await requestQuartierChange({
        newQuartierName: newQuartier,
        reason: reason.trim(),
      });

      toast({
        title: "Demande envoyée",
        description: "Votre demande de transfert a été envoyée aux chefs de quartier",
        variant: "success",
      });

      // Reset form
      setNewQuartier("");
      setReason("");
      onUpdate?.();
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error?.message || "Une erreur est survenue",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = () => {
    switch (transferStatus) {
      case "pending":
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case "partially_approved":
        return <Clock className="w-4 h-4 text-blue-600" />;
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case "rejected":
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (transferStatus) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "partially_approved":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = () => {
    switch (transferStatus) {
      case "pending":
        return "En attente d'approbation";
      case "partially_approved":
        return "Partiellement approuvé";
      case "completed":
        return "Transfert complété";
      case "rejected":
        return "Demande rejetée";
      default:
        return "Aucune demande";
    }
  };

  const hasPendingRequest = transferStatus === "pending" || transferStatus === "partially_approved";

  // Filtrer les quartiers disponibles (exclure le quartier actuel et ceux sans chef)
  const availableQuartiers = quartiers.filter(
    (q) => q.nom !== currentQuartier && q.chefId
  );

  return (
    <Card className="relative overflow-hidden border-0 shadow-none">
      <CardHeader className="space-y-1 pb-8">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2"
        >
          <div className="h-8 w-1 rounded-full bg-gradient-to-b from-accent-primary to-accent-secondary" />
          <div>
            <CardTitle className="text-2xl bg-gradient-to-r from-accent-primary to-accent-secondary bg-clip-text text-transparent">
              Demande de transfert de quartier
            </CardTitle>
            <p className="text-neutral-600 text-sm mt-1">
              Demander un transfert vers un nouveau quartier
            </p>
          </div>
        </motion.div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Current Quartier */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gradient-to-r from-neutral-50 to-neutral-100 rounded-xl p-4 border border-neutral-200"
        >
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-accent-primary/10 flex items-center justify-center">
              <MapPin className="w-5 h-5 text-accent-primary" />
            </div>
            <div>
              <h3 className="font-semibold text-neutral-900">Quartier actuel</h3>
              <p className="text-sm text-neutral-600">{currentQuartier || "Non défini"}</p>
            </div>
          </div>
        </motion.div>

        {/* Transfer Status */}
        {transferStatus && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-xl p-4 border border-neutral-200"
          >
            <div className="flex items-center gap-3 mb-3">
              {getStatusIcon()}
              <div>
                <h3 className="font-semibold text-neutral-900">Statut de la demande</h3>
                <Badge className={getStatusColor()}>
                  {getStatusText()}
                </Badge>
              </div>
            </div>
            
            {transferRequest && (
              <div className="text-sm text-neutral-600 space-y-1">
                <p><strong>Nouveau quartier:</strong> {transferRequest.newQuartierName}</p>
                <p><strong>Demandé le:</strong> {new Date(transferRequest.requestedAt).toLocaleDateString()}</p>
                <p><strong>Motif:</strong> {transferRequest.reason}</p>
              </div>
            )}
          </motion.div>
        )}

        {!hasPendingRequest && (
          <>
            {/* New Quartier Selection */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="space-y-2"
            >
              <label className="text-sm font-medium text-neutral-900">
                Nouveau quartier
              </label>
              <Select value={newQuartier} onValueChange={setNewQuartier} disabled={isLoadingQuartiers}>
                <SelectTrigger className="transition-all duration-200 focus:ring-accent-primary/20 focus:border-accent-primary/30 hover:border-accent-primary/20">
                  <SelectValue placeholder={
                    isLoadingQuartiers 
                      ? "Chargement des quartiers..." 
                      : "Sélectionnez votre nouveau quartier"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {availableQuartiers.map((quartier) => (
                    <SelectItem key={quartier.$id} value={quartier.nom}>
                      {quartier.nom} ({quartier.commune})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {!isLoadingQuartiers && availableQuartiers.length === 0 && (
                <p className="text-xs text-red-500">
                  Aucun quartier disponible pour le transfert
                </p>
              )}
            </motion.div>

            {/* Reason */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="space-y-2"
            >
              <label className="text-sm font-medium text-neutral-900">
                Motif du transfert
              </label>
              <Textarea
                placeholder="Expliquez les raisons de votre demande de transfert..."
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="min-h-[120px] resize-none transition-all duration-200
                  focus:ring-accent-primary/20 focus:border-accent-primary/30
                  hover:border-accent-primary/20"
              />
              <p className="text-xs text-neutral-500">
                {reason.length}/20 caractères minimum
              </p>
            </motion.div>

            {/* Submit Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <Button
                className="w-full relative overflow-hidden group bg-gradient-to-r from-accent-primary to-accent-secondary
                  hover:from-accent-primary/90 hover:to-accent-secondary/90 text-white font-medium
                  shadow-lg shadow-accent-primary/20 hover:shadow-xl hover:shadow-accent-primary/30
                  transform hover:-translate-y-0.5 transition-all duration-300"
                onClick={handleSubmit}
                disabled={!newQuartier || !reason || reason.length < 20 || isLoading || isLoadingQuartiers}
              >
                <span
                  className="absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(255,255,255,0.2)_50%,transparent_100%)]
                  translate-x-[-150%] group-hover:translate-x-[150%] duration-1000 transition-transform"
                />
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Envoi en cours...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    Envoyer la demande de transfert
                  </>
                )}
              </Button>
            </motion.div>
          </>
        )}

        {hasPendingRequest && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-8"
          >
            <p className="text-neutral-600">
              Vous avez déjà une demande de transfert en cours. 
              Veuillez attendre la réponse des chefs de quartier.
            </p>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
}
