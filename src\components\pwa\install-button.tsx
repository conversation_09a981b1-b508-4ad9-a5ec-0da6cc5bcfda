"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";
import { usePWA } from "@/hooks/use-pwa";

export function InstallButton() {
  const { isInstallable, install } = usePWA();

  if (!isInstallable) return null;

  return (
    <Button onClick={install} variant="outline" size="sm">
      <Download className="mr-2 h-4 w-4" />
      Installer l'application
    </Button>
  );
}