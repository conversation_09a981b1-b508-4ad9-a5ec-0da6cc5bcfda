import { program } from "commander";
import { initializeAppwrite, resetInitializationStatus } from "@/lib/server/init";

program
  .option("-f, --force", "Force reinitialization")
  .option("-d, --database-only", "Initialize only database")
  .option("-s, --storage-only", "Initialize only storage")
  .option("-r, --reset", "Reset initialization status")
  .parse(process.argv);

const options = program.opts();

async function main() {
  console.log("\n🚀 Starting Appwrite initialization script...\n");

  try {
    // Reset si demandé
    if (options.reset) {
      await resetInitializationStatus();
      console.log("🔄 Initialization status reset");
      return;
    }

    // Initialisation avec force si spécifié
    await initializeAppwrite(options.force);

    if (options.databaseOnly) {
      console.log("📦 Database initialization completed");
    } else if (options.storageOnly) {
      console.log("📦 Storage initialization completed");
    } else {
      console.log("✨ Full initialization completed");
    }
  } catch (error) {
    console.error("\n❌ Error during initialization:", error);
    process.exit(1);
  }
}

main();