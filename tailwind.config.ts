import type { Config } from "tailwindcss";
import { theme } from "./src/styles/theme";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: ["class"],
  theme: {
    extend: {
      colors: {
        // Couleurs principales
        primary: theme.colors.primary,
        neutral: theme.colors.neutral,

        // Couleurs système
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",

        // Couleurs d'accent pour l'interface
        accent: {
          primary: '#004D40',    // Vert profond pour les éléments principaux
          secondary: '#00796B',  // Vert moyen pour les éléments secondaires
          tertiary: '#009688',   // Vert clair pour les accents tertiaires
          green: '#00C853',      // Vert vif pour les succès
          red: '#FF5252',        // Rouge pour les erreurs
          yellow: '#FFD740',     // Jaune pour les avertissements
          neutral: '#9E9E9E',    // Gris neutre pour les éléments désactivés
        },

        // États et alertes unifiés
        state: {
          // États sémantiques hérités
          success: theme.colors.semantic.success,
          warning: theme.colors.semantic.warning,
          error: theme.colors.semantic.error,
          info: theme.colors.semantic.info,
        },

        // Système d'alertes avec variants
        alert: {
          destructive: {
            DEFAULT: "hsl(var(--destructive))",
            foreground: "hsl(var(--destructive-foreground))",
          },
          success: {
            DEFAULT: "hsl(var(--success-base))",
            foreground: "hsl(var(--success-foreground))",
          },
          warning: {
            DEFAULT: "hsl(var(--warning-base))",
            foreground: "hsl(var(--warning-foreground))",
          },
        },

        // Couleurs de toast
        toast: {
          success: {
            bg: "#10B981",
            border: "#059669",
            text: "#FFFFFF"
          },
          warning: {
            bg: "#FFCB05",
            border: "#FFB905",
            text: "rgba(0, 0, 0, 0.8)"
          },
          info: {
            bg: "#3B82F6",
            border: "#2563EB",
            text: "#FFFFFF"
          },
          error: {
            bg: "#EF4444",
            border: "#DC2626",
            text: "#FFFFFF"
          }
        },
      },
      fontFamily: {
        sans: [theme.typography.fonts.sans],
        mono: [theme.typography.fonts.mono],
      },
      animation: {
        fadeIn: theme.effects.animations.fadeIn,
        slideUp: theme.effects.animations.slideUp,
        pulse: theme.effects.animations.pulse,
        'pulse-slow': theme.effects.animations.pulseSlow,
      },
      keyframes: {
        pulse: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '.5' },
        },
      },
      backgroundImage: {
        'gradient-primary': theme.effects.gradients.primary,
      },
      borderRadius: {
        DEFAULT: 'var(--radius)',
      },
      transitionDuration: {
        DEFAULT: '300ms',
      },
      ringColor: {
        DEFAULT: 'hsl(var(--ring))',
      },
      ringWidth: {
        DEFAULT: '2px',
      },
    },
  },
  plugins: [
    require("@tailwindcss/typography"),
    require("@tailwindcss/forms")({
      strategy: 'class',
    }),
    require("@tailwindcss/aspect-ratio"),
    require("tailwindcss-animate"),
  ],
} satisfies Config;

export default config;
