const fs = require('fs');
const path = require('path');
const https = require('https');

// Configurations des polices
const FONTS = {
  inter: {
    url: 'https://fonts.gstatic.com/s/inter/v13/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7.woff2',
    output: 'inter.woff2'
  },
  jetbrainsMono: {
    url: 'https://github.com/JetBrains/JetBrainsMono/raw/master/fonts/webfonts/JetBrainsMono-Regular.woff2',
    output: 'jetbrains-mono.woff2'
  }
};

// Créer le dossier fonts s'il n'existe pas
const fontsDir = path.join(__dirname, '../public/fonts');
if (!fs.existsSync(fontsDir)) {
  fs.mkdirSync(fontsDir, { recursive: true });
}

// Fonction pour télécharger un fichier
function downloadFont(url, filename) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(fontsDir, filename);
    const file = fs.createWriteStream(filePath);

    const request = https.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    }, (response) => {
      // Gérer les redirections
      if (response.statusCode === 302 || response.statusCode === 301) {
        file.close();
        downloadFont(response.headers.location, filename)
          .then(resolve)
          .catch(reject);
        return;
      }

      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download font: ${response.statusCode}`));
        return;
      }

      response.pipe(file);

      file.on('finish', () => {
        file.close();
        console.log(`✅ Downloaded: ${filename}`);
        resolve();
      });
    });

    request.on('error', (err) => {
      fs.unlink(filePath, () => {});
      reject(err);
    });
  });
}

async function generateFonts() {
  console.log('🎨 Generating optimized fonts...\n');

  try {
    // Nettoyer les anciens fichiers
    const existingFiles = fs.readdirSync(fontsDir);
    for (const file of existingFiles) {
      if (file.endsWith('.woff2')) {
        fs.unlinkSync(path.join(fontsDir, file));
      }
    }

    // Télécharger chaque police
    for (const [fontName, font] of Object.entries(FONTS)) {
      console.log(`📦 Processing ${fontName}...`);
      await downloadFont(font.url, font.output);
    }

    console.log('\n✨ All fonts generated successfully!');
  } catch (error) {
    console.error('\n❌ Error generating fonts:', error);
    process.exit(1);
  }
}

// Exécuter le script
generateFonts();
