"use client";

import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";

// Animation variants
const containerVariants = {
  initial: { opacity: 0 },
  animate: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

const floatingAnimation = {
  y: [0, -10, 0],
  transition: {
    duration: 3,
    repeat: Infinity,
    ease: "easeInOut",
  },
};

export default function NotFound() {
  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-[#004D40] via-[#00695C] to-[#00796B] flex items-center justify-center">
      {/* Motif de fond */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-10" />
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-black/5 to-black/20" />
      </div>

      {/* Cercles décoratifs animés */}
      <motion.div
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.2, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -left-32 -top-32 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-3xl opacity-30"
      />
      <motion.div
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.2, 0.3, 0.2],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -right-32 top-1/4 w-96 h-96 bg-red-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20"
      />
      <motion.div
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.25, 0.15, 0.25],
        }}
        transition={{
          duration: 7,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        className="absolute -bottom-32 left-1/3 w-96 h-96 bg-green-500 rounded-full mix-blend-multiply filter blur-3xl opacity-25"
      />

      {/* Contenu principal */}
      <div className="relative z-10 container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="initial"
          animate="animate"
          className="max-w-2xl mx-auto text-center"
        >
          {/* Logo */}
          <motion.div
            variants={itemVariants}
            className="mb-8 flex justify-center"
          >
            <div className="relative w-20 h-20 bg-white/10 rounded-2xl p-3 backdrop-blur-sm">
              <Image
                src="/logo.png"
                alt="Logo NCR"
                width={64}
                height={64}
                className="object-contain"
                priority
              />
            </div>
          </motion.div>

          {/* Titre animé */}
          <motion.div
            variants={itemVariants}
            className="mb-6"
          >
            <h1 className="text-8xl font-bold text-white mb-2">
              4
              <motion.span
                animate={floatingAnimation}
                className="inline-block text-yellow-400"
              >
                0
              </motion.span>
              4
            </h1>
            <p className="text-2xl text-white/90 font-medium">
              Page introuvable
            </p>
          </motion.div>

          {/* Message d'erreur */}
          <motion.div
            variants={itemVariants}
            className="mb-12"
          >
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <p className="text-lg text-white/80 leading-relaxed">
                La page que vous recherchez semble avoir été déplacée, supprimée ou
                n&apos;a jamais existé.
              </p>
            </div>
          </motion.div>

          {/* Suggestions */}
          <motion.div
            variants={itemVariants}
            className="grid md:grid-cols-2 gap-4 mb-8"
          >
            {[
              {
                title: "Retourner à l'accueil",
                description: "Revenez à la page principale",
                href: "/",
                icon: "🏠",
              },
              {
                title: "Contactez-nous",
                description: "Besoin d'aide ? Contactez notre support",
                href: "/contact",
                icon: "📞",
              },
            ].map((item, index) => (
              <Link
                key={index}
                href={item.href}
                className="group block bg-white/5 hover:bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/10 transition-all duration-300"
              >
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="text-2xl mb-2 block">{item.icon}</span>
                  <h3 className="text-white font-semibold mb-1 group-hover:text-yellow-400 transition-colors">
                    {item.title}
                  </h3>
                  <p className="text-sm text-white/70">{item.description}</p>
                </motion.div>
              </Link>
            ))}
          </motion.div>

          {/* Retour rapide */}
          <motion.div variants={itemVariants}>
            <Link
              href="/"
              className="inline-flex items-center gap-2 text-white/90 hover:text-white transition-colors"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Retour à la page d&apos;accueil
            </Link>
          </motion.div>
        </motion.div>
      </div>

      {/* Bordures décoratives */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-yellow-400/20 via-red-500/20 to-green-400/20" />
        <div className="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-green-400/20 via-red-500/20 to-yellow-400/20" />
        <div className="absolute top-0 left-0 h-full w-2 bg-gradient-to-b from-yellow-400/20 via-red-500/20 to-green-400/20" />
        <div className="absolute top-0 right-0 h-full w-2 bg-gradient-to-b from-green-400/20 via-red-500/20 to-yellow-400/20" />
      </div>
    </div>
  );
}
