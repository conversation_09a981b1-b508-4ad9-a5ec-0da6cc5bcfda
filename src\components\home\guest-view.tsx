"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { ImpactSection } from "./impact-section";
import { SolutionsSection } from "./solutions-section";
import { VisionSection } from "./vision-section";

export function GuestView() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-green-50/30 relative overflow-hidden">
      {/* Motifs de fond animés - optimisés pour mobile */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-[0.03] md:opacity-5 animate-slide-slow" />
      </div>

      {/* Cercles décoratifs avec animation - ajustés pour mobile */}
      <div className="absolute -left-24 -top-24 md:-left-48 md:-top-48 w-48 md:w-96 h-48 md:h-96 bg-[#004D40] rounded-full mix-blend-multiply filter blur-2xl md:blur-3xl opacity-10 animate-blob" />
      <div className="absolute -right-24 -top-24 md:-right-48 md:-top-48 w-48 md:w-96 h-48 md:h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-2xl md:blur-3xl opacity-10 animate-blob animation-delay-2000" />
      <div className="absolute -bottom-24 left-24 md:-bottom-48 md:left-48 w-48 md:w-96 h-48 md:h-96 bg-red-500 rounded-full mix-blend-multiply filter blur-2xl md:blur-3xl opacity-10 animate-blob animation-delay-4000" />

      {/* Contenu principal */}
      <main className="container mx-auto px-4 sm:px-6 py-12 md:py-24 relative z-10">
        <div className="grid lg:grid-cols-2 gap-8 md:gap-12 items-center">
          {/* Section texte */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            className="space-y-6 md:space-y-8"
          >
            <div className="space-y-4">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight">
                <span className="bg-gradient-to-r from-[#004D40] to-[#00796B] bg-clip-text text-transparent">
                  Certificats de Résidence
                </span>
                <br />
                <span className="bg-gradient-to-r from-yellow-600 via-red-600 to-[#004D40] bg-clip-text text-transparent animate-gradient">
                  Nouvelle Génération
                </span>
              </h1>
              <p className="text-lg md:text-xl text-gray-600 leading-relaxed">
                Modernisez votre expérience administrative avec notre plateforme
                innovante de gestion des certificats de résidence.
              </p>
            </div>

            {/* Caractéristiques - Responsive grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
              {[
                {
                  icon: "🔒",
                  title: "Sécurisé",
                  desc: "Protection des données",
                },
                { icon: "⚡", title: "Rapide", desc: "Traitement en 48h" },
                { icon: "📱", title: "Mobile", desc: "Accessible partout" },
                { icon: "✨", title: "Simple", desc: "Interface intuitive" },
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  className="group p-4 rounded-2xl bg-white/50 backdrop-blur-sm border border-gray-100 hover:bg-white hover:shadow-lg hover:border-gray-200 transition-all duration-300"
                >
                  <div className="text-2xl md:text-3xl mb-2 transform transition-transform group-hover:scale-110">
                    {feature.icon}
                  </div>
                  <h3 className="font-semibold text-gray-900">
                    {feature.title}
                  </h3>
                  <p className="text-sm text-gray-600">{feature.desc}</p>
                </motion.div>
              ))}
            </div>

            {/* Call to action - Responsive buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.4 }}
              className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4"
            >
              <Link
                href="/connexion"
                className="flex-1 sm:flex-none text-center px-6 sm:px-8 py-3 rounded-full bg-gradient-to-r from-[#004D40] via-[#00796B] to-[#004D40] text-white font-medium shadow-lg shadow-green-600/20 hover:shadow-xl hover:shadow-green-600/30 transform hover:-translate-y-0.5 transition-all duration-300 bg-[length:200%_100%] hover:bg-right animate-gradient"
              >
                Commencer maintenant
              </Link>
              <Link
                href="/a-propos"
                className="flex-1 sm:flex-none text-center px-6 sm:px-8 py-3 rounded-full border border-gray-200 text-gray-600 font-medium hover:bg-gray-50 hover:border-gray-300 transform hover:-translate-y-0.5 transition-all duration-300"
              >
                En savoir plus
              </Link>
            </motion.div>
          </motion.div>

          {/* Section illustration - Masquée sur mobile, visible sur desktop */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.7 }}
            className="relative h-[400px] lg:h-[600px] hidden lg:block"
          >
            {/* Cercle décoratif principal */}
            <div className="absolute inset-0">
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[300px] md:w-[400px] h-[300px] md:h-[400px] bg-gradient-to-br from-[#004D40] via-yellow-500 to-red-500 rounded-full opacity-20 blur-3xl animate-pulse-slow" />
            </div>

            {/* Images flottantes */}
            <div className="relative h-full">
              <motion.div
                animate={{
                  y: [0, -20, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[250px] md:w-[300px] aspect-[3/4] bg-white rounded-2xl shadow-2xl p-4 md:p-6 transform rotate-3"
              >
                <div className="h-full rounded-lg bg-gradient-to-br from-[#004D40] to-[#00796B] p-4 text-white">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-10 md:w-12 h-10 md:h-12 rounded-full bg-white/20" />
                    <div className="space-y-2">
                      <div className="h-2 w-20 md:w-24 rounded-full bg-white/20" />
                      <div className="h-2 w-24 md:w-32 rounded-full bg-white/20" />
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="h-2 w-full rounded-full bg-white/20" />
                    <div className="h-2 w-5/6 rounded-full bg-white/20" />
                    <div className="h-2 w-4/6 rounded-full bg-white/20" />
                  </div>
                </div>
              </motion.div>

              <motion.div
                animate={{
                  y: [0, 20, 0],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="absolute top-1/4 right-0 w-[160px] md:w-[200px] aspect-[3/4] bg-white rounded-2xl shadow-2xl p-3 md:p-4 transform -rotate-6"
              >
                <div className="h-full rounded-lg border-2 border-[#004D40]/20 p-2 md:p-3">
                  <div className="flex items-center gap-2 md:gap-3 mb-3">
                    <div className="w-6 md:w-8 h-6 md:h-8 rounded-full bg-[#004D40]/10" />
                    <div className="space-y-1 md:space-y-1.5">
                      <div className="h-1.5 w-12 md:w-16 rounded-full bg-[#004D40]/10" />
                      <div className="h-1.5 w-16 md:w-20 rounded-full bg-[#004D40]/10" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-1.5 w-full rounded-full bg-[#004D40]/10" />
                    <div className="h-1.5 w-5/6 rounded-full bg-[#004D40]/10" />
                    <div className="h-1.5 w-4/6 rounded-full bg-[#004D40]/10" />
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* Section statistiques - Responsive grid */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.6 }}
          className="mt-12 md:mt-24 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-8"
        >
          {[
            { number: "24/7", label: "Disponibilité" },
            { number: "100%", label: "Sécurisé" },
            { number: "48h", label: "Délai maximum" },
            { number: "100%", label: "Satisfait" },
          ].map((stat, index) => (
            <div key={index} className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-[#004D40] via-yellow-600 to-red-600 rounded-2xl blur opacity-0 group-hover:opacity-20 transition-opacity" />
              <div className="relative p-4 md:p-6 text-center rounded-2xl bg-white/50 backdrop-blur-sm border border-gray-100 hover:bg-white hover:shadow-lg hover:border-gray-200 transition-all duration-300">
                <div className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-[#004D40] via-yellow-600 to-red-600 bg-clip-text text-transparent">
                  {stat.number}
                </div>
                <div className="text-sm md:text-base text-gray-600 mt-1">
                  {stat.label}
                </div>
              </div>
            </div>
          ))}
        </motion.div>

        {/* Nouvelle section Services */}
        <SolutionsSection />

        {/* Nouvelle section Vision & Mission */}
        <VisionSection />

        {/* Nouvelle section Impact & Avantages */}
        <ImpactSection />
      </main>
    </div>
  );
}
