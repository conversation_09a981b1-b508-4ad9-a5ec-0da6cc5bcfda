import { initiatePayment } from "@/actions/payment";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { useState } from "react";

export function usePaymentFlow() {
  const router = useRouter();
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);

  const startPaymentFlow = async (params: {
    amount: number;
    certificateId: string;
    returnUrl?: string;
  }) => {
    try {
      setIsProcessing(true);
      const result = await initiatePayment({
        amount: params.amount,
        provider: "orange-money",
        metadata: {
          certificateId: params.certificateId,
          type: "certificate_download",
          returnUrl:
            params.returnUrl ||
            `/dashboard/certificates/${params.certificateId}`,
        },
      });

      if (result.success && result.paymentUrl) {
        router.push(result.paymentUrl);
      }
    } catch (error: any) {
      toast({
        title: "Erreur de paiement",
        description:
          error.message ||
          "Une erreur est survenue lors de l'initiation du paiement",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    startPaymentFlow,
    isProcessing,
  };
}
