import { civilServices } from "@/data/civil-services";
import { motion } from "framer-motion";
import { ServiceCard } from "./service-card";

export function SolutionsSection() {
  return (
    <section className="py-12 md:py-16 xl:py-24 bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5">
      <div className="container mx-auto px-4 md:px-6 xl:px-8 max-w-7xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          className="text-center mb-10 md:mb-14 xl:mb-20"
        >
          <h2 className="text-2xl md:text-3xl xl:text-5xl font-bold bg-gradient-to-r from-[#004D40] to-[#00796B] bg-clip-text text-transparent leading-tight">
            Une Solution Complète pour l'État Civil
          </h2>
          <p className="mt-3 md:mt-4 text-sm md:text-base xl:text-lg text-neutral-600 max-w-2xl md:max-w-3xl mx-auto">
            Découvrez notre suite complète de services numériques pour
            moderniser l'administration
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 xl:gap-8">
          {civilServices.map((service, index) => (
            <ServiceCard
              key={service.id}
              {...service}
              delay={0.2 + index * 0.1}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
