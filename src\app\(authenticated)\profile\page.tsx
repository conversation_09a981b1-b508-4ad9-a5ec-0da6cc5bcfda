import { getCurrentUser } from "@/actions/auth/session";
import { ProfileManagement } from "@/components/profile/profile-management";

export const dynamic = "force-dynamic";

export default async function ProfilePage() {
  const { user } = await getCurrentUser();

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5">
      {/* Fond décoratif */}
      <div className="absolute inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(0,77,64,0.08),transparent_50%)]" />
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-[0.02]" />
      </div>

      <div className="container mx-auto px-6 py-8 max-w-[1200px]">
        <ProfileManagement user={user} />
      </div>
    </div>
  );
}
