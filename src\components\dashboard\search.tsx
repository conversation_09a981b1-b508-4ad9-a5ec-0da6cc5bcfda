"use client";

import { cn } from "@/lib/utils/cn";

interface SearchProps extends React.HTMLAttributes<HTMLDivElement> {}

export function Search({ className, ...props }: SearchProps) {
  return (
    <div className={cn("relative w-full max-w-md", className)} {...props}>
      <input
        type="search"
        placeholder="Rechercher..."
        className="w-full rounded-full bg-muted px-4 py-2 pl-10 text-sm"
      />
      <svg
        className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
        />
      </svg>
    </div>
  );
}
