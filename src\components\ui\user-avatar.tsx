"use client";

import Image from "next/image";
import { cn } from "@/lib/utils/cn";
import { useState, useMemo, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface UserAvatarProps {
  name?: string;
  src?: string;
  className?: string;
  size?: "sm" | "md" | "lg";
  status?: "online" | "offline" | "away" | "busy";
  showStatus?: boolean;
  solid?: boolean;
}

// Tailles des conteneurs
const sizeClasses = {
  sm: "h-8 w-8",
  md: "h-10 w-10",
  lg: "h-12 w-12"
};

// Tailles des polices ajustées pour un centrage parfait
const fontSizeClasses = {
  sm: "text-[14px]",
  md: "text-[18px]",
  lg: "text-[22px]"
};

const statusColors = {
  online: "bg-accent-green",
  offline: "bg-accent-neutral",
  away: "bg-accent-yellow",
  busy: "bg-accent-red"
};

export function UserAvatar({ 
  name,
  src,
  className,
  size = "md",
  status,
  showStatus = false,
  solid = false
}: UserAvatarProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Simuler un délai de chargement minimal pour l'effet
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  const initials = useMemo(() => {
    if (!name) return "U";
    return name
      .split(' ')
      .map(part => part[0])
      .slice(0, 2)
      .join('')
      .toUpperCase();
  }, [name]);

  // Si pas d'image ou erreur, afficher les initiales avec effet de chargement
  if (!src || imageError) {
    return (
      <div className={cn(
        "relative rounded-full",
        sizeClasses[size],
        className
      )}>
        <AnimatePresence mode="wait">
          {isLoading ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className={cn(
                "absolute inset-0",
                "flex items-center justify-center",
                "bg-accent-primary/30"
              )}
            >
              <div className="w-1/2 h-1/2 rounded-full animate-pulse bg-accent-primary/50" />
            </motion.div>
          ) : (
            <motion.div
              key="content"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ type: "spring", stiffness: 300, damping: 25 }}
              className={cn(
                "absolute inset-0",
                "flex items-center justify-center",
                solid ? "bg-transparent" : "bg-accent-primary"
              )}
            >
              <div className={cn(
                "flex items-center justify-center",
                "w-full h-full",
                "leading-[1]"
              )}>
                <span className={cn(
                  "font-semibold text-white select-none",
                  fontSizeClasses[size],
                  "-translate-y-[1px]"
                )}>
                  {initials}
                </span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {showStatus && status && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute bottom-0 right-0 translate-x-1/4 translate-y-1/4"
          >
            <div className={cn(
              "rounded-full",
              "h-3 w-3",
              "ring-2 ring-white",
              statusColors[status]
            )} />
          </motion.div>
        )}
      </div>
    );
  }

  // Sinon afficher l'image
  return (
    <div className={cn(
      "relative rounded-full overflow-hidden",
      sizeClasses[size],
      className
    )}>
      <Image
        src={src}
        alt={name || "Avatar"}
        fill
        className="object-cover"
        onError={() => setImageError(true)}
      />
      {showStatus && status && (
        <div className="absolute bottom-0 right-0 translate-x-1/4 translate-y-1/4">
          <div className={cn(
            "rounded-full",
            "h-3 w-3",
            "ring-2 ring-white",
            statusColors[status]
          )} />
        </div>
      )}
    </div>
  );
}