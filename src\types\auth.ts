import { Models } from "node-appwrite";

// Type pour les préférences utilisateur
export type UserPreferences = Models.Preferences;
// Type pour l'utilisateur Appwrite
export type User = Models.User<UserPreferences>;

// Type pour les credentials de connexion
export interface LoginCredentials {
  email: string;
  password: string;
}

// Type pour la réponse d'authentification
export interface AuthResponse {
  success: boolean;
  user?: User;
  error?: string;
  token?: string;
}

// Type pour la session
export interface Session {
  userId: string;
  token: string;
  expiresAt: number;
}

// Type pour le contexte d'authentification
export interface AuthContext {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<AuthResponse>;
  signOut: () => Promise<void>;
}

// Type pour les erreurs d'authentification
export interface AuthError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Type pour les événements d'authentification
export type AuthEvent =
  | { type: "LOGIN_SUCCESS"; payload: User }
  | { type: "LOGIN_ERROR"; payload: AuthError }
  | { type: "LOGOUT" }
  | { type: "SESSION_EXPIRED" };