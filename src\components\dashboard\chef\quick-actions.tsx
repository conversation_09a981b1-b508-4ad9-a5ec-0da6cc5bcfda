"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { UserPlus2, FileSignature, ClipboardList, Users } from "lucide-react";
import Link from "next/link";
import { motion } from "framer-motion";

const actions = [
  {
    href: "/dashboard/certificates/pending",
    icon: ClipboardList,
    label: "Demandes en attente",
    description: "Gérer les certificats en attente de validation",
    color: "from-amber-500 to-orange-600",
    shadowColor: "shadow-amber-500/20",
  },
  {
    href: "/dashboard/certificates/sign",
    icon: FileSignature,
    label: "Signer des certificats",
    description: "<PERSON>ider et signer les certificats approuvés",
    color: "from-emerald-500 to-teal-600",
    shadowColor: "shadow-emerald-500/20",
  },
  {
    href: "/dashboard/agents/new",
    icon: UserPlus2,
    label: "Ajouter un agent",
    description: "Créer un nouveau compte agent",
    color: "from-blue-500 to-indigo-600",
    shadowColor: "shadow-blue-500/20",
  },
  {
    href: "/dashboard/agents",
    icon: Users,
    label: "G<PERSON>rer les agents",
    description: "Administrer les agents du quartier",
    color: "from-violet-500 to-purple-600",
    shadowColor: "shadow-violet-500/20",
  },
];

export function ChefQuickActions() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent">
          Actions rapides
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {actions.map((action, index) => (
          <motion.div
            key={action.href}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Link href={action.href} className="block h-full">
              <motion.div
                whileHover={{ y: -4 }}
                className={`relative h-full p-6 rounded-xl bg-gradient-to-br ${action.color} ${action.shadowColor} shadow-lg group transition-all duration-300`}
              >
                <div className="absolute inset-0 bg-white opacity-95 rounded-xl transition-opacity group-hover:opacity-90" />

                <div className="relative space-y-4">
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${action.color} p-2.5
                    shadow-lg ${action.shadowColor} transform group-hover:scale-110 transition-all duration-300`}>
                    <action.icon className="w-full h-full text-white" />
                  </div>

                  <div className="space-y-1">
                    <h3 className={`text-base font-semibold bg-gradient-to-r ${action.color} bg-clip-text text-transparent`}>
                      {action.label}
                    </h3>
                    <p className="text-sm text-neutral-600">
                      {action.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            </Link>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
