import { ROLES } from "@/actions/auth/constants";
import { getCurrentUser } from "@/actions/auth/session";
import { CertificatesTable } from "@/components/dashboard/admin/certificates/certificates-table";
import { DashboardHeader } from "@/components/dashboard/header";
import { NotAuthorized } from "@/components/shared/not-authorized";

export const dynamic = "force-dynamic";

export default async function CertificatesPage() {
  const { user } = await getCurrentUser();

  if (!user) {
    return <NotAuthorized />;
  }

  // Vérification du rôle admin
  if (user.prefs?.role !== ROLES.ADMIN) {
    return <NotAuthorized />;
  }

  return (
    <div className="container mx-auto px-6 py-8 max-w-[1600px] space-y-8">
      <DashboardHeader
        heading="Gestion des certificats"
        text="Gérez l'ensemble des certificats de résidence de la plateforme NCR"
      />

      <div className="bg-white rounded-xl shadow-sm border border-neutral-200/60 p-6">
        <CertificatesTable />
      </div>
    </div>
  );
}
