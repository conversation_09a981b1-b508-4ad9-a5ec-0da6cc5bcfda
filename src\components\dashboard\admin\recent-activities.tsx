"use client";

import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { motion } from "framer-motion";

const activities = [
  {
    id: 1,
    type: "certificate_created",
    user: "<PERSON> Doe",
    district: "Ratoma",
    timestamp: new Date(2024, 2, 15, 14, 30),
  },
  {
    id: 2,
    type: "chief_added",
    user: "<PERSON>",
    district: "Matoto",
    timestamp: new Date(2024, 2, 15, 13, 45),
  },
  // ... autres activités
];

export function AdminRecentActivities() {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "certificate_created":
        return "📄";
      case "chief_added":
        return "👤";
      default:
        return "📌";
    }
  };

  const getActivityText = (activity: (typeof activities)[0]) => {
    switch (activity.type) {
      case "certificate_created":
        return `Nouveau certificat créé par ${activity.user} (${activity.district})`;
      case "chief_added":
        return `Nouveau chef de quartier ajouté : ${activity.user} (${activity.district})`;
      default:
        return "Action inconnue";
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-6">
        Activités récentes
      </h2>
      <div className="space-y-4">
        {activities.map((activity, index) => (
          <motion.div
            key={activity.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-start gap-4 p-4 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <span className="text-2xl">{getActivityIcon(activity.type)}</span>
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-900">
                {getActivityText(activity)}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {formatDistanceToNow(activity.timestamp, {
                  addSuffix: true,
                  locale: fr,
                })}
              </p>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
