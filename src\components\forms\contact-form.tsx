"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Loader } from "@/components/ui/loader";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useContactForm } from "@/hooks/use-contact-form";
import { serviceOptions } from "@/schemas/contact";
import { motion } from "framer-motion";
import Link from "next/link";

export function ContactForm() {
  const { form, isSubmitting, onSubmit } = useContactForm();

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom complet</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Votre nom"
                      className="bg-white/50 backdrop-blur-sm border-neutral-200/60
                        focus:ring-2 focus:ring-accent-primary/20 focus:border-accent-primary/30"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="<EMAIL>"
                      className="bg-white/50 backdrop-blur-sm border-neutral-200/60
                        focus:ring-2 focus:ring-accent-primary/20 focus:border-accent-primary/30"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <FormField
            control={form.control}
            name="service"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Service</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger
                      className="bg-white/50 backdrop-blur-sm border-neutral-200/60
                      focus:ring-2 focus:ring-accent-primary/20 focus:border-accent-primary/30"
                    >
                      <SelectValue placeholder="Sélectionnez un département" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {serviceOptions.map((option) => (
                      <SelectItem
                        key={option.value}
                        value={option.value}
                        className="flex flex-col items-start py-3"
                      >
                        <span className="font-medium">{option.label}</span>
                        <span className="text-xs ml-2 text-neutral-500">
                          {option.description}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <FormField
            control={form.control}
            name="subject"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Sujet</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Sujet de votre message"
                    className="bg-white/50 backdrop-blur-sm border-neutral-200/60
                      focus:ring-2 focus:ring-accent-primary/20 focus:border-accent-primary/30"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <FormField
            control={form.control}
            name="message"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Message</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    rows={4}
                    placeholder="Votre message..."
                    className="bg-white/50 backdrop-blur-sm border-neutral-200/60
                      focus:ring-2 focus:ring-accent-primary/20 focus:border-accent-primary/30
                      resize-none"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <FormField
            control={form.control}
            name="consent"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    J&apos;accepte que mes données soient traitées conformément
                    à la{" "}
                    <Link
                      href="/confidentialite"
                      className="text-accent-primary hover:underline"
                    >
                      politique de confidentialité
                    </Link>
                  </FormLabel>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="relative"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-accent-primary to-accent-secondary opacity-10 blur-xl" />
          <Button
            type="submit"
            disabled={isSubmitting}
            className="w-full relative overflow-hidden group bg-gradient-to-r from-accent-primary to-accent-secondary
              hover:from-accent-primary/90 hover:to-accent-secondary/90 text-white font-medium
              shadow-lg shadow-accent-primary/20 hover:shadow-xl hover:shadow-accent-primary/30
              transform hover:-translate-y-0.5 transition-all duration-300"
          >
            <span
              className="absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(255,255,255,0.2)_50%,transparent_100%)]
              translate-x-[-150%] group-hover:translate-x-[150%] duration-1000 transition-transform"
            />
            {isSubmitting ? (
              <span className="flex items-center justify-center">
                <Loader size="sm" variant="ghost" className="mr-2" />
                Envoi en cours...
              </span>
            ) : (
              "Envoyer le message"
            )}
          </Button>
        </motion.div>
      </form>
    </Form>
  );
}
