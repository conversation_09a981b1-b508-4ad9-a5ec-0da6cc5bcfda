import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { useCertificates } from "@/hooks/use-certificates";
import { useUser } from "@/hooks/use-user";
import { useMemo } from "react";

export type CitizenCertificateStats = {
  isNew: boolean;
  hasUpdates: boolean;
  timeToExpire?: number;
  isUrgent: boolean;
};

export function useCitizenCertificates(limit?: number) {
  const { user } = useUser();
  const { certificates, isLoading, error } = useCertificates();

  const citizenCertificates = useMemo(() => {
    if (!certificates || !user) return null;

    // Filtrer les certificats du citoyen
    const userCertificates = certificates
      .filter((cert) => cert.citizenId === user.$id)
      .map((cert) => {
        const createdAt = new Date(cert.$createdAt);
        const updatedAt = new Date(cert.$updatedAt);
        const now = new Date();

        // Calculer les statistiques pour chaque certificat
        const stats: CitizenCertificateStats = {
          // Nouveau si créé dans les dernières 24h
          isNew: now.getTime() - createdAt.getTime() < 24 * 60 * 60 * 1000,
          // Mis à jour si modifié dans les dernières 12h
          hasUpdates:
            updatedAt.getTime() > createdAt.getTime() &&
            now.getTime() - updatedAt.getTime() < 12 * 60 * 60 * 1000,
          // Urgent si en attente depuis plus de 48h
          isUrgent:
            cert.status === CERTIFICATE_STATUS.PENDING &&
            now.getTime() - createdAt.getTime() > 48 * 60 * 60 * 1000,
        };

        // Calculer le temps avant expiration pour les certificats délivrés
        if (cert.status === CERTIFICATE_STATUS.DELIVERED && cert.validUntil) {
          const validUntil = new Date(cert.validUntil);
          stats.timeToExpire = Math.ceil(
            (validUntil.getTime() - now.getTime()) / (24 * 60 * 60 * 1000)
          );
        }

        return {
          ...cert,
          stats,
        };
      })
      .sort((a, b) => {
        // Prioriser les certificats urgents
        if (a.stats.isUrgent !== b.stats.isUrgent) {
          return a.stats.isUrgent ? -1 : 1;
        }
        // Puis par date de mise à jour
        return (
          new Date(b.$updatedAt).getTime() - new Date(a.$updatedAt).getTime()
        );
      });

    return limit ? userCertificates.slice(0, limit) : userCertificates;
  }, [certificates, user, limit]);

  return {
    certificates: citizenCertificates,
    isLoading,
    error,
  };
}
