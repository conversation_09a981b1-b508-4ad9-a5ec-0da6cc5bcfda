"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ROLES, STATUS } from "@/actions/auth/constants";
import { Search, X } from "lucide-react";

export function UsersTableFilters() {
  const [filters, setFilters] = useState({
    search: "",
    role: "",
    status: "",
    district: "",
  });

  const handleReset = () => {
    setFilters({
      search: "",
      role: "",
      status: "",
      district: "",
    });
  };

  return (
    <div className="bg-white p-4 rounded-xl shadow-sm space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Rechercher..."
            value={filters.search}
            onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            className="pl-9"
          />
        </div>

        <Select
          value={filters.role}
          onValueChange={(value) => setFilters({ ...filters, role: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Rôle" />
          </SelectTrigger>
          <SelectContent>
            {Object.values(ROLES).map((role) => (
              <SelectItem key={role} value={role}>
                {role.charAt(0).toUpperCase() + role.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={filters.status}
          onValueChange={(value) => setFilters({ ...filters, status: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Statut" />
          </SelectTrigger>
          <SelectContent>
            {Object.values(STATUS).map((status) => (
              <SelectItem key={status} value={status}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={filters.district}
          onValueChange={(value) => setFilters({ ...filters, district: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Quartier" />
          </SelectTrigger>
          <SelectContent>
            {/* Liste des quartiers à charger depuis l'API */}
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end">
        <Button
          variant="outline"
          size="sm"
          onClick={handleReset}
          className="flex items-center gap-2"
        >
          <X className="w-4 h-4" />
          Réinitialiser les filtres
        </Button>
      </div>
    </div>
  );
}