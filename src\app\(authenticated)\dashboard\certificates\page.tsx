"use client";

import { getAllCertificates } from "@/actions/certificates";
import { CertificateActions } from "@/components/certificates/certificate-actions";
import { StatusBadge } from "@/components/certificates/status-badge";
import { DashboardHeader } from "@/components/dashboard/header";
import { But<PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/hooks/use-auth";
import { cn } from "@/lib/utils/cn";
import { useQuery } from "@tanstack/react-query";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import {
  CalendarRange,
  Filter,
  Loader2,
  Search,
  UserCircle2,
  MapPin,
  FileText,
  ArrowUpDown,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";
import { CERTIFICATE_TYPE } from "@/actions/auth/constants";

const glassEffect = `
  bg-white/80
  border border-neutral-100
  shadow-[0_4px_16px_0_rgba(31,38,135,0.07)]
  hover:shadow-[0_4px_16px_0_rgba(31,38,135,0.1)]
  transition-all duration-300
`;

const glassCardEffect = `
  bg-white/70
  border border-neutral-100
  shadow-[0_2px_8px_0_rgba(31,38,135,0.03)]
  hover:shadow-[0_2px_8px_0_rgba(31,38,135,0.06)]
  hover:bg-white/80
  transition-all duration-300
`;

const ITEMS_PER_PAGE = 10;

const TYPE_LABELS = {
  [CERTIFICATE_TYPE.RESIDENCE]: "Certificat de Résidence",
  [CERTIFICATE_TYPE.BIRTH_CERTIFICATE]: "Certificat de Naissance",
  [CERTIFICATE_TYPE.DEATH_CERTIFICATE]: "Certificat de Décès",
  [CERTIFICATE_TYPE.MARRIAGE_CERTIFICATE]: "Certificat de Mariage",
  [CERTIFICATE_TYPE.DIVORCE_CERTIFICATE]: "Certificat de Divorce",
  [CERTIFICATE_TYPE.CHILD_BIRTH_CERTIFICATE]:
    "Certificat de Naissance d'Enfant",
  [CERTIFICATE_TYPE.CHILD_DEATH_CERTIFICATE]: "Certificat de Décès d'Enfant",
} as const;

const TYPE_COLORS = {
  [CERTIFICATE_TYPE.RESIDENCE]: "from-blue-500/80 to-blue-600/80",
  [CERTIFICATE_TYPE.BIRTH_CERTIFICATE]: "from-emerald-500/80 to-emerald-600/80",
  [CERTIFICATE_TYPE.DEATH_CERTIFICATE]: "from-purple-500/80 to-purple-600/80",
  [CERTIFICATE_TYPE.MARRIAGE_CERTIFICATE]: "from-pink-500/80 to-pink-600/80",
  [CERTIFICATE_TYPE.DIVORCE_CERTIFICATE]: "from-orange-500/80 to-orange-600/80",
  [CERTIFICATE_TYPE.CHILD_BIRTH_CERTIFICATE]:
    "from-yellow-500/80 to-yellow-600/80",
  [CERTIFICATE_TYPE.CHILD_DEATH_CERTIFICATE]: "from-red-500/80 to-red-600/80",
} as const;

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 20,
    },
  },
  hover: {
    y: -2,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 30,
    },
  },
};

export default function CertificatesPage() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [hoveredRow, setHoveredRow] = useState<string | null>(null);

  const { data, isLoading } = useQuery({
    queryKey: ["certificates", searchQuery, page, sortBy, sortOrder],
    queryFn: async () => {
      const response = await getAllCertificates({
        search: searchQuery,
        limit: ITEMS_PER_PAGE,
        offset: (page - 1) * ITEMS_PER_PAGE,
        sortBy,
        sortOrder,
      });
      return response;
    },
  });

  if (!user) return null;

  const columns = [
    {
      header: "Référence",
      accessorKey: "reference",
      cell: (props: any) => (
        <motion.div
          initial="hidden"
          animate="visible"
          whileHover="hover"
          variants={itemVariants}
          className={`${glassCardEffect} font-medium text-neutral-900 px-4 py-2 rounded-lg`}
        >
          <span className="relative z-10">{props.getValue()}</span>
        </motion.div>
      ),
    },
    {
      header: "Type",
      accessorKey: "type",
      cell: (props: any) => {
        const type = props.getValue() as keyof typeof TYPE_LABELS;
        return (
          <motion.div
            initial="hidden"
            animate="visible"
            whileHover="hover"
            variants={itemVariants}
            className="relative group"
          >
            <div className={`${glassCardEffect} relative px-4 py-2 rounded-lg`}>
              <span
                className={`text-sm font-medium bg-gradient-to-br ${TYPE_COLORS[type]} bg-clip-text text-transparent`}
              >
                {TYPE_LABELS[type] || type}
              </span>
            </div>
          </motion.div>
        );
      },
    },
    {
      header: "Statut",
      accessorKey: "status",
      cell: (props: any) => (
        <motion.div
          initial="hidden"
          animate="visible"
          whileHover="hover"
          variants={itemVariants}
          className={`${glassCardEffect} inline-block rounded-xl overflow-hidden`}
        >
          <StatusBadge
            status={props.getValue()}
            timestamp={props.row.original.updatedAt}
          />
        </motion.div>
      ),
    },
    {
      header: "Demandeur",
      accessorKey: "citizenName",
      cell: (props: any) => (
        <motion.div
          initial="hidden"
          animate="visible"
          whileHover="hover"
          variants={itemVariants}
          className={`${glassCardEffect} flex items-center gap-3 px-4 py-2 rounded-xl`}
        >
          <div className="relative">
            <div className="absolute inset-0 bg-accent-primary/10 blur-md rounded-full" />
            <UserCircle2 className="relative z-10 w-5 h-5 text-accent-primary/80" />
          </div>
          <div className="font-medium text-neutral-900">
            {props.getValue() || "N/A"}
          </div>
        </motion.div>
      ),
    },
    {
      header: "Date de demande",
      accessorKey: "createdAt",
      cell: (props: any) => (
        <motion.div
          initial="hidden"
          animate="visible"
          whileHover="hover"
          variants={itemVariants}
          className={`${glassCardEffect} flex items-center gap-3 px-4 py-2 rounded-xl`}
        >
          <div className="relative">
            <div className="absolute inset-0 bg-accent-primary/10 blur-md rounded-full" />
            <CalendarRange className="relative z-10 w-5 h-5 text-accent-primary/80" />
          </div>
          <span className="text-neutral-600">
            {formatDistanceToNow(new Date(props.getValue()), {
              addSuffix: true,
              locale: fr,
            })}
          </span>
        </motion.div>
      ),
    },
    {
      header: "Quartier",
      accessorKey: "quartierInfo",
      cell: (props: any) => (
        <motion.div
          initial="hidden"
          animate="visible"
          whileHover="hover"
          variants={itemVariants}
          className={`${glassCardEffect} flex items-center gap-3 px-4 py-2 rounded-xl`}
        >
          <div className="relative">
            <div className="absolute inset-0 bg-accent-primary/10 blur-md rounded-full" />
            <MapPin className="relative z-10 w-5 h-5 text-accent-primary/80" />
          </div>
          <div className="text-sm text-neutral-700 font-medium">
            {props.getValue() || "N/A"}
          </div>
        </motion.div>
      ),
    },
    {
      header: "Actions",
      id: "actions",
      cell: (props: any) => (
        <motion.div
          initial={{ opacity: 0.6 }}
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
          className="flex items-center gap-2"
        >
          <CertificateActions
            certificate={props.row.original}
            userRole={
              user.prefs?.role as "admin" | "chef" | "agent" | "citizen"
            }
          />
        </motion.div>
      ),
    },
  ];

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5">
      {/* Fond simplifié */}
      <div className="fixed inset-0 -z-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(0,77,64,0.05),transparent_50%)]" />
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-[0.02]" />
        <motion.div
          animate={{
            opacity: [0.03, 0.05, 0.03],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            repeatType: "reverse",
          }}
          className="absolute inset-0 bg-gradient-to-tr from-accent-primary/5 via-transparent to-accent-secondary/5"
        />
      </div>

      <div className="container mx-auto px-6 py-8 space-y-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`${glassEffect} rounded-xl p-8 relative overflow-hidden`}
        >
          <div className="absolute -top-12 -right-12 w-48 h-48 bg-accent-primary/5 rounded-full" />
          <div className="absolute -bottom-12 -left-12 w-48 h-48 bg-accent-secondary/5 rounded-full" />

          <div className="relative z-10">
            <DashboardHeader
              heading="Tous les certificats"
              text={`Liste ${
                user.prefs?.role === "admin"
                  ? "complète"
                  : user.prefs?.role === "chef"
                  ? "de votre quartier"
                  : user.prefs?.role === "agent"
                  ? "des certificats assignés"
                  : "de vos certificats"
              }`}
            >
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="flex items-center gap-4"
              >
                <motion.div variants={itemVariants} className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-accent-primary/5 to-accent-secondary/5 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity" />
                  <div className="relative">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                    <Input
                      placeholder="Rechercher par référence..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-12 w-[300px] bg-white/50 backdrop-blur-sm border-neutral-200/50
                        focus:ring-2 focus:ring-accent-primary/20 focus:border-accent-primary/30
                        transition-all duration-300"
                    />
                  </div>
                </motion.div>

                <motion.div variants={itemVariants}>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className={`${glassCardEffect} w-[180px]`}>
                      <Filter className="w-4 h-4 mr-2 text-accent-primary/80" />
                      <SelectValue placeholder="Trier par" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="createdAt">
                        Date de création
                      </SelectItem>
                      <SelectItem value="reference">Référence</SelectItem>
                      <SelectItem value="type">Type</SelectItem>
                      <SelectItem value="status">Statut</SelectItem>
                    </SelectContent>
                  </Select>
                </motion.div>

                <motion.div variants={itemVariants}>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() =>
                      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                    }
                    className={`${glassCardEffect} transition-all duration-200`}
                  >
                    <motion.div
                      animate={{ rotate: sortOrder === "asc" ? 0 : 180 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ArrowUpDown className="w-4 h-4 text-accent-primary/80" />
                    </motion.div>
                  </Button>
                </motion.div>
              </motion.div>
            </DashboardHeader>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className={`${glassEffect} rounded-xl overflow-hidden relative`}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-xl" />

          <div className="relative z-10">
            <AnimatePresence mode="wait">
              {isLoading ? (
                <motion.div
                  key="loading"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="flex flex-col items-center justify-center h-[400px] gap-4"
                >
                  <Loader2 className="w-12 h-12 animate-spin text-accent-primary" />
                  <p className="text-sm text-neutral-500 animate-pulse">
                    Chargement des certificats...
                  </p>
                </motion.div>
              ) : data?.certificates?.length === 0 ? (
                <motion.div
                  key="empty"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  className="flex flex-col items-center justify-center h-[400px] gap-4"
                >
                  <motion.div
                    initial={{ rotate: -10, scale: 0.9 }}
                    animate={{ rotate: 0, scale: 1 }}
                    transition={{
                      type: "spring",
                      stiffness: 100,
                      damping: 15,
                    }}
                  >
                    <FileText className="w-16 h-16 text-neutral-400" />
                  </motion.div>
                  <div className="text-center">
                    <p className="text-lg font-medium text-neutral-900">
                      Aucun certificat trouvé
                    </p>
                    <p className="text-sm text-neutral-500">
                      Modifiez vos critères de recherche pour voir plus de
                      résultats
                    </p>
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key="table"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <DataTable
                    columns={columns}
                    data={data?.certificates || []}
                    pageCount={Math.ceil(
                      (data?.pagination?.total || 0) / ITEMS_PER_PAGE
                    )}
                    currentPage={page}
                    onPageChange={setPage}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
