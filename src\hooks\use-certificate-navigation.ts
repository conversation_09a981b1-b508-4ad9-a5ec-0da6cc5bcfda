import { usePathname, useRouter } from "next/navigation";
import { useCallback, useEffect } from "react";

const PREVIOUS_URL_KEY = "certificate_previous_url";
const VALID_CERTIFICATE_ROUTES = [
  "/dashboard/certificates",
  "/dashboard/certificates/pending",
  "/dashboard/certificates/delivered",
  "/dashboard/certificates/rejected",
  "/dashboard/certificates/sign",
];

export function useCertificateNavigation() {
  const router = useRouter();
  const pathname = usePathname();

  const isDetailsPage = useCallback(
    (path = pathname) => {
      return (
        path.startsWith("/dashboard/certificates/") &&
        !path.includes("/pending") &&
        !path.includes("/delivered") &&
        !path.includes("/rejected") &&
        !path.includes("/sign") &&
        path.split("/").length === 4
      );
    },
    [pathname]
  );

  // Mémoriser l'URL précédente lorsqu'on n'est pas sur une page de détails
  useEffect(() => {
    if (
      !isDetailsPage(pathname) &&
      VALID_CERTIFICATE_ROUTES.some((route) => pathname.startsWith(route))
    ) {
      try {
        localStorage.setItem(PREVIOUS_URL_KEY, pathname);
      } catch (error) {
        console.error(
          "Erreur lors de la sauvegarde de l'URL précédente:",
          error
        );
      }
    }
  }, [pathname, isDetailsPage]);

  const getReturnPath = () => {
    try {
      // Récupérer l'URL précédente du localStorage
      const previousUrl = localStorage.getItem(PREVIOUS_URL_KEY);

      // Vérifier si l'URL précédente est valide
      if (
        previousUrl &&
        VALID_CERTIFICATE_ROUTES.some((route) => previousUrl.startsWith(route))
      ) {
        return previousUrl;
      }

      // Fallback sur la page par défaut si l'URL précédente n'est pas valide
      return "/dashboard/certificates/pending";
    } catch (error) {
      console.error(
        "Erreur lors de la récupération de l'URL précédente:",
        error
      );
      return "/dashboard/certificates/pending";
    }
  };

  const navigateBack = () => {
    const returnPath = getReturnPath();
    router.push(returnPath);
  };

  const navigateToDetails = (certificateId: string) => {
    router.push(`/dashboard/certificates/${certificateId}`);
  };

  return {
    navigateBack,
    navigateToDetails,
    isDetailsPage,
    getReturnPath,
  };
}
