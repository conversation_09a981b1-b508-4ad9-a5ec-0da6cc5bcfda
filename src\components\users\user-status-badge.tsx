"use client";

import { STATUS } from "@/actions/auth/constants";
import { Badge } from "@/components/ui/badge";
import {
  USER_STATUS_COLORS,
  USER_STATUS_LABELS,
} from "@/constants/status-labels";
import { CheckCircle2, Clock, ShieldAlert, UserX2 } from "lucide-react";

const getStatusIcon = (status: STATUS) => {
  switch (status) {
    case STATUS.ACTIVE:
      return CheckCircle2;
    case STATUS.PENDING:
      return Clock;
    case STATUS.INACTIVE:
      return UserX2;
    case STATUS.BLOCKED:
      return ShieldAlert;
    default:
      return Clock;
  }
};

interface UserStatusBadgeProps {
  status: STATUS;
  size?: "sm" | "md" | "lg";
}

export function UserStatusBadge({ status, size = "sm" }: UserStatusBadgeProps) {
  const Icon = getStatusIcon(status);

  return (
    <Badge
      variant={USER_STATUS_COLORS[status] || "default"}
      size={size}
      icon={<Icon className="h-3.5 w-3.5" />}
    >
      {USER_STATUS_LABELS[status] || status}
    </Badge>
  );
}
