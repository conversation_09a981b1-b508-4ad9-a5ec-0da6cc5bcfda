import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  assignCertificate,
  unassignCertificate,
  rejectCertificate,
  verifyCertificate,
  signCertificate,
  deliverCertificate,
  markCertificateAsReady,
} from "@/actions/chef/certificates";
import { downloadCertificate } from "@/actions/certificates";

export function useCertificateActions() {
  const queryClient = useQueryClient();

  const handleSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["certificates"] });
    queryClient.invalidateQueries({ queryKey: ["pending-certificates"] });
    queryClient.invalidateQueries({ queryKey: ["certificate"] });
  };

  const assignMutation = useMutation({
    mutationFn: assignCertificate,
    onSuccess: handleSuccess,
  });

  const unassignMutation = useMutation({
    mutationFn: unassignCertificate,
    onSuccess: handleSuccess,
  });

  const rejectMutation = useMutation({
    mutationFn: rejectCertificate,
    onSuccess: handleSuccess,
  });

  const verifyMutation = useMutation({
    mutationFn: verifyCertificate,
    onSuccess: handleSuccess,
  });

  const signMutation = useMutation({
    mutationFn: ({
      certificateId,
      signatureFileId,
    }: {
      certificateId: string;
      signatureFileId: string;
    }) => signCertificate(certificateId, signatureFileId),
    onSuccess: handleSuccess,
  });

  const deliverMutation = useMutation({
    mutationFn: deliverCertificate,
    onSuccess: handleSuccess,
  });

  const downloadMutation = useMutation({
    mutationFn: downloadCertificate,
  });

  const markAsReadyMutation = useMutation({
    mutationFn: markCertificateAsReady,
    onSuccess: handleSuccess,
  });

  return {
    assign: assignMutation.mutateAsync,
    unassign: unassignMutation.mutateAsync,
    reject: rejectMutation.mutateAsync,
    verify: verifyMutation.mutateAsync,
    sign: signMutation.mutateAsync,
    deliver: deliverMutation.mutateAsync,
    download: downloadMutation.mutateAsync,
    markAsReady: markAsReadyMutation.mutateAsync,
    isLoading:
      assignMutation.isPending ||
      unassignMutation.isPending ||
      rejectMutation.isPending ||
      verifyMutation.isPending ||
      signMutation.isPending ||
      deliverMutation.isPending ||
      downloadMutation.isPending ||
      markAsReadyMutation.isPending,
  };
}
