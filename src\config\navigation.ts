import { ROLES } from "@/actions/auth/constants";
import { Baby, Home, Info, Mail } from "lucide-react";

// Types pour la navigation
export interface NavItem {
  title: string;
  href: string;
  icon?: any;
  description?: string;
  items?: NavItem[];
  roles?: ROLES[];
}

// Navigation principale
export const mainNavigation: NavItem[] = [
  {
    title: "Accueil",
    href: "/",
    icon: Home,
  },
  {
    title: "Services",
    href: "/services",
    items: [
      {
        title: "Déclaration de naissance",
        description: "Déclarer une nouvelle naissance",
        href: "/dashboard/declaration-naissance",
        icon: Baby,
        roles: [ROLES.CITIZEN, ROLES.AGENT, ROLES.CHEF],
      },
      // Autres services existants...
    ],
  },
  {
    title: "À propos",
    href: "/a-propos",
    icon: Info,
  },
  {
    title: "Contact",
    href: "/contact",
    icon: Mail,
  },
];

// Navigation des services
export const servicesNavigation: NavItem[] = [
  {
    title: "Déclaration de naissance",
    description:
      "Enregistrer une nouvelle naissance dans le registre d'état civil",
    href: "/dashboard/declaration-naissance",
    icon: Baby,
    roles: [ROLES.CITIZEN, ROLES.AGENT, ROLES.CHEF],
  },
  // ... autres services
];

// Fonction utilitaire pour filtrer la navigation selon le rôle
export function filterNavigationByRole(
  navigation: NavItem[],
  role?: ROLES | null
): NavItem[] {
  return navigation
    .map((item) => ({
      ...item,
      items: item.items
        ?.filter(
          (subItem) => !subItem.roles || !role || subItem.roles.includes(role)
        )
        .map((subItem) => ({
          ...subItem,
          items: subItem.items?.filter(
            (deepSubItem) =>
              !deepSubItem.roles || !role || deepSubItem.roles.includes(role)
          ),
        })),
    }))
    .filter((item) => !item.roles || !role || item.roles.includes(role));
}
