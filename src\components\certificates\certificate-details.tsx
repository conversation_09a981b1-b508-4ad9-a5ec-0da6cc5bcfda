"use client";

import { Certificate } from "@/actions/types";
import { CERTIFICATE_STATUS, CERTIFICATE_TYPE } from "@/actions/auth/constants";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { StatusBadge } from "./status-badge";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";

interface CertificateDetailsProps {
  certificate: Certificate;
  userRole: "chef" | "agent" | "citizen" | "admin";
}

const TYPE_LABELS = {
  [CERTIFICATE_TYPE.RESIDENCE]: "Certificat de Résidence",
  [CERTIFICATE_TYPE.BIRTH_CERTIFICATE]: "Certificat de Naissance",
  [CERTIFICATE_TYPE.DEATH_CERTIFICATE]: "Certificat de Décès",
  [CERTIFICATE_TYPE.MARRIAGE_CERTIFICATE]: "Certificat de Mariage",
  [CERTIFICATE_TYPE.DIVORCE_CERTIFICATE]: "Certificat de Divorce",
  [CERTIFICATE_TYPE.CHILD_BIRTH_CERTIFICATE]:
    "Certificat de Naissance d'Enfant",
  [CERTIFICATE_TYPE.CHILD_DEATH_CERTIFICATE]: "Certificat de Décès d'Enfant",
} as const;

export function CertificateDetails({
  certificate,
  userRole,
}: CertificateDetailsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Informations du certificat</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Informations de base */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-neutral-500">Référence</h3>
            <p className="mt-1 text-sm">{certificate.reference}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-neutral-500">Type</h3>
            <p className="mt-1 text-sm">
              {TYPE_LABELS[certificate.type as keyof typeof TYPE_LABELS]}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-neutral-500">Statut</h3>
            <div className="mt-1">
              <StatusBadge
                status={certificate.status}
                timestamp={certificate.updatedAt}
              />
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium text-neutral-500">
              Date de demande
            </h3>
            <p className="mt-1 text-sm">
              {formatDistanceToNow(new Date(certificate.createdAt), {
                addSuffix: true,
                locale: fr,
              })}
            </p>
          </div>
        </div>

        {/* Raison du rejet si applicable */}
        {certificate.status === CERTIFICATE_STATUS.REJECTED && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <h3 className="text-sm font-medium text-red-800 mb-2">
              Motif du rejet
            </h3>
            <p className="text-sm text-red-700">
              {certificate.rejectionReason}
            </p>
            {certificate.rejectedAt && (
              <div className="mt-2 flex items-center gap-2 text-xs text-red-600">
                <span>
                  {formatDistanceToNow(new Date(certificate.rejectedAt), {
                    addSuffix: true,
                    locale: fr,
                  })}
                </span>
                {certificate.rejectedByName && (
                  <>
                    <span>•</span>
                    <span>par {certificate.rejectedByName}</span>
                  </>
                )}
              </div>
            )}
          </div>
        )}

        {/* Motif */}
        <div>
          <h3 className="text-sm font-medium text-neutral-500">
            Motif de la demande
          </h3>
          <p className="mt-1 text-sm whitespace-pre-wrap">
            {certificate.motif}
          </p>
        </div>

        {/* Informations du demandeur */}
        <div>
          <h3 className="text-sm font-medium text-neutral-500 mb-2">
            Informations du demandeur
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-neutral-500">Nom</h4>
              <p className="mt-1 text-sm">
                {certificate.citizen?.nom || "N/A"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-neutral-500">Prénom</h4>
              <p className="mt-1 text-sm">
                {certificate.citizen?.prenom || "N/A"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-neutral-500">Email</h4>
              <p className="mt-1 text-sm">
                {certificate.citizen?.email || "N/A"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-neutral-500">
                Téléphone
              </h4>
              <p className="mt-1 text-sm">
                {certificate.citizen?.telephone || "N/A"}
              </p>
            </div>
          </div>
        </div>

        {/* Informations de traitement */}
        {(userRole === "admin" ||
          userRole === "chef" ||
          userRole === "agent") && (
          <div>
            <h3 className="text-sm font-medium text-neutral-500 mb-2">
              Informations de traitement
            </h3>
            <div className="grid grid-cols-2 gap-4">
              {certificate.agentId && (
                <>
                  <div>
                    <h4 className="text-sm font-medium text-neutral-500">
                      Agent assigné
                    </h4>
                    <p className="mt-1 text-sm">
                      {certificate.agent
                        ? `${certificate.agent.prenom} ${certificate.agent.nom}`
                        : "N/A"}
                    </p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-neutral-500">
                      Date d&apos;assignation
                    </h4>
                    <p className="mt-1 text-sm">
                      {certificate.assignedAt
                        ? formatDistanceToNow(
                            new Date(certificate.assignedAt),
                            {
                              addSuffix: true,
                              locale: fr,
                            }
                          )
                        : "N/A"}
                    </p>
                  </div>
                </>
              )}
              {certificate.verifiedAt && (
                <>
                  <div>
                    <h4 className="text-sm font-medium text-neutral-500">
                      Vérifié par
                    </h4>
                    <p className="mt-1 text-sm">
                      {`${certificate.chefQuartierName} (${certificate.chefQuartier?.telephone}) - ${certificate.verifiedBy}` ||
                        "N/A"}
                    </p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-neutral-500">
                      Notes de vérification
                    </h4>
                    <p className="mt-1 text-sm whitespace-pre-wrap">
                      {certificate.verificationNotes || "Aucune note"}
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
