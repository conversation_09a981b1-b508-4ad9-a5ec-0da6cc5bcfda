"use client";

import { Certificate } from "@/actions/types";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import {
  CheckCircle2,
  Clock,
  FileCheck,
  FileSignature,
  Send,
  UserCheck,
  XCircle,
  FileText,
  FileOutput,
  Ban,
} from "lucide-react";
import { cn } from "@/lib/utils/cn";

interface CertificateTimelineProps {
  certificate: Certificate;
}

const STATUS_CONFIG: Record<
  CERTIFICATE_STATUS,
  { icon: any; label: string; color: string }
> = {
  [CERTIFICATE_STATUS.DRAFT]: {
    icon: FileText,
    label: "Brouillon",
    color: "text-neutral-500",
  },
  [CERTIFICATE_STATUS.SUBMITTED]: {
    icon: Send,
    label: "Soumis",
    color: "text-blue-500",
  },
  [CERTIFICATE_STATUS.PENDING]: {
    icon: Clock,
    label: "En attente",
    color: "text-amber-500",
  },
  [CERTIFICATE_STATUS.VERIFIED]: {
    icon: FileCheck,
    label: "Vérifié",
    color: "text-teal-500",
  },
  [CERTIFICATE_STATUS.APPROVED]: {
    icon: CheckCircle2,
    label: "Approuvé",
    color: "text-emerald-500",
  },
  [CERTIFICATE_STATUS.READY]: {
    icon: FileCheck,
    label: "Prêt",
    color: "text-violet-500",
  },
  [CERTIFICATE_STATUS.SIGNED]: {
    icon: FileSignature,
    label: "Signé",
    color: "text-indigo-500",
  },
  [CERTIFICATE_STATUS.DELIVERED]: {
    icon: FileOutput,
    label: "Délivré",
    color: "text-green-500",
  },
  [CERTIFICATE_STATUS.REJECTED]: {
    icon: Ban,
    label: "Rejeté",
    color: "text-red-500",
  },
  [CERTIFICATE_STATUS.EXPIRED]: {
    icon: Ban,
    label: "Expiré",
    color: "text-neutral-500",
  },
};

const TIMELINE_STATUSES = [
  CERTIFICATE_STATUS.SUBMITTED,
  CERTIFICATE_STATUS.PENDING,
  CERTIFICATE_STATUS.VERIFIED,
  CERTIFICATE_STATUS.APPROVED,
  CERTIFICATE_STATUS.READY,
  CERTIFICATE_STATUS.SIGNED,
  CERTIFICATE_STATUS.DELIVERED,
];

export function CertificateTimeline({ certificate }: CertificateTimelineProps) {
  const currentStatusIndex = TIMELINE_STATUSES.indexOf(
    certificate.status as CERTIFICATE_STATUS
  );

  // Vérifier si le certificat est rejeté
  const isRejected = certificate.status === CERTIFICATE_STATUS.REJECTED;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Suivi de la demande</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative space-y-4">
          {/* Ligne verticale de connexion */}
          <div
            className={cn(
              "absolute left-[17px] top-[30px] bottom-4 w-[2px]",
              isRejected ? "bg-red-200" : "bg-neutral-200"
            )}
          />

          {/* Étapes */}
          {TIMELINE_STATUSES.map((status, index) => {
            const config = STATUS_CONFIG[status];
            const Icon = config.icon;
            const isCompleted = !isRejected && index <= currentStatusIndex;
            const isCurrent = !isRejected && index === currentStatusIndex;
            const isDisabled = isRejected && index > currentStatusIndex;
            const timestamp = (() => {
              switch (status) {
                case CERTIFICATE_STATUS.SUBMITTED:
                  return certificate.createdAt;
                case CERTIFICATE_STATUS.PENDING:
                  return certificate.assignedAt;
                case CERTIFICATE_STATUS.VERIFIED:
                  return certificate.verifiedAt;
                case CERTIFICATE_STATUS.READY:
                  return certificate.readyAt;
                case CERTIFICATE_STATUS.SIGNED:
                  return certificate.signedAt;
                case CERTIFICATE_STATUS.DELIVERED:
                  return certificate.deliveredAt;
                default:
                  return null;
              }
            })();

            return (
              <div
                key={status}
                className={cn("relative flex items-start gap-4 pl-9", {
                  "opacity-50": (!isCompleted && !isCurrent) || isDisabled,
                })}
              >
                <div
                  className={cn(
                    "absolute left-0 p-1 rounded-full bg-white ring-2",
                    isRejected && index === currentStatusIndex
                      ? "ring-red-500 text-red-500"
                      : isCompleted || isCurrent
                      ? `${config.color} ring-current`
                      : "ring-neutral-300"
                  )}
                >
                  <Icon
                    className={cn("h-4 w-4", {
                      "text-current":
                        isCompleted ||
                        isCurrent ||
                        (isRejected && index === currentStatusIndex),
                      "text-neutral-300":
                        (!isCompleted && !isCurrent) || isDisabled,
                    })}
                  />
                </div>
                <div className="flex-1 space-y-1">
                  <p
                    className={cn("text-sm font-medium", {
                      [config.color]: isCompleted || isCurrent,
                      "text-red-500":
                        isRejected && index === currentStatusIndex,
                      "text-neutral-500":
                        (!isCompleted && !isCurrent) || isDisabled,
                    })}
                  >
                    {config.label}
                  </p>
                  {timestamp && (
                    <p className="text-xs text-neutral-500">
                      {formatDistanceToNow(new Date(timestamp), {
                        addSuffix: true,
                        locale: fr,
                      })}
                    </p>
                  )}
                </div>
              </div>
            );
          })}

          {/* Afficher le statut rejeté si applicable */}
          {isRejected && (
            <div className="relative flex items-start gap-4 pl-9">
              <div className="absolute left-0 p-1 rounded-full bg-white ring-2 ring-red-500 text-red-500">
                <XCircle className="h-4 w-4" />
              </div>
              <div className="flex-1 space-y-1">
                <p className="text-sm font-medium text-red-500">
                  Demande rejetée
                </p>
                {certificate.rejectionReason && (
                  <p className="text-sm text-neutral-600 mt-1">
                    {certificate.rejectionReason}
                  </p>
                )}
                {certificate.rejectedAt && (
                  <div className="flex flex-col gap-1 mt-2">
                    <p className="text-xs text-neutral-500">
                      {formatDistanceToNow(new Date(certificate.rejectedAt), {
                        addSuffix: true,
                        locale: fr,
                      })}
                    </p>
                    {certificate.rejectedBy && (
                      <p className="text-xs text-neutral-500">
                        par {certificate.rejectedByName ?? "Agent"}
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
