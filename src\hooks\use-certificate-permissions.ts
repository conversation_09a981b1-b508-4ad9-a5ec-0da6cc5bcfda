import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { Certificate } from "@/actions/types";
import { User } from "@/types/auth";

interface CertificatePermissions {
  canDownload: boolean;
  canPerformActions: boolean;
  canReject: boolean;
  canAssign: boolean;
  canUnassign: boolean;
  canVerify: boolean;
  canSign: boolean;
  canDeliver: boolean;
  canMarkAsReady: boolean;
}

export function useCertificatePermissions(
  certificate: Certificate,
  userRole: "chef" | "agent" | "citizen" | "admin",
  user: User | null,
  isSignPage = false
): CertificatePermissions {
  // Vérification de base pour l'utilisateur connecté
  if (!user) {
    return {
      canDownload: false,
      canPerformActions: false,
      canReject: false,
      canAssign: false,
      canUnassign: false,
      canVerify: false,
      canSign: false,
      canDeliver: false,
      canMarkAsReady: false,
    };
  }

  // Admin a tous les droits
  if (userRole === "admin") {
    return {
      canDownload: certificate.status === CERTIFICATE_STATUS.DELIVERED,
      canPerformActions: true,
      canReject: !isSignPage,
      canAssign: true,
      canUnassign: Boolean(certificate.agentId),
      canVerify: true,
      canSign: certificate.status === CERTIFICATE_STATUS.READY,
      canDeliver: certificate.status === CERTIFICATE_STATUS.SIGNED,
      canMarkAsReady: certificate.status === CERTIFICATE_STATUS.VERIFIED,
    };
  }

  // Téléchargement disponible pour tous au statut DELIVERED
  const canDownload = Boolean(
    certificate.status === CERTIFICATE_STATUS.DELIVERED &&
      Boolean(certificate.documentUrl) &&
      !certificate.downloads?.includes(user.$id)
  );

  // Permissions spécifiques pour le chef
  const isChefCertificate = certificate.chefId === user.$id;
  const chefPermissions = userRole === "chef" &&
    isChefCertificate && {
      canPerformActions: certificate.status !== CERTIFICATE_STATUS.DELIVERED,
      canReject:
        !isSignPage &&
        [
          CERTIFICATE_STATUS.SUBMITTED,
          CERTIFICATE_STATUS.PENDING,
          CERTIFICATE_STATUS.VERIFIED,
          CERTIFICATE_STATUS.APPROVED, // car un agentpeut approuver mais le chef le rejette
        ].includes(certificate.status as CERTIFICATE_STATUS),
      canAssign: [
        CERTIFICATE_STATUS.SUBMITTED,
        CERTIFICATE_STATUS.PENDING,
      ].includes(certificate.status as CERTIFICATE_STATUS),
      canUnassign:
        [CERTIFICATE_STATUS.SUBMITTED, CERTIFICATE_STATUS.PENDING].includes(
          certificate.status as CERTIFICATE_STATUS
        ) && Boolean(certificate.agentId),
      canVerify: true,
      canSign: certificate.status === CERTIFICATE_STATUS.READY,
      canDeliver: certificate.status === CERTIFICATE_STATUS.SIGNED,
      canMarkAsReady: [
        CERTIFICATE_STATUS.APPROVED,
        CERTIFICATE_STATUS.VERIFIED,
      ].includes(certificate.status as CERTIFICATE_STATUS),
    };

  // Permissions spécifiques pour l'agent
  const isAgentCertificate = certificate.agentId === user.$id;
  const agentPermissions = userRole === "agent" &&
    isAgentCertificate && {
      canPerformActions: ![
        CERTIFICATE_STATUS.SIGNED,
        CERTIFICATE_STATUS.DELIVERED,
      ].includes(certificate.status as CERTIFICATE_STATUS),
      canReject:
        !isSignPage &&
        [CERTIFICATE_STATUS.PENDING, CERTIFICATE_STATUS.VERIFIED].includes(
          certificate.status as CERTIFICATE_STATUS
        ),
      canAssign: false,
      canUnassign: false,
      canVerify: [
        CERTIFICATE_STATUS.SUBMITTED,
        CERTIFICATE_STATUS.PENDING,
      ].includes(certificate.status as CERTIFICATE_STATUS),
      canSign: false,
      canDeliver: false,
      canMarkAsReady: certificate.status === CERTIFICATE_STATUS.VERIFIED,
    };

  // Permissions spécifiques pour le citoyen
  const isCitizenCertificate = certificate.citizenId === user.$id;
  const citizenPermissions = userRole === "citizen" &&
    isCitizenCertificate && {
      canPerformActions: [
        CERTIFICATE_STATUS.DRAFT,
        CERTIFICATE_STATUS.DELIVERED,
      ].includes(certificate.status as CERTIFICATE_STATUS),
      canReject: false,
      canAssign: false,
      canUnassign: false,
      canVerify: false,
      canSign: false,
      canDeliver: false,
      canMarkAsReady: false,
    };

  // Combine les permissions selon le rôle
  const rolePermissions = {
    ...(chefPermissions || {}),
    ...(agentPermissions || {}),
    ...(citizenPermissions || {}),
  } as CertificatePermissions;

  return {
    canDownload,
    canPerformActions: rolePermissions.canPerformActions ?? false,
    canReject: rolePermissions.canReject ?? false,
    canAssign: rolePermissions.canAssign ?? false,
    canUnassign: rolePermissions.canUnassign ?? false,
    canVerify: rolePermissions.canVerify ?? false,
    canSign: rolePermissions.canSign ?? false,
    canDeliver: rolePermissions.canDeliver ?? false,
    canMarkAsReady: rolePermissions.canMarkAsReady ?? false,
  };
}
