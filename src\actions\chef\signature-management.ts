"use server";

import { getCurrentUser } from "@/actions/auth/session";
import { createAdminClient } from "@/lib/server/appwrite";
import { BUCKETS } from "@/lib/server/constant";
import {
  CHEF_SIGNATURES_COLLECTION_ID,
  DATABASE_ID,
} from "@/lib/server/database";
import { ID, Query } from "node-appwrite";

export interface SaveSignatureParams {
  signatureData: string;
  signatureType: "digital" | "handwritten";
}

export interface SignatureActionResult {
  success: boolean;
  signature?: any;
  error?: string;
}

/**
 * Sauvegarder ou mettre à jour la signature d'un chef
 */
export async function saveChefSignatureAction(
  params: SaveSignatureParams
): Promise<SignatureActionResult> {
  try {
    const { user } = await getCurrentUser();

    // Vérifier que l'utilisateur est chef
    if (!user || user.prefs?.role !== "chef") {
      return {
        success: false,
        error:
          "Accès non autorisé. Seuls les chefs peuvent gérer leurs signatures.",
      };
    }

    // Valider les données de signature
    if (!params.signatureData || params.signatureData.trim() === "") {
      return {
        success: false,
        error: "Les données de signature sont requises.",
      };
    }

    if (!["digital", "handwritten"].includes(params.signatureType)) {
      return {
        success: false,
        error: "Type de signature invalide.",
      };
    }

    const { databases, storage } = await createAdminClient();
    const now = new Date().toISOString();

    // Convertir le base64 en Buffer pour le stockage
    const base64Data = params.signatureData.replace(
      /^data:image\/\w+;base64,/,
      ""
    );
    const buffer = Buffer.from(base64Data, "base64");

    // Vérifier si une signature existe déjà
    const existingSignatures = await databases.listDocuments(
      DATABASE_ID,
      CHEF_SIGNATURES_COLLECTION_ID,
      [
        Query.equal("chefId", user.$id),
        Query.equal("isActive", "true"),
        Query.limit(1),
      ]
    );

    let signatureRecord;

    if (existingSignatures.documents.length > 0) {
      // Mettre à jour la signature existante
      const existingSignature = existingSignatures.documents[0];

      // Supprimer l'ancien fichier de signature
      try {
        await storage.deleteFile(
          BUCKETS.SIGNATURES,
          existingSignature.signatureFileId
        );
      } catch (error) {
        console.warn(
          "Impossible de supprimer l'ancien fichier de signature:",
          error
        );
      }

      // Créer un nouveau fichier de signature
      const signatureFile = await storage.createFile(
        BUCKETS.SIGNATURES,
        ID.unique(),
        new File([new Uint8Array(buffer)], `chef-signature-${user.$id}.png`, {
          type: "image/png",
        })
      );

      // Mettre à jour l'enregistrement
      signatureRecord = await databases.updateDocument(
        DATABASE_ID,
        CHEF_SIGNATURES_COLLECTION_ID,
        existingSignature.$id,
        {
          signatureFileId: signatureFile.$id,
          signatureType: params.signatureType,
          updatedAt: now,
          isActive: "true",
        }
      );
    } else {
      // Créer une nouvelle signature
      const signatureFile = await storage.createFile(
        BUCKETS.SIGNATURES,
        ID.unique(),
        new File([new Uint8Array(buffer)], `chef-signature-${user.$id}.png`, {
          type: "image/png",
        })
      );

      signatureRecord = await databases.createDocument(
        DATABASE_ID,
        CHEF_SIGNATURES_COLLECTION_ID,
        ID.unique(),
        {
          chefId: user.$id,
          signatureFileId: signatureFile.$id,
          signatureType: params.signatureType,
          isActive: "true",
          createdAt: now,
          updatedAt: now,
          usageCount: "0",
        }
      );
    }

    return {
      success: true,
      signature: {
        $id: signatureRecord.$id,
        signatureType: signatureRecord.signatureType,
        createdAt: signatureRecord.createdAt,
        updatedAt: signatureRecord.updatedAt,
        usageCount: parseInt(signatureRecord.usageCount || "0"),
      },
    };
  } catch (error: any) {
    console.error("Erreur lors de la sauvegarde de la signature:", error);
    return {
      success: false,
      error: error?.message || "Erreur lors de la sauvegarde de la signature",
    };
  }
}
