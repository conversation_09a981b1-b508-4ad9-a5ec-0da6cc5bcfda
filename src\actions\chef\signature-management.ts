"use server";

import { getCurrentUser } from "@/actions/auth/session";
import { createAdminClient } from "@/lib/server/appwrite";
import { BUCKETS } from "@/lib/server/constant";
import {
  CHEF_SIGNATURES_COLLECTION_ID,
  DATABASE_ID,
} from "@/lib/server/database";
import { ID, Query } from "node-appwrite";

export interface SaveSignatureParams {
  signatureData: string;
  signatureType: "digital" | "handwritten";
}

export interface SignatureActionResult {
  success: boolean;
  signature?: any;
  error?: string;
}

/**
 * Sauvegarder ou mettre à jour la signature d'un chef
 */
export async function saveChefSignatureAction(
  params: SaveSignatureParams
): Promise<SignatureActionResult> {
  try {
    const { user } = await getCurrentUser();

    // Vérifier que l'utilisateur est chef
    if (!user || user.prefs?.role !== "chef") {
      return {
        success: false,
        error:
          "Accès non autorisé. Seuls les chefs peuvent gérer leurs signatures.",
      };
    }

    // Valider les données de signature
    if (!params.signatureData || params.signatureData.trim() === "") {
      return {
        success: false,
        error: "Les données de signature sont requises.",
      };
    }

    if (!["digital", "handwritten"].includes(params.signatureType)) {
      return {
        success: false,
        error: "Type de signature invalide.",
      };
    }

    const { databases, storage } = await createAdminClient();
    const now = new Date().toISOString();

    // Convertir le base64 en Buffer pour le stockage
    const base64Data = params.signatureData.replace(
      /^data:image\/\w+;base64,/,
      ""
    );
    const buffer = Buffer.from(base64Data, "base64");

    // Vérifier si une signature existe déjà
    const existingSignatures = await databases.listDocuments(
      DATABASE_ID,
      CHEF_SIGNATURES_COLLECTION_ID,
      [
        Query.equal("chefId", user.$id),
        Query.equal("isActive", "true"),
        Query.limit(1),
      ]
    );

    let signatureRecord;

    if (existingSignatures.documents.length > 0) {
      // Mettre à jour la signature existante
      const existingSignature = existingSignatures.documents[0];

      // Supprimer l'ancien fichier de signature
      try {
        await storage.deleteFile(
          BUCKETS.SIGNATURES,
          existingSignature.signatureFileId
        );
      } catch (error) {
        console.warn(
          "Impossible de supprimer l'ancien fichier de signature:",
          error
        );
      }

      // Créer un nouveau fichier de signature
      const signatureFile = await storage.createFile(
        BUCKETS.SIGNATURES,
        ID.unique(),
        new File([new Uint8Array(buffer)], `chef-signature-${user.$id}.png`, {
          type: "image/png",
        })
      );

      // Mettre à jour l'enregistrement
      signatureRecord = await databases.updateDocument(
        DATABASE_ID,
        CHEF_SIGNATURES_COLLECTION_ID,
        existingSignature.$id,
        {
          signatureFileId: signatureFile.$id,
          signatureType: params.signatureType,
          updatedAt: now,
          isActive: "true",
        }
      );
    } else {
      // Créer une nouvelle signature
      const signatureFile = await storage.createFile(
        BUCKETS.SIGNATURES,
        ID.unique(),
        new File([new Uint8Array(buffer)], `chef-signature-${user.$id}.png`, {
          type: "image/png",
        })
      );

      signatureRecord = await databases.createDocument(
        DATABASE_ID,
        CHEF_SIGNATURES_COLLECTION_ID,
        ID.unique(),
        {
          chefId: user.$id,
          signatureFileId: signatureFile.$id,
          signatureType: params.signatureType,
          isActive: "true",
          createdAt: now,
          updatedAt: now,
          usageCount: "0",
        }
      );
    }

    return {
      success: true,
      signature: {
        $id: signatureRecord.$id,
        signatureType: signatureRecord.signatureType,
        createdAt: signatureRecord.createdAt,
        updatedAt: signatureRecord.updatedAt,
        usageCount: parseInt(signatureRecord.usageCount || "0"),
      },
    };
  } catch (error: any) {
    console.error("Erreur lors de la sauvegarde de la signature:", error);
    return {
      success: false,
      error: error?.message || "Erreur lors de la sauvegarde de la signature",
    };
  }
}

/**
 * Récupérer la signature active d'un chef
 */
export async function getChefSignatureAction(): Promise<SignatureActionResult> {
  try {
    const { user } = await getCurrentUser();

    // Vérifier que l'utilisateur est chef
    if (!user || user.prefs?.role !== "chef") {
      return {
        success: false,
        error:
          "Accès non autorisé. Seuls les chefs peuvent accéder à leurs signatures.",
      };
    }

    const { databases, storage } = await createAdminClient();

    // Récupérer la signature active
    const signatures = await databases.listDocuments(
      DATABASE_ID,
      CHEF_SIGNATURES_COLLECTION_ID,
      [
        Query.equal("chefId", user.$id),
        Query.equal("isActive", "true"),
        Query.limit(1),
      ]
    );

    if (signatures.documents.length === 0) {
      return {
        success: true,
        signature: null,
      };
    }

    const signatureDoc = signatures.documents[0];

    // Récupérer l'URL du fichier de signature
    let signatureData = "";
    try {
      const signatureFile = await storage.getFileDownload(
        BUCKETS.SIGNATURES,
        signatureDoc.signatureFileId
      );
      const buffer = Buffer.from(signatureFile);
      signatureData = `data:image/png;base64,${buffer.toString("base64")}`;
    } catch (error) {
      console.error(
        "Erreur lors de la récupération du fichier de signature:",
        error
      );
    }

    return {
      success: true,
      signature: {
        $id: signatureDoc.$id,
        signatureData: signatureData,
        signatureType: signatureDoc.signatureType,
        createdAt: signatureDoc.createdAt,
        updatedAt: signatureDoc.updatedAt,
        lastUsedAt: signatureDoc.lastUsedAt,
        usageCount: parseInt(signatureDoc.usageCount || "0"),
      },
    };
  } catch (error: any) {
    console.error("Erreur lors de la récupération de la signature:", error);
    return {
      success: false,
      error: error?.message || "Erreur lors de la récupération de la signature",
    };
  }
}

/**
 * Vérifier si un chef a une signature active
 */
export async function hasActiveSignatureAction(): Promise<{
  success: boolean;
  hasSignature?: boolean;
  error?: string;
}> {
  try {
    const { user } = await getCurrentUser();

    // Vérifier que l'utilisateur est chef
    if (!user || user.prefs?.role !== "chef") {
      return {
        success: false,
        error: "Accès non autorisé.",
      };
    }

    const { databases } = await createAdminClient();

    const signatures = await databases.listDocuments(
      DATABASE_ID,
      CHEF_SIGNATURES_COLLECTION_ID,
      [
        Query.equal("chefId", user.$id),
        Query.equal("isActive", "true"),
        Query.limit(1),
      ]
    );

    return {
      success: true,
      hasSignature: signatures.documents.length > 0,
    };
  } catch (error: any) {
    console.error("Erreur lors de la vérification de la signature:", error);
    return {
      success: false,
      error: error?.message || "Erreur lors de la vérification de la signature",
    };
  }
}

/**
 * Désactiver la signature d'un chef
 */
export async function deactivateChefSignatureAction(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const { user } = await getCurrentUser();

    // Vérifier que l'utilisateur est chef
    if (!user || user.prefs?.role !== "chef") {
      return {
        success: false,
        error: "Accès non autorisé.",
      };
    }

    const { databases } = await createAdminClient();

    // Récupérer la signature active
    const signatures = await databases.listDocuments(
      DATABASE_ID,
      CHEF_SIGNATURES_COLLECTION_ID,
      [
        Query.equal("chefId", user.$id),
        Query.equal("isActive", "true"),
        Query.limit(1),
      ]
    );

    if (signatures.documents.length === 0) {
      return {
        success: false,
        error: "Aucune signature active trouvée.",
      };
    }

    const signatureDoc = signatures.documents[0];
    const now = new Date().toISOString();

    // Désactiver la signature
    await databases.updateDocument(
      DATABASE_ID,
      CHEF_SIGNATURES_COLLECTION_ID,
      signatureDoc.$id,
      {
        isActive: "false",
        updatedAt: now,
      }
    );

    return {
      success: true,
    };
  } catch (error: any) {
    console.error("Erreur lors de la désactivation de la signature:", error);
    return {
      success: false,
      error:
        error?.message || "Erreur lors de la désactivation de la signature",
    };
  }
}
