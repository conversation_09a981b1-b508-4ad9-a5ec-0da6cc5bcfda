"use server";

import { getCurrentUser } from "@/actions/auth/session";
import { chefSignatureService, CreateSignatureParams } from "@/services/chef/signature-management";

export interface SaveSignatureParams {
  signatureData: string;
  signatureType: "digital" | "handwritten";
}

export interface SignatureActionResult {
  success: boolean;
  signature?: any;
  error?: string;
}

/**
 * Sauvegarder ou mettre à jour la signature d'un chef
 */
export async function saveChefSignatureAction(
  params: SaveSignatureParams
): Promise<SignatureActionResult> {
  try {
    const { user } = await getCurrentUser();

    // Vérifier que l'utilisateur est chef
    if (!user || user.prefs?.role !== "chef") {
      return {
        success: false,
        error: "Accès non autorisé. Seuls les chefs peuvent gérer leurs signatures.",
      };
    }

    // Valider les données de signature
    if (!params.signatureData || params.signatureData.trim() === "") {
      return {
        success: false,
        error: "Les données de signature sont requises.",
      };
    }

    if (!["digital", "handwritten"].includes(params.signatureType)) {
      return {
        success: false,
        error: "Type de signature invalide.",
      };
    }

    // Sauvegarder la signature
    const signature = await chefSignatureService.upsertChefSignature({
      chefId: user.$id,
      signatureData: params.signatureData,
      signatureType: params.signatureType,
    });

    return {
      success: true,
      signature: {
        $id: signature.$id,
        signatureType: signature.signatureType,
        createdAt: signature.createdAt,
        updatedAt: signature.updatedAt,
        usageCount: signature.usageCount,
      },
    };
  } catch (error: any) {
    console.error("Erreur lors de la sauvegarde de la signature:", error);
    return {
      success: false,
      error: error?.message || "Erreur lors de la sauvegarde de la signature",
    };
  }
}

/**
 * Récupérer la signature active d'un chef
 */
export async function getChefSignatureAction(): Promise<SignatureActionResult> {
  try {
    const { user } = await getCurrentUser();

    // Vérifier que l'utilisateur est chef
    if (!user || user.prefs?.role !== "chef") {
      return {
        success: false,
        error: "Accès non autorisé. Seuls les chefs peuvent accéder à leurs signatures.",
      };
    }

    const signature = await chefSignatureService.getChefSignature(user.$id);

    if (!signature) {
      return {
        success: true,
        signature: null,
      };
    }

    return {
      success: true,
      signature: {
        $id: signature.$id,
        signatureData: signature.signatureData,
        signatureType: signature.signatureType,
        createdAt: signature.createdAt,
        updatedAt: signature.updatedAt,
        lastUsedAt: signature.lastUsedAt,
        usageCount: signature.usageCount,
      },
    };
  } catch (error: any) {
    console.error("Erreur lors de la récupération de la signature:", error);
    return {
      success: false,
      error: error?.message || "Erreur lors de la récupération de la signature",
    };
  }
}

/**
 * Vérifier si un chef a une signature active
 */
export async function hasActiveSignatureAction(): Promise<{
  success: boolean;
  hasSignature?: boolean;
  error?: string;
}> {
  try {
    const { user } = await getCurrentUser();

    // Vérifier que l'utilisateur est chef
    if (!user || user.prefs?.role !== "chef") {
      return {
        success: false,
        error: "Accès non autorisé.",
      };
    }

    const hasSignature = await chefSignatureService.hasActiveSignature(user.$id);

    return {
      success: true,
      hasSignature,
    };
  } catch (error: any) {
    console.error("Erreur lors de la vérification de la signature:", error);
    return {
      success: false,
      error: error?.message || "Erreur lors de la vérification de la signature",
    };
  }
}

/**
 * Désactiver la signature d'un chef
 */
export async function deactivateChefSignatureAction(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const { user } = await getCurrentUser();

    // Vérifier que l'utilisateur est chef
    if (!user || user.prefs?.role !== "chef") {
      return {
        success: false,
        error: "Accès non autorisé.",
      };
    }

    const deactivated = await chefSignatureService.deactivateChefSignature(user.$id);

    if (!deactivated) {
      return {
        success: false,
        error: "Aucune signature active trouvée.",
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error("Erreur lors de la désactivation de la signature:", error);
    return {
      success: false,
      error: error?.message || "Erreur lors de la désactivation de la signature",
    };
  }
}
